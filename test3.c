#include <sys/ioctl.h>
#include <mtd/mtd-user.h>
#include <fcntl.h>
#include <stdio.h>
#include <unistd.h>

int get_mtd_info(const char *device) {
    int fd;
    struct mtd_info_user mtd_info;
    
    fd = open(device, O_RDONLY);
    if (fd < 0) {
        perror("open");
        return -1;
    }
    
    if (ioctl(fd, MEMGETINFO, &mtd_info) < 0) {
        perror("ioctl");
        close(fd);
        return -1;
    }
    
    printf("MTD Device: %s\n", device);
    printf("Type: %d\n", mtd_info.type);
    printf("Flags: 0x%x\n", mtd_info.flags);
    printf("Size: %u bytes\n", mtd_info.size);
    printf("Erase size: %u bytes\n", mtd_info.erasesize);
    printf("Write size: %u bytes\n", mtd_info.writesize);
    printf("OOB size: %u bytes\n", mtd_info.oobsize);
    
    close(fd);
    return 0;
}

int main() {
    get_mtd_info("/dev/mtd7");
    return 0;
}