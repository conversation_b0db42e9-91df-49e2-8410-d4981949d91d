#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <strings.h>
#include <termios.h>
#include <unistd.h>
#include "ublox.h"
#include "gps.h"

int checksum(char * msg) {

    char * p = msg;

    char sum = 0;

    while (*p && (*p != '*')) {
        printf("%c\n", *p);
        sum ^= *p;
        p++;
    }

    return sum;
}

void calculate_checksum(uint8_t *payload, int length, uint8_t *ck_a, uint8_t *ck_b) {
    *ck_a = 0;
    *ck_b = 0;
    for (int i = 0; i < length; i++) {
        *ck_a += payload[i];
        *ck_b += *ck_a;
    }

}

int write_gps_data(int fd, void *buf, size_t nBytes)
{
    int ret;

    ret = write(fd, buf, nBytes);
    if (ret < 0) {
        perror("Failed to write to GPS module");
        close(fd);
        return -1;
    }

    // 阻塞，直到数据全部发送完毕
    tcdrain(fd);

    return 0;
}

void send_ubx_message(int fd, UBXmessage *msg) {
    uint8_t ck_a, ck_b;
    uint16_t length = (uint16_t)(msg->length[1] << 8 | msg->length[0]);
    uint8_t *message = (uint8_t *)malloc(length + 8);
    message[0] = msg->sync_char1;
    message[1] = msg->sync_char2;
    message[2] = msg->class_id;
    message[3] = msg->id;
    message[4] = msg->length[0];
    message[5] = msg->length[1];
    memcpy(&message[6], msg->playload, length);
    if(msg->playload != NULL) {
        calculate_checksum(&message[2], length + 4, &ck_a, &ck_b);
        msg->ck_a = ck_a;
        msg->ck_b = ck_b;
    }
    message[length + 6] = ck_a;
    message[length + 7] = ck_b;
    write_gps_data(fd, message, length + 8);
    free(message);
}

int main(int argc, char *argv[]) {
    int fd;
    UBXmessage msg_ver;
    msg_ver.sync_char1 = UBX_SYNC1;
    msg_ver.sync_char2 = UBX_SYNC2;
    msg_ver.class_id = UBX_CLASSID_MON;
    msg_ver.id = UBX_MON_VER;
    msg_ver.length[0] = 0x00;
    msg_ver.length[1] = 0x00;
    msg_ver.playload = NULL;

    if(set_gps_power(1) < 0)
    {
        printf("gps power on failed\n");
        return -1;
    }

    fd = open_port(GPS_PATH);
    if (fd < 0) {
        printf("open port failed\n");
        return -1;
    }

    if(set_opt(fd, 115200, 8, 1, 'n') < 0)
    {
        printf("set opt failed\n");
        close(fd);
        return -1;
    }

    send_ubx_message(fd, &msg_ver);

    return 0;
}


// int main(){
//     uint8_t payload[] = {
//         0x06, 0x04, // Class和ID
//         0x04, 0x00, // Payload Length（1字节）
//         0x00, 0x00,
//         0x08,
//         0x00,
//     };


//     uint8_t ck_a = 0, ck_b = 0;
//     calculate_checksum(payload, sizeof(payload), &ck_a, &ck_b);

//     printf("CK_A: 0x%02X\n", ck_a);
//     printf("CK_B: 0x%02X\n", ck_b);

//     return 0;
// }

// int main() {
//     int ret;
//     char msg[] = "PUBX,40,GLL,1,0,0,0,0,0";
//     char NMEA[32];
//     sprintf(NMEA, "$%s*%X", msg, checksum(msg));
//     printf("Checksum: %s\n", NMEA);
//     return 0;
// }