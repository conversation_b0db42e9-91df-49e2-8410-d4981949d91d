/*********************************************************************
 * \file timeslot.h
 * @Author: liming
 * @Date:   2025-02-19 10:37:21
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 16:39:00
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 *                                                              
 *********************************************************************/
#include <linux/ioctl.h>
#include "asm-generic/int-ll64.h"
 
#define TIMESLOT_CMD_MAGIC 'T'
#define SIG_SLOT      (40)
#define SIG_SUB_SLOT1 (SIG_SLOT + 1)
#define SIG_SUB_SLOT2 (SIG_SLOT + 2)
#define SIG_SUB_SLOT3 (SIG_SLOT + 3)
#define SIG_SUB_SLOT4 (SIG_SLOT + 4)
#define SIG_SUB_SLOT5 (SIG_SLOT + 5)
#define SIG_SUB_SLOT6 (SIG_SLOT + 6)
#define SIG_SUB_SLOT7 (SIG_SLOT + 7)
 
#define TIMESLOT_SUPPORT_TIMER_MAX (SIG_SUB_SLOT7 - SIG_SLOT + 1)
#define TIMESLOT_SUB_SLOT_MIN_PEROID 500   //TBD
 
 typedef enum _te_slot_type
 {
     SLOT_MAIN = 0,
     SLOT_SUB_1,
     SLOT_SUB_2,
     SLOT_SUB_3,
     SLOT_SUB_4,
     SLOT_SUB_5,
     SLOT_SUB_6,
     SLOT_SUB_7,
     SLOT_MAX
 }_te_slot_type;

typedef void (*ts_process_handle)(int signo);  

 typedef struct _ts_timeslot_type
 {
     unsigned int slot_id; /* 0 - FIXED FOR TIMESLOT,[1,TIMESLOT_SUPPORT_SUB_OPS_MAX) FOR SUBTIMER*/
     unsigned int period_us; /* [0, TIMESLOT_SUB_SLOT_MIN_PEROID)  0 - disable  */
 }ts_timeslot_type;
 
#define TIMESLOT_CMD_TIMESLOT_INFO                     _IO(TIMESLOT_CMD_MAGIC, 0)
#define TIMESLOT_CMD_ENABLE                            _IOW(TIMESLOT_CMD_MAGIC,1, ts_timeslot_type )
#define TIMESLOT_CMD_REG                               _IOW(TIMESLOT_CMD_MAGIC,2, int )
#define TIMESLOT_CMD_SET_TS_PROCESS                    _IOW(TIMESLOT_CMD_MAGIC,3, ts_process_handle ) 
 /*end of the file: timeslot.h*/
  