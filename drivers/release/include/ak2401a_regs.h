/*********************************************************************
 * \file ak2401a_regs.h
 * @Author: liming
 * @Date:   2025-02-19 10:37:21
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 16:37:13
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 *                                                              
 *********************************************************************/

#ifndef _AK2401A_REGS_H_
#define _AK2401A_REGS_H_
#define AK2401A_REGS_NUM    0x60
 typedef struct _ts_ak2401a_regs_info
 {
     unsigned int frac;             /* 0x01 - 0x03 */
     unsigned int mod;              /* 0x04 - 0x06*/
     unsigned short integer;          /* 0x07,0x08*/
     unsigned char rdiv;            /* 0x09 */
 
     unsigned char cpof;            /* 0x0a D6 D5*/          
     unsigned char cpfine;   /* 0x0a */ 
     unsigned char cpfast; /*0x0B*/
 
     /*0x0c*/
     unsigned char dummy1; /* This register must be in the default setting:0 */
     unsigned char fasten; /* fasten 0 - fast lock mode disable ,1 - fast lock mode enable*/
     unsigned char cphiz;  /* Charge pump output of the PLL synthesizer setting. Normally, CPHIZ bit should be set to “0”.0: Normal Output 1: Tri-State*/
     unsigned char dsmon; /*Delta-sigma Modulator Setting */
     unsigned char ld;/*Lock Detection Function Mode Setting: 0 - digital lock detection output, 1 : operation status of local frequency offset control function*/
 
     /*0x0d,0x0e*/
     unsigned short fast_time;
 
     /*0x0f,0x10,0x11*/
     unsigned int ofset1;
 
     /*0x12,*/
     unsigned char i_lodiv;
     unsigned char i_lobuf;
     unsigned char divsel;
 
     /*0x13*/
     unsigned char daccnt;
     unsigned char dummy2;
     unsigned char txolv;
 
     /*0x14*/
     unsigned char lpmode_dem;
     unsigned char dummy3;
     unsigned char rxlpf_fc;
     unsigned char iq_sel;
     unsigned char ana_path;
     unsigned char main_path;
     unsigned char lpmode_lna;
 
     /*0x15, 0x16*/
     unsigned char pga_gain_i;
     unsigned char pga_gain_q;
 
     /*0x17*/
     unsigned char cal_lnapd;
     unsigned char ofs2reg;
     unsigned char ofscal4;
     unsigned char ofscal3;
     unsigned char ofscal2;
     unsigned char ofscal1;
 
     /*0x18*/
     unsigned char agc_ofs_ave;
     unsigned char chofs_ave;
 
     /*0x19 0x1a 0x1b*/
     unsigned int ofst_i;
 
     /*0x1c,0x1d, 0x1e*/
     unsigned int ofst_q;
 
     /*0x1f*/
     unsigned char agc_tim;
     unsigned char agc_hys;
     unsigned char agc_keep_sel;
     unsigned char agc_keepr;
     unsigned char agc_off;
 
     /*0x20*/
     unsigned char lna_agc_off;
     unsigned char lna_lgmode;
     unsigned char agc_max;
     unsigned char agc_tgt;
 
     /*0x21*/
     unsigned char fb_rdoc;
     unsigned char agckp_mode;
     unsigned char agc_trw;
 
     /*0x22*/
     unsigned char dfil_sr;
     unsigned char dfil_prog;
     unsigned char dfil_clk;
     unsigned char dfil_sel;
 
     /*0x23*/
     unsigned char pfil_sat;
     unsigned char pfil_sift;
 
     /*0x24*/
     unsigned char test9;
     unsigned char test10;
     unsigned char test11;
 
     /*0x25*/
     unsigned char test12;
     unsigned char test13;
     unsigned char test14;
 
     /*0x26*/
     unsigned char rdoc1;
     unsigned char rdoc2;
     unsigned char keep_rdoc;
     unsigned char rdoc3;
     unsigned char rdoc4;
     unsigned char rdoc;
 
     /*0x27*/
     unsigned char rdoc18;
     unsigned char rdoc5;
     unsigned char rdoc6;
     unsigned char rdoc7;
 
     /*0x28*/
     unsigned char rdoc8;
     unsigned char ofst_rsel;
     unsigned char rodc_fm;
     unsigned char rdoc9;
 
     /*0x29 0x2a 0x2b*/
     unsigned int ofset2;
 
     /* 0x2c*/
     unsigned char rssi_low;
     unsigned char rssiavg;
 
     /*0x2d*/
     unsigned char coef_num;
     unsigned char coef_st;
 
     /*0x2e*/
     unsigned char pd_clkbuf_n;
     unsigned char pd_lna_n;
     unsigned char pd_rxr_n;
     unsigned char pd_txr_n;
     unsigned char pd_synth_n;
     unsigned char pd_adc_n;
     unsigned char pd_dac_n;
     unsigned char pd_ref_n;
 
     /*0x2f*/
     unsigned char r_lna_lgmode;
     unsigned char rpga_i;
     
     /*0x30*/
     unsigned char rpga_q;
 
     /*0x31 0x32 0x33*/
     unsigned int r_ofst_i;
 
     /*0x34 0x35 0x36*/
     unsigned int r_ofst_q;
 
     /*0x37*/
     unsigned char tapnum;
 
     /*0x38 0x39*/
     unsigned short r_coef;
 
     /*0x3a*/
     unsigned char rssi;
 
     /*0x3b*/
     unsigned char ofst1h_i;
 
     /*0x3c*/
     unsigned char ofst1h_q;
 
     /*0x3d*/
     unsigned char ofst1l_i;
 
     /*0x3e*/
     unsigned char ofst1l_q;
 
     /*0x3f*/
     unsigned char ld_lockcnt;
 
     /*0x40*/
     unsigned char ld_unlockcnt;
 
     /*0x41*/
     unsigned char dummy4;
 
     /*0x42*/
     unsigned char ph_adj;
 
     /*0x43*/
     unsigned char r_ph_adj;
 
     /*0x44*/
     unsigned char r_rdoc;
     unsigned char rdoc10;
     unsigned char rdoc11;
     unsigned char rdoc12;
     
     /*0x45*/
     unsigned char rdoc13;
 
     /*0x46*/
     unsigned char rdoc14;
     unsigned char rdoc15;
     unsigned char rdoc16;
     unsigned char rdoc17;
 
     /*0x47*/
     unsigned char lna_tgt_h;
 
     /*0x48*/
     unsigned char lna_tgt_l;
 
     /*0x49*/
     unsigned char pre_tstwe;
 
     /*0x4a*/
     unsigned char do_mode;
     unsigned char test6;
     unsigned char test7;
     unsigned char test8;
     unsigned char test1;
     unsigned char dfil_acc;
     unsigned char dfil_clkg;
 
     /*0x4b*/
     unsigned char test2;
     unsigned char test3;
     unsigned char test4;
     unsigned char test5;
     unsigned char lnalg_sts;
     unsigned char agc_sts;
     unsigned char rssi_sts;
     unsigned char test15;
 
     /*0x4c*/
     unsigned char keep_rd_dly;
 
     /*0x4d*/
     unsigned char rdoc19;
     unsigned char rdoc20;
     unsigned char rdoc21;
     unsigned char rdoc22;
     unsigned char rdoc23;
 
     /*0x4e*/
     unsigned char keep_hpf2;
     unsigned char hpf2_fc;
     unsigned char hpf2sel;
 
     /*0x4f*/
     unsigned char keep_hpf2_dly_1;
 
     /*0x50*/
     unsigned char keep_hpf2_dly_2;
 
     /*0x51*/
     unsigned char test16;
 
     /*0x5f*/
     unsigned char srst;
     
 }ts_ak2401a_regs_info;
 
#endif /*end of #ifndef _AK2401A_REGS_H_*/
 
 /*end of the file:ak2401a_regs.h*/
 