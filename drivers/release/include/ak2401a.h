/*********************************************************************
 * \file ak2401a.h
 * @Author: liming
 * @Date:   2025-02-19 10:37:21
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 16:37:41
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 *                                                              
 *********************************************************************/
#ifndef _AK2401A_H_
#define _AK2401A_H_
#include <linux/ioctl.h>
 
#define AK2401A_IOC_MAGIC 'K'
 
 typedef struct _ts_ak2401_reg_type 
 {
    unsigned char addr;
    unsigned char value;
    unsigned char path; /* 0 - spi 1 - mcasp  .MUST.*/
 }ts_ak2401_reg_type;
 
 /* used for init through mcasp*/
 typedef struct _ts_ak2401a_regs 
 {
    unsigned char num;
    unsigned char rst_flag; /*1 - reset, 0 -  poweroff*/
    unsigned char path; /* 0 - spi 1 - mcasp  .MUST.*/
    ts_ak2401_reg_type reg[81];
 }ts_ak2401a_regs;
 
 /* used for setting frequency through mcasp*/
 typedef struct _ts_ak2401a_freq_regs 
 {
    unsigned int freq; /* stuffed by user*/
    unsigned char path; /* 0 - spi 1 - mcasp  .MUST.*/
    ts_ak2401_reg_type reg[8]; /*1 - 8*/
 }ts_ak2401a_freq_regs;
 
 typedef struct _ts_ak2401a_ld_type
 {
    int ld_flag;
    unsigned int freq;
 }ts_ak2401a_ld_type;

typedef enum _te_ak2401_work_mode
{
    AK2401_WORK_MODE_IDLE,
    AK2401_WORK_MODE_TX,
    AK2401_WORK_MODE_RX,
}te_ak2401_work_mode;


typedef struct _ts_ak2401_mode_type
{
    te_ak2401_work_mode work_mode;
    ts_ak2401a_freq_regs freq_info;
}ts_ak2401_mode_type;

#define AK2401A_CMD_PRINT_REGS             _IO(AK2401A_IOC_MAGIC, 0)
#define AK2401A_CMD_SET_FREQ              _IOW(AK2401A_IOC_MAGIC, 1, unsigned int)
#define AK2401A_CMD_WIRTE_REG             _IOW(AK2401A_IOC_MAGIC, 2, ts_ak2401_reg_type)
#define AK2401A_CMD_READ_REG             _IOWR(AK2401A_IOC_MAGIC, 3, ts_ak2401_reg_type)
#define AK2401A_CMD_RST_CTRL             _IOWR(AK2401A_IOC_MAGIC, 4, ts_ak2401a_regs)
#define AK2401A_CMD_RXPDN_CTRL            _IOW(AK2401A_IOC_MAGIC, 5, unsigned int)
#define AK2401A_CMD_TXPDN_CTRL            _IOW(AK2401A_IOC_MAGIC, 6, unsigned int)
#define AK2401A_CMD_AGC_KEEP_CTRL         _IOW(AK2401A_IOC_MAGIC, 7, unsigned int)
#define AK2401A_CMD_LD_CTRL               _IOWR(AK2401A_IOC_MAGIC, 8, ts_ak2401a_ld_type)
#define AK2401A_CMD_RSSI_CTRL             _IOR(AK2401A_IOC_MAGIC, 9, unsigned int)
 
 /* ONLY FOR AK2401 CONNECTED BY MCASP BETWEEN A53 AND AK2401!! */
#define AK2401A_CMD_INIT_REGS            _IOWR(AK2401A_IOC_MAGIC, 10, ts_ak2401a_regs)
#define AK2401A_CMD_FREQ_REGS            _IOWR(AK2401A_IOC_MAGIC, 11, ts_ak2401a_freq_regs)
#define AK2401A_CMD_OFFSET_REGS          _IOWR(AK2401A_IOC_MAGIC, 12, ts_ak2401a_freq_regs)
#define AK2401A_CMD_MODE_SET             _IOWR(AK2401A_IOC_MAGIC, 13, ts_ak2401_mode_type)
 
#endif /*end of#ifndef _AK2401A_H_*/
 
/*end of the file:ak2401a.h*/
