/*********************************************************************
 * \file gc9307.h
 * @Author: liming
 * @Date:   2025-05-22 11:41:27
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 16:37:57
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 *                                                              
 *********************************************************************/
#ifndef GC9307_H
#define GC9307_H

#ifdef __cplusplus
extern "C" {
#endif

/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <linux/delay.h>
#include <linux/backlight.h>
//============================> Libraries Headers <============================

//============================> Project Headers <============================

/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/
/* 屏幕分辨率宏定义 */
#define ipsTft_CNT      1
#define ipsTft_NAME     "gc9307"
#define LCD_W           320
#define LCD_H           240

/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/
typedef enum
{
    LCD_DRV_ROTATE_0 = 0,
    LCD_DRV_ROTATE_90,
    LCD_DRV_ROTATE_180,
    LCD_DRV_ROTATE_270
} lcd_drv_rotation_t;

/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/
struct lvgl_region {
    int32_t x1;
    int32_t x2;
    int32_t y1;
    int32_t y2;
    bool is_dirty;
} __attribute__((packed));

typedef struct {
    struct spi_device *spi;          // 关联的SPI设备
    struct delayed_work delayed_display_work; // 延迟显示工作队列
} lcd_data_t;

struct ipsTft_dev {
    struct backlight_device *bl;     // 背光设备
    void *private_data;              // 私有数据（通常为SPI设备）
    struct cdev cdev;                // 字符设备
    struct class *class;             // 设备类
    struct device *device;           // 设备节点
    struct device_node *nd;          // 设备树节点
    dev_t devid;                     // 设备号
    int major;                       // 主设备号
    int dc_gpio;                     // DC引脚GPIO
    int cs_gpio;                     // CS片选GPIO
    int res_gpio;                    // 复位引脚GPIO
    int led_gpio;                    // 背光控制GPIO
    u32 rotate;                      // 旋转角度
    lcd_drv_rotation_t rotation;     // 屏幕旋转方向
};

/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/

/******************************************************************************

                                Public Functions

 ******************************************************************************/

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
