/*********************************************************************
 * \file sa6100lp_reg.h
 * @Author: liming
 * @Date:   2025-02-19 10:37:21
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 16:38:51
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 *                                                              
 *********************************************************************/
#ifndef SMA6100P_H
#define SMA6100P_H

#ifdef __cplusplus
extern "C" {
#endif

/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================

//============================> Libraries Headers <============================

//============================> Project Headers <============================
#include "drv_math.h"

/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/

/**
 * @brief SMA6100P I2C地址
 * 
 * @note AD0引脚接地时，I2C地址为0x12；接VDD时，I2C地址为0x13
 */
#define SMA6100P_SLAVE_ADDR                     (0x12 << 1)

//============================> Register Map <============================//

#define SMA6100P_REG_CHIP_ID            0x00 // 芯片ID

#define SMA6100P_REG_ACC_X_LSB          0x01 // X轴加速度低字节
#define SMA6100P_REG_ACC_X_MSB          0x02 // X轴加速度高字节
#define SMA6100P_REG_ACC_Y_LSB          0x03 // Y轴加速度低字节
#define SMA6100P_REG_ACC_Y_MSB          0x04 // Y轴加速度高字节
#define SMA6100P_REG_ACC_Z_LSB          0x05 // Z轴加速度低字节
#define SMA6100P_REG_ACC_Z_MSB          0x06 // Z轴加速度高字节

#define SMA6100P_REG_STEP_CNT_LSB       0x07 // 步态计数低字节
#define SMA6100P_REG_STEP_CNT_MSB       0x08 // 步态计数高字节

#define SMA6100P_REG_INT_ST0            0x09 // 中断状态寄存器0
#define SMA6100P_REG_INT_ST1            0x0A // 中断状态寄存器1
#define SMA6100P_REG_INT_ST2            0x0B // 中断状态寄存器2
#define SMA6100P_REG_INT_ST3            0x0C // 中断状态寄存器3
#define SMA6100P_REG_INT_ST4            0x0D // 中断状态寄存器4

#define SMA6100P_REG_FIFO_ST            0x0E // FIFO状态寄存器
#define SMA6100P_REG_FSR                0x0F // 满量程设置寄存器
#define SMA6100P_REG_BW                 0x10 // 带宽设置寄存器
#define SMA6100P_REG_PM                 0x11 // 

#define SMA6100P_REG_STEP_CONF0         0x12 // 步态计数配置寄存器0
#define SMA6100P_REG_STEP_CONF1         0x13 // 步态计数配置寄存器1
#define SMA6100P_REG_STEP_CONF2         0x14 // 步态计数配置寄存器2
#define SMA6100P_REG_STEP_CONF3         0x15 // 步态计数配置寄存器3

#define SMA6100P_REG_INT_EN0            0x16 // 中断使能寄存器0
#define SMA6100P_REG_INT_EN1            0x17 // 中断使能寄存器1
#define SMA6100P_REG_INT_EN2            0x18 // 中断使能寄存器2

#define SMA6100P_REG_INT_MAP0           0x19 // 中断映射寄存器0
#define SMA6100P_REG_INT_MAP1           0x1A // 中断映射寄存器1
#define SMA6100P_REG_INT_MAP2           0x1B // 中断映射寄存器2
#define SMA6100P_REG_INT_MAP3           0x1C // 中断映射寄存器3

#define SMA6100P_REG_STEP_CFG0          0x1D // 步态计数配置寄存器0
#define SMA6100P_REG_STEP_CFG1          0x1E // 步态计数配置寄存器1
#define SMA6100P_REG_STEP_CFG2          0x1F // 步态计数配置寄存器2

#define SMA6100P_REG_INTPIN_CONF        0x20 // 中断引脚配置寄存器
#define SMA6100P_REG_INT_CFG            0x21 // 中断配置寄存器

#define SMA6100P_REG_RAISE_WAKE_CFG0    0x22 // 抬手唤醒配置寄存器
#define SMA6100P_REG_RAISE_WAKE_CFG1    0x23 // 抬手唤醒配置寄存器
#define SMA6100P_REG_RAISE_WAKE_CFG2    0x24 // 抬手唤醒配置寄存器
#define SMA6100P_REG_RAISE_WAKE_CFG3    0x25 // 抬手唤醒配置寄存器
#define SMA6100P_REG_RAISE_WAKE_CFG4    0x26 // 抬手唤醒配置寄存器

#define SMA6100P_REG_OS_CUST_X          0x27 // X轴偏移校准寄存器
#define SMA6100P_REG_OS_CUST_Y          0x28 // Y轴偏移校准寄存器
#define SMA6100P_REG_OS_CUST_Z          0x29 // Z轴偏移校准寄存器

#define SMA6100P_REG_TAP_CFG0           0x2A // 轻击配置寄存器0
#define SMA6100P_REG_TAP_CFG1           0x2B // 轻击配置寄存器1

#define SMA6100P_REG_MOT_CFG0           0x2C // 运动检测配置寄存器0
#define SMA6100P_REG_MOT_CFG1           0x2D // 运动检测配置寄存器1
#define SMA6100P_REG_MOT_CFG2           0x2E // 运动检测配置寄存器2
#define SMA6100P_REG_MOT_CFG3           0x2F // 运动检测配置寄存器3

#define SMA6100P_REG_RST_MOT            0x30 // 运动检测配置寄存器4
#define SMA6100P_REG_FIFO_WM            0x31 // FIFO水位寄存器
#define SMA6100P_REG_ST                 0x32 // 自检寄存器

#define SMA6100P_REG_XYZ_TH_CFG0        0x33 // 
#define SMA6100P_REG_XYZ_TH_CFG1        0x34 // 

#define SMA6100P_REG_SOFT_RESET         0x36 // 软件复位寄存器

#define SMA6100P_REG_FIFO_CFG0          0x3E // FIFO配置寄存器0
#define SMA6100P_REG_FIFO_DATA          0x3F // FIFO数据寄存器

//============================> Register Bits <============================//

/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/

/**
 * @brief SMA6100P采用14位ADC, 可测量的值数量为 2^14 = 16384, 
 * 若满量程为2G, 则每个LSB的值为 4G / 16384 = 4 * 9.81 / 16384 = 244ug。
 */
typedef enum
{
    SMA6100P_RNG_2G = 0x1,  // 244ug/LSB
    SMA6100P_RNG_4G = 0x2,  // 488ug/LSB
    SMA6100P_RNG_8G = 0x4,  // 977ug/LSB
    SMA6100P_RNG_16G = 0x8, // 1.95mg/LSB
    SMA6100P_RNG_32G = 0xf, // 3.91mg/LSB
    // 其余值: 2G
} sma6100p_range_t;

/**
 * @brief SMA6100P集成了一个FIFO内存, 可存储达64帧数据, 每帧数据包含3个14位的字, 
 * 对应X, Y, Z轴的加速度数据。FIFO可配置为以下三种模式: BYPASS, STREAM, FIFO。
 * 
 * 我们可在FIFO_CH中
 */
typedef enum
{
    /**
     * @brief 只能从FIFO中读取最新的数据, BYPASS模式类似于深度为1的STREAM模式。
     * 与直接从数据寄存器中读取数据相比, BYPASS模式的优势在于 XYZ 数据的是从同一时间点采集的。
     * 而直接从数据寄存器中读取数据时, XYZ 数据寄存器有可能是不同时间点采集的。
     */
    SMA6100P_BYPASS = 0x00,

    /**
     * @brief FIFO模式, 缓冲区持续填充直到填满 64 帧。当缓冲区满时，数据采集停止，新数据将被忽略。
     */
    SMA6100P_FIFO = 0x01,

    /**
     * @brief 缓冲区持续填充直到填满 64 帧。当缓冲区满时，新数据将覆盖最旧的数据，最旧的数据被丢弃。
     */
    SMA6100P_STREAM = 0x02,
} sma6100p_fifo_mode_t;

/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/

/**
 * @brief 带宽设置寄存器, 默认值为 0x0E
 */
typedef union
{
    uint8_t raw;
    struct
    {
        uint8_t bw : 5;   // b4-b0: 带宽设置
        uint8_t nlpf : 2; // b6-b5: 无低通滤波器
        uint8_t hpf : 1;  // b7: 高通滤波器
    } bit;
} sma6100p_bw_t;

/**
 * @brief PM寄存器, 默认值为 0x00
 */
typedef union
{
    uint8_t raw;
    struct
    {
        uint8_t mclk_sel : 4;        // b0-b3: 主时钟设置
        uint8_t t_rstb_sinc_sel : 2; // b4-b5: 
        uint8_t : 1;

        /**
         * @brief b7: 活动模式设置,
         * - 0: 将设备设置为Standby模式, 是设备在上电或软复位后的默认模式。
         * - 1: 将设备设置为Active模式, 在此模式下, 设备将开始采集数据。
         */
        uint8_t mode_bit : 1;
    } bit;
} sma6100p_pm_t;

typedef union
{
    uint8_t raw;
    struct
    {
        uint8_t step_sample_cnt : 7; // b6-b0: 步态计数采样设置
        uint8_t step_en : 1;         // b7: 步态计数使能
    } bit;
} sma6100p_step_conf0_t;

typedef union
{
    uint8_t raw;
    struct
    {
        uint8_t step_precision : 7; // b6-b0: 步态计数精度设置
        uint8_t step_clr : 1;       // b7: 步态计数清零
    } bit;
} sma6100p_step_conf1_t;

typedef union
{
    uint8_t raw;
    struct
    {
        uint8_t step_time_low; // b7-b0: 步态计数时间低设置
    } bit;
} sma6100p_step_conf2_t;

typedef union
{
    uint8_t raw;
    struct
    {
        uint8_t step_time_up; // b7-b0: 步态计数时间高设置
    } bit;
} sma6100p_step_conf3_t;

/**
 * @brief FSR寄存器, 默认值为 0x00
 */
typedef union
{
    uint8_t raw;
    struct
    {
        uint8_t range : 4; // b3-b0: 满量程设置, 详细见sma6100p_range_t
        uint8_t : 2;
        uint8_t lpf_hpf : 1; // b5-b4: 低通滤波器/高通滤波器
    } bit;
} sma6100p_fsr_t;

typedef union
{
    uint8_t raw;
    struct
    {
        uint8_t latch_int : 1; // b0: 中断锁存
        uint8_t latch_int_step : 1; // b1:
        uint8_t : 3;
        /**
         * @brief b5: 禁用I2C
         * - 0: I2C使能
         * - 1: I2C禁用, 若使用SPI接口, 请将此位设置为1
         */
        uint8_t dis_i2c : 1;
        /**
         * @brief b6: 禁用中断
         * - 0: 影子功能启用, 读取LSB时, MSB将被锁存。确保在读取过程中数据的完整性。
         *      即使在读取 LSB 期间数据更新, MSB 仍保持不变, 直到读取 MSB。
         * - 1: 禁用影子功能, LSB 和 MSB 均会立即更新。
         */
        uint8_t shadow_dis : 1;
        uint8_t int_rd_clear : 1; // b7: 
    } bit;
} sma6100p_int_cfg_t;

typedef union
{
    uint8_t raw;
    struct
    {
        uint8_t step_bp_axis : 2; // b1-b0: 步态计数旁路轴设置
        uint8_t selftest_sign : 1; //
        uint8_t : 4;
        /**
         * @brief b7: 自检
         * - 0: 自建关闭
         * - 1: 自检开启, 当自检开启时, 需要等待3ms
         */
        uint8_t selftest : 1;
    } bit;
} sma6100p_st_t;

typedef union
{
    /**
     * @brief FIFO帧计数器, 一个空的 FIFO 对应的计数器值为 0。
     */
    uint8_t fifo_frame_cnt;
} sma6100p_fifo_st_t;

/**
 * @brief FIFO配置寄存器0, 默认值为 0x07
 * 
 * @note 假如 3 个轴都启用, 那么读取 FIFO 数据寄存器时的格式为: 
 *  [X_LSB, X_MSB, Y_LSB, Y_MSB, Z_LSB, Z_MSB]
 * 
 * @note 假如只启用 X 轴, 那么读取 FIFO 数据寄存器时的格式为:
 *  [X_LSB, X_MSB]
 */
typedef union
{
    uint8_t raw;
    struct
    {
        uint8_t fifo_en_x : 1;    // b0: 启用X轴FIFO
        uint8_t fifo_en_y : 1;    // b1: 启用Y轴FIFO
        uint8_t fifo_en_z : 1;    // b2: 启用Z轴FIFO
        uint8_t raise_xyz_sw : 3; // b5-b3: 启用XYZ切换
        uint8_t fifo_mode : 2;    // b7:6 FIFO模式设置, 详细见sma6100p_fifo_mode_t
    } bit;
} sma6100p_fifo_cfg0_t;

/**
 * @brief 中断使能寄存器0, 默认值为 0x00
 */
typedef union
{
    uint8_t raw;
    struct
    {
        uint8_t any_mot_en_x : 1; // b0: X轴任意运动检测使能
        uint8_t any_mot_en_y : 1; // b1: Y轴任意运动检测使能
        uint8_t any_mot_en_z : 1; // b2: Z轴任意运动检测使能
        uint8_t : 2;
        uint8_t no_mot_en_x : 1; // b5: X轴无运动检测使能
        uint8_t no_mot_en_y : 1; // b6: Y轴无运动检测使能
        uint8_t no_mot_en_z : 1; // b7: Z轴无运动检测使能
    } bit;
} sma6100p_int_en2_t;

/**
 * @brief 中断状态寄存器(只读), 默认值为 0x00
 */
typedef union
{
    uint8_t raw;
    struct
    {
        uint8_t any_mot_first_x : 1; // b0: 任意运动中断由X轴触发
        uint8_t any_mot_first_y : 1; // b1: 任意运动中断由Y轴触发
        uint8_t any_mot_first_z : 1; // b2: 任意运动中断由Z轴触发
        uint8_t any_mot_sign : 1;    // b3: 任意运动中断信号正负
        uint8_t : 2;
        uint8_t step_flag : 1; // b6: 是否检测到步态
        uint8_t no_mot : 1;    // b7: 无运动中断是否激活
    } bit;
} sma6100p_int_st0_t;

/**
 * @brief 数据寄存器
 */
typedef union
{
    uint8_t byte[2];
    struct
    {
        uint16_t new_data : 1;
        uint16_t accel_data : 14;
        uint16_t reserved : 1;
    } bit;
} sma6100p_accel_data_t;

typedef struct s_sma6100_output
{
    int16_t x;
    int16_t y;
    int16_t z;
} sma6100p_output_t;

/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/

/******************************************************************************

                                Public Functions

 ******************************************************************************/

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
