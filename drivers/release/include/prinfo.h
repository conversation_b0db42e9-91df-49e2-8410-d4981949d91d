/*********************************************************************
 * \file prinfo.h
 * @Author: liming
 * @Date:   2025-02-19 10:37:21
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 16:38:37
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 *                                                              
 *********************************************************************/

#include <linux/ioctl.h> 

#define PRINFO_CMD_MAGIC 'I'

#define PRODUCT_NAME_LEN    16
#define SN_LEN              128
#define BRAND_LEN           64
#define PRODUCT_BASE_LEN    128

typedef struct _ts_product_info 
{
    char name[PRODUCT_NAME_LEN + 1];
    char sn[SN_LEN + 1];
    char brand[BRAND_LEN + 1];
    char base[PRODUCT_BASE_LEN + 1];
}ts_product_info;

typedef enum _te_band_type
{
    VHF_150M = 0,
    VHF_230M,
    UHF_350M,
    UHF_400M,
    UHF_450M,
    BAND_MAX
}te_band_type;


typedef enum _te_terminal_type
{
    TE_PORTABLE = 0,
    TE_MOBILE,
    TE_MODEM,
    TE_BASE_STAION,
    TE_TYPE_MAX
}te_terminal_type;

typedef struct _ts_radio_info 
{
    te_band_type band;
    te_terminal_type type;
    unsigned int max_freq;
    unsigned int min_freq;
    unsigned int channel_space;

}ts_radio_info;

#define PRINFO_CMD_PRINT                           _IO(PRINFO_CMD_MAGIC, 0)
#define PRINFO_CMD_RADIO_INFO_READ                 _IOR(PRINFO_CMD_MAGIC,1, ts_radio_info)
#define PRINFO_CMD_PRODUCT_INFO_READ               _IOR(PRINFO_CMD_MAGIC,2, ts_product_info)
#define PRINFO_CMD_RADIO_INFO_WRITE                _IOW(PRINFO_CMD_MAGIC,3, ts_radio_info)
#define PRINFO_CMD_PRODUCT_INFO_WRITE              _IOW(PRINFO_CMD_MAGIC,4, ts_product_info)


/*end of the file:prinfo.h*/
