/*********************************************************************
 * \file jl7016g.h
 * @Author: liming
 * @Date:   2025-05-29 10:02:29
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 16:38:32
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 *                                                              
 *********************************************************************/

#ifndef _JL7016G_H_
#define _JL7016G_H_
struct device;
struct regmap_config;

/*
IIC主机直接发送2字节数据：0x08 0x01，开始测试DAC播放固定的语音
IIC主机直接发送2字节数据：0x08 0x00，停止测试DAC播放固定的语音

IIC主机直接发送2字节数据：0x09 0x01，开始测试MIC采集到数据直推DAC播放
IIC主机直接发送2字节数据：0x09 0x00，停止测试MIC采集到数据直推DAC播放
*/

#define JL7016G_DAC            (0X08)
#define JL7016G_MIC            (0x09)  

/*JL7016G_DAC*/
#define JL7016G_DAC_EN     BIT(0)

/*JL7016G_MIC*/
#define JL7016G_MIC_TEST_EN       BIT(0) 

#endif /*end of #ifdef _JL7016G_H_*/
/*end of the file:jl7016.h*/
