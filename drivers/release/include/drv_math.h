/*********************************************************************
 * \file drv_math.h
 * @Author: liming
 * @Date:   2025-02-19 10:37:21
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 16:37:55
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 *                                                              
 *********************************************************************/
#ifndef DRV_MATH_H
#define DRV_MATH_H

#ifdef __cplusplus
extern "C" {
#endif

/******************************************************************************
   
                                 Includes     
   
 ******************************************************************************/


/******************************************************************************
   
                                 Defines  
   
 ******************************************************************************/

#define DRV_PI                          3.14159265358979323846f
#define DRV_G                           9.80665f

#define DRV_BIT(pos)                    (1 << (pos))
#define DRV_SET_BIT(x, pos)             ((x) |= (DRV_BIT(pos)))
#define DRV_SET_BITS(x, bm)             ((x) |= (bm))

#define DRV_CLEAR_BIT(x, pos)           ((x) &= ~(DRV_BIT(pos)))
#define DRV_CLEAR_BITS(x, bm)           ((x) &= (~(bm)))

#define DRV_FLIP_BIT(x, pos)            ((x) ^= (DRV_BIT(pos)))
#define DRV_FLIP_BITS(x, bm)            ((x) ^= (bm))

#define DRV_HAS_BIT(y, pos)             ((0u == ((y) & (DRV_BIT(pos))) ? 0u : 1u))
#define DRV_HAS_ANY_BITS(y, bm)         ((0u == ((y) & (bm)) ? 0u : 1u))
#define DRV_HAS_ALL_BITS(y, bm)         ((bm) == ((y) & (bm)) ? 1u : 0u)

#define DRV_SET_LSBITS(len)             (DRV_BIT(len) - 1)
#define DRV_BF_MASK(start, len)         (DRV_SET_LSBITS(len) << (start))
#define DRV_BF_PREP(y, start, len)      (((y) & DRV_SET_LSBITS(len)) << (start))
#define DRV_BF_GET(y, start, len)       (((y) >> (start)) & DRV_SET_LSBITS(len))
#define DRV_BF_SET(x, bf, start, len)   (x = ((x) & ~DRV_BF_MASK(start, len)) | DRV_BF_PREP(bf, start, len))


/******************************************************************************
   
                                 Enums
   
 ******************************************************************************/

/******************************************************************************
   
                                 Functions
   
 ******************************************************************************/

/**
 * @brief 将任意位宽的有符号数据扩展为16位有符号数据
 * 
 * 该函数接受一个任意位宽的有符号数据和其位宽，通过符号扩展将其转换为16位有符号数据。
 * 符号扩展是通过检查符号位（最高有效位）并根据其值扩展或清零高位实现的。
 * 
 * @param data 原始数据，位宽小于16位
 * @param bit_width 数据的原始位宽，必须小于16
 * 
 * @return int16_t 扩展后的16位有符号数据
 */
static inline int16_t drv_sign_extend_16(uint16_t x, uint8_t bit_width)
{
    int16_t sign_bit = DRV_BIT(bit_width - 1);

    if (x & sign_bit)
    {
        x |= ~(DRV_BIT(bit_width) - 1);
    }
    else
    {
        x &= (DRV_BIT(bit_width) - 1);
    }
    
    return x;
}

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
