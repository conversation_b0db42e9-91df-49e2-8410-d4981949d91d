/*********************************************************************
 * \file rfcontrol.h
 * @Author: liming
 * @Date:   2025-06-11 16:55:30
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 16:38:47
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 *V0001   李明   2025-06-13 1111    创建文件，定义调制数据，功放爬坡滚降参数，以及控制命令   
 *V0002   LIMING 2025-06-22 1058    透传akm相关控制定义                                                          
 *********************************************************************/

#ifndef SRC_RF_H_
#define SRC_RF_H_

#if defined(__KERNEL__)
#include <linux/types.h>
#else 
#include <stdint.h>
#endif 
#include <linux/ioctl.h>

#define RFCONTROL_IOC_MAGIC 'R'

typedef struct _ts_rfcontrol_parameters
{
    int32_t  accept_rfcontrol_signo_pid; /* 接收射频时隙及判决信号进程号 */
    uint32_t timeslot_period; /*unit:us */
    uint32_t timeslot_judge_period; /* 发射判决时间*/
    uint32_t num_of_timeslot_one_frame; /*dmr/pdt 2, tetra 4 */
    uint32_t num_of_frame_one_superframe; /*dmr/pdt 6, tetra 18*/
    uint32_t num_of_superfame_one_mutiframe;/*N/A for dmr/pdt*/
    uint32_t rx_freq;/*待机收频率，由于发射后，进入接收使用*/
    uint32_t tx_freq;/*发射频率由发射数据携带确定，这里仅初始化存储*/
    uint32_t freq_locked_time; /*提前多久开启锁相环锁频，取决于频率锁定时间 */
    uint32_t pll_cp_keep_time;/*cp 大电流持续时间 */
    uint32_t power_amp_ahead_time;/*发射功放提前打开时间, 0 - 默认与标准一致 */
    uint32_t power_amp_delay_time;/*发射功放是否需要延后，0 - 默认与标准一致 */
}ts_rfcontrol_parameters;


typedef struct _ts_tdma_time 
{
    uint32_t symbolNo;     /* symbol number (1...144) for dmr/pdt , 1...255 for tetra*/
    uint32_t tsNo;         /*timeslot number (1..2) for dmr/pdt ，  (1...4) for tetra*/
    uint32_t frameNo;      /* frame number (1...6) for dmr /pdt ，  (1...18) for tetra*/
    uint32_t superframeNo; /*super frame number (1...-) for dmr/pdt (1...60) for tetra*/
    uint32_t hyperframeNo; /*NOT USED FOR DMR/PDT,                  (1...-) for tetra*/
}ts_tdma_time; 

/** 调制数据 */
#define MODULATE_RATE_192K  (192000) 
#define MODULATE_RATE_38P4K (38400)
#define MODULATE_DATA_RATE MODULATE_RATE_38P4K

#define DMR_BURST_SYMN          (144)
#define MODULATE_DATA_SIZE_30MS (int)((MODULATE_DATA_RATE/4800)*DMR_BURST_SYMN)  /*参考 DMR/PDT, 38.4k:38400/4800 * 144 = 1152, 192k: 192000/4800 *144 = 5760 */
#define MODULATE_DATA_SIZE_60MS (int)(MODULATE_DATA_SIZE_30MS*2)


typedef enum _te_rfstate_type
{
    RF_STATE_IDLE = 0,
    RF_STATE_TX_SINGLE_SLOT_NORMAL,
    RF_STATE_TX_SINGLE_SLOT_RX_RC,
    RF_STATE_TX_FULL_SLOT,
    RF_STATE_RX_SINGLE_SLOT_NORMAL,
    RF_STATE_RX_FULL_SLOT_NORMAL,
    RF_STATE_RX_SINGLE_SLOT_TX_RC,
    RF_STATE_RX_PREPARE,/*内部状态跳转使用*/
    RF_STATE_TX_PREPARE,/*内部状态跳转使用*/
    NR_RF_STATES
}te_rfstate_type;

typedef enum _te_rf_work_mode
{
    RF_WORK_MODE_IDLE,
    RF_WORK_MODE_SINGLE_SLOT_TX,
    RF_WORK_MODE_FULL_SLOT_TX,
    RF_WORK_MODE_RC_TX,
    RF_WORK_MODE_SINGLE_SLOT_RX,
    RF_WORK_MODE_SINGLE_SLOT_RX_RC,
    RF_WORK_MODE_FULL_SLOT_RX,
    NR_RF_WORK_MODE
}te_rf_work_mode;

typedef struct _ts_transfer_type
{
    te_rf_work_mode work_mode; /* 指定工作状态*/
    int work_slot_no; /* 工作时隙*/
    uint32_t work_freq;/*工作频率，单位：Hz*/
    uint32_t tx_power_mv; /*仅发射有效：发射功放电压 单位：mv */
    uint32_t nr_samples; /*仅发射有效：双时隙，则MODULATE_DATA_SIZE_60MS，单时隙为 MODULATE_DATA_SIZE_30MS*/
    int symbols[MODULATE_DATA_SIZE_60MS];/*仅发射有效：编码经过RRC成型滤波器数据*/
}ts_transfer_type;


/*功放PA爬坡滚降数据*/
#define PA_RAMP_RATE                 (192000)    /* 192k */
#define PA_RAMP_PERIOD               (1.25)      /* 1.25ms 参考DMR/PDT,1.25MS = 功率爬坡/滚降数据保护比特长度*/
#define PA_RAMP_DATA_NUM             (240)//(int)((PA_RAMP_RATE / 1000) * PA_RAMP_PERIOD)   /* 192k 1.25MS 采样数=240 Samples */

typedef struct _ts_pa_data_type
{
    uint32_t power_voltage_mv;
    uint32_t ramp_up_data[PA_RAMP_DATA_NUM];
    uint32_t ramp_down_data[PA_RAMP_DATA_NUM];
}ts_pa_data_type;

/*控制AKM 相关定义*/
typedef enum _te_akm_param_type
{
    AKM_INIT = 0,
    AKM_FREQ,
    AKM_TXPDN,
    AKM_RXPDN,
    AKM_AGC_KEEP,

    AKM_LD,
    AKM_REG,
    AKM_REG_PRINT,
    AKM_OFFSET,
    AKM_RSSI,

    AKM_PARAM_NUM    
}te_akm_param_type;

typedef struct _ts_akm_param_type
{
    int path;
    te_akm_param_type param_type;
    uint32_t value;
}ts_akm_param_type;

#define PN9_LEN (5760)
/*一次发送30ms数据，由用户层读取PN9文件，并填充*/
typedef struct _ts_akm_pn9_type
{
    int type;
    uint32_t pn9_data[PN9_LEN]; 
} ts_akm_pn9_type;


/**时隙同步 
手台：
直通模式，
发射时:
当前时隙为主时隙，下个时隙为从时隙
主时隙发射数据
从时隙做数据准备，及发射射频电路准备

接收时：
全时隙接收，同步上后，依据业务设置主从时隙，业务时隙为主时隙
同步后和业务设置接收时隙时共同确定主从


集群模式下
注册后，即确认主从时隙
发射在主时隙

基站
不区分主从时隙，
收发分开模块
发射模块，全时隙发射
接收全时隙接收
*/
/**
使能单时隙发射 
使能全时隙发射
写入爬坡滚降参数
写入发射数据

提示接收数据
提示发射数据准备
*/

/**业务流程
发射（正常发送和方向信令发送）
--------user space------------kenerl------
         发射使能 正常  ---------> 收到发射指示，设置发射开始
                       
                       <--------发射准备数据信号
        数据准备好，并写入内核 ---------->
                                  发射数据
                      

*/
#define RFCONTROL_CMD_PARAS_GET                      _IO(RFCONTROL_IOC_MAGIC, 0)
#define RFCONTROL_CMD_PARAS_SET                      _IOW(RFCONTROL_IOC_MAGIC,1, ts_rfcontrol_parameters)
#define RFCONTROL_CMD_SLOT_ADJUST                    _IOW(RFCONTROL_IOC_MAGIC,2, int)  /* 接收同步上后，设置发射时隙，获取当前时隙号，确定后调整时隙 ，调整时隙时，不能发射*/
#define RFCONTROL_CMD_SLOT_GET                       _IOR(RFCONTROL_IOC_MAGIC,3, ts_tdma_time)
#define RFCONTROL_CMD_SLOT_ENABLE                    _IOW(RFCONTROL_IOC_MAGIC,4, uint32_t)
#define RFCONTROL_CMD_PA_RAMP_DATA                   _IOW(RFCONTROL_IOC_MAGIC,5, ts_pa_data_type)  /* 调整时设置 */
#define RFCONTROL_CMD_TRANSFER_DATA_WRITE            _IOW(RFCONTROL_IOC_MAGIC,6, ts_transfer_type)  /* 依据是否有数据，决定是否发射 /因为爬升和该数据一起填充，共用一个命令，滚降也同时用该命令*/
#define RFCONTROL_CMD_PA_VOLTAG_MV                   _IOW(RFCONTROL_IOC_MAGIC,7, uint32_t)  /* 设置功放电压*/
#define RFCONTROL_CMD_TV_VOLTAG_MV                   _IOW(RFCONTROL_IOC_MAGIC,8, uint32_t)  /* TV电压*/
#define RFCONTROL_CMD_CRYSTAL_VOLTAG_MV              _IOW(RFCONTROL_IOC_MAGIC,9, uint32_t)  /* 参考晶振电压(19.2M/18.432M)*/
#define RFCONTROL_CMD_MOD_REF_VOLTAG_MV              _IOW(RFCONTROL_IOC_MAGIC,10, uint32_t)  /* 参考晶振电压(26M)*/
#define RFCONTROL_CMD_TX_ON                          _IOW(RFCONTROL_IOC_MAGIC,11, uint32_t)
#define RFCONTROL_CMD_RX1_ON                         _IOW(RFCONTROL_IOC_MAGIC,12, uint32_t)
#define RFCONTROL_CMD_RX2_ON                         _IOW(RFCONTROL_IOC_MAGIC,13, uint32_t)
#define RFCONTROL_CMD_RX_VCO1_ON                     _IOW(RFCONTROL_IOC_MAGIC,14, uint32_t)
#define RFCONTROL_CMD_RX_VCO2_ON                     _IOW(RFCONTROL_IOC_MAGIC,15, uint32_t)
#define RFCONTROL_CMD_RFPWREN_ON                     _IOW(RFCONTROL_IOC_MAGIC,16, uint32_t)
#define RFCONTROL_CMD_PSAPC_ON                       _IOW(RFCONTROL_IOC_MAGIC,17, uint32_t)
#define RFCONTROL_CMD_PLL_TX_FRQ_SET                 _IOW(RFCONTROL_IOC_MAGIC,18, uint32_t)
#define RFCONTROL_CMD_PLL_RX_FRQ_SET                 _IOW(RFCONTROL_IOC_MAGIC,19, uint32_t)
#define RFCONTROL_CMD_PLL_RX2_FRQ_SET                _IOW(RFCONTROL_IOC_MAGIC,20, uint32_t)
#define RFCONTROL_CMD_PLL_LD_GET                     _IOWR(RFCONTROL_IOC_MAGIC,21, uint32_t)  /*锁频状态检测，1 - 第一路 2 - 第二路*/

/*以下组合命令，降低内核与用户层交互*/
#define RFCONTROL_CMD_RF_TX_ONOFF_SET                _IOW(RFCONTROL_IOC_MAGIC,60, uint32_t) /*组合命令关闭/开启发射射频电路*/
#define RFCONTROL_CMD_RF_RX_ONOFF_SET                _IOW(RFCONTROL_IOC_MAGIC,61, uint32_t) /*组合命令，关闭/开启接收第一通道电路(rxon,rxvco等)*/
#define RFCONTROL_CMD_RF_RX2_ONOFF_SET               _IOW(RFCONTROL_IOC_MAGIC,62, uint32_t) /*组合命令，关闭/开启接收第二通道电路(rxon,rxvco等)*/


/*以下依据不同硬件平台，对锁相环芯片进行控制，比如2401/lmx2571/lmx2572/sky72310/bk4819*/
#define RFCONTROL_CMD_AKM                            _IOWR(RFCONTROL_IOC_MAGIC,80, ts_akm_param_type)
#define RFCONTROL_CMD_AKM_PN9                        _IOW_BAD(RFCONTROL_IOC_MAGIC,81, ts_akm_pn9_type)  /*抓取demo板的PN9数据，属于双时隙发射 192K速率，32bit数据，共46080，一次发送30ms数据*/

#endif /* SRC_RF_H_ */

/*end of the file:rf.h */
