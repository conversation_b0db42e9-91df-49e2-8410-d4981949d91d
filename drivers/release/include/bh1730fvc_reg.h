/*********************************************************************
 * \file bh1730fvc_reg.h
 * @Author: liming
 * @Date:   2025-02-19 10:37:21
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 16:37:44
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 *                                                              
 *********************************************************************/
#ifndef BH1730FVC_H
#define BH1730FVC_H

#ifdef __cplusplus
extern "C" {
#endif

/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================

//============================> Libraries Headers <============================

//============================> Project Headers <============================

/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/

//============================> I2C Settings <============================

#define BH1730FVC_SLAVE_ADDR                    (uint8_t)(0x29 << 1)

//============================> Register Addresses <============================

#define BH1730FVC_CMD                           (uint8_t)0x80   // 命令

#define BH1730FVC_REG_CONTROL                   (uint8_t)0x00   // R/W
#define BH1730FVC_REG_TIMING                    (uint8_t)0x01   // R/W
#define BH1730FVC_REG_INTERRUPT                 (uint8_t)0x02   // R/W
#define BH1730FVC_REG_THLLOW                    (uint8_t)0x03   // R/W
#define BH1730FVC_REG_THLHIGH                   (uint8_t)0x04   // R/W
#define BH1730FVC_REG_THHLOW                    (uint8_t)0x05   // R/W
#define BH1730FVC_REG_THHHIGH                   (uint8_t)0x06   // R/W
#define BH1730FVC_REG_GAIN                      (uint8_t)0x07   // R/W
#define BH1730FVC_REG_ID                        (uint8_t)0x12   // R
#define BH1730FVC_REG_DATA0LOW                  (uint8_t)0x14   // R
#define BH1730FVC_REG_DATA0HIGH                 (uint8_t)0x15   // R
#define BH1730FVC_REG_DATA1LOW                  (uint8_t)0x16   // R
#define BH1730FVC_REG_DATA1HIGH                 (uint8_t)0x17   // R

//============================> Register Settings <============================

#define BH1730FVC_CONTROL_ADC_POWER_ON          (uint8_t)0x01   // 0: ADC电源关闭 1: ADC电源打开
#define BH1730FVC_CONTROL_ADC_EN                (uint8_t)0x02   // 0: 停止ADC测量 1: 开始ADC测量
#define BH1730FVC_CONTROL_TYPE0_ONLY            (uint8_t)0x04   // 0: ADC测量类型为Type0(可见光)和Type1(红外光) 1: ADC测量类型仅为Type0(可见光)
#define BH1730FVC_CONTROL_ONE_TIME              (uint8_t)0x08   // 0: 连续测量 1: 单次测量(测量完成后ADC电源关闭)
#define BH1730FVC_CONTROL_ADC_VALID             (uint8_t)0x10   // 只读, 0: 距离上次测量, ADC数据未更新 1: 距离上次测量, ADC数据已更新
#define BH1730FVC_CONTROL_ADC_INTR              (uint8_t)0x20   // 只读, 0: 无中断 1: 有中断

#define BH1730FVC_INTERRUPT_INT_ENABLE          (uint8_t)0x10   // 中断使能
#define BH1730FVC_INTERRUPT_INT_STOP            (uint8_t)0x40   // 当中断发生时, 停止测量并进入关机模式

#define BH1730FVC_GAIN_1X                       (uint8_t)0x00   // 增益1X
#define BH1730FVC_GAIN_2X                       (uint8_t)0x01   // 增益2X
#define BH1730FVC_GAIN_64X                      (uint8_t)0x02   // 增益64X
#define BH1730FVC_GAIN_128X                     (uint8_t)0x03   // 增益128X

//============================> Special Commands <============================

#define BH1730FVC_CMD_SPECIAL                   (uint8_t)0x60 | BH1730FVC_CMD   // 特殊命令

#define BH1730FVC_INT_OUTPUT_RESET              (uint8_t)0x01   // 中断输出复位
#define BH1730FVC_STOP_MEASUREMENT              (uint8_t)0x02   // 停止测量
#define BH1730FVC_START_MEASUREMENT             (uint8_t)0x03   // 开始测量
#define BH1730FVC_SOFT_RESET                    (uint8_t)0x04   // 软件复位

#define BH1730FVC_PART_NUMBER                   (uint8_t)0x07

/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/

/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/

/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/

/******************************************************************************

                                Public Functions

 ******************************************************************************/

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
