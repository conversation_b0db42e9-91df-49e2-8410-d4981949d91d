/*********************************************************************
 * \file sc5883lp_reg.h
 * @Author: liming
 * @Date:   2025-02-19 10:37:21
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 16:38:53
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 *                                                              
 *********************************************************************/
#ifndef SMC5883P_H
#define SMC5883P_H

#ifdef __cplusplus
extern "C" {
#endif

/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================

//============================> Libraries Headers <============================

//============================> Project Headers <============================

/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/

#define SMC5883P_SLAVE_ADDR                     0x2C

//============================> Register Map <============================//

#define SMC5883P_REG_CHIP_ID                    0x00
#define SMC5883P_REG_DATA_X_LSB                 0x01
#define SMC5883P_REG_DATA_X_MSB                 0x02
#define SMC5883P_REG_DATA_Y_LSB                 0x03
#define SMC5883P_REG_DATA_Y_MSB                 0x04
#define SMC5883P_REG_DATA_Z_LSB                 0x05
#define SMC5883P_REG_DATA_Z_MSB                 0x06
#define SMC5883P_REG_STATUS                     0x09

#define SMC5883P_REG_AXIS_DIR                   0x29

#define SMC5883P_REG_CTRL1                      0x0A
#define SMC5883P_REG_CTRL2                      0x0B

//============================> Register Bits <============================//

/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/

/**
 * @brief SMC5883P磁传感器工作模式
 */
typedef enum
{
    /**
     * @brief 暂停模式 (Suspend Mode)
     * 在此模式下，传感器的功耗最低，所有测量停止，寄存器保持当前状态。
     */
    SMC5883P_MODE_SUSPEND = 0,

    /**
     * @brief 正常模式 (Normal Mode)
     * 传感器处于活跃状态并进行定期测量，数据速率和过采样率由ODR和OSR寄存器设置。
     */
    SMC5883P_MODE_NORMAL = 1,

    /**
     * @brief 单次模式 (Single Mode)
     * 传感器进行一次测量后自动进入暂停模式，适用于需要单次测量的应用。
     */
    SMC5883P_MODE_SINGLE = 2,

    /**
     * @brief 连续模式 (Continuous Mode)
     * 传感器连续进行测量并更新数据，适用于需要连续数据流的应用。
     */
    SMC5883P_MODE_CONTINUOUS = 3
} smc5883p_mode_t;

typedef enum
{
    SMC5883P_ODR_10HZ = 0,
    SMC5883P_ODR_50HZ = 1,
    SMC5883P_ODR_100HZ = 2,
    SMC5883P_ODR_200HZ = 3
} smc5883p_odr_t;

/**
 * @brief SMC5883P磁传感器过采样率
 * 
 * @note 过采样率越高，输出数据的精度越高，但是功耗也会增加。
 */
typedef enum
{
    SMC5883P_OVER_SAMPLE_RATE_8 = 0,
    SMC5883P_OVER_SAMPLE_RATE_4 = 1,
    SMC5883P_OVER_SAMPLE_RATE_2 = 2,
    SMC5883P_OVER_SAMPLE_RATE_1 = 3
} smc5883p_osr1_t;

/**
 * @brief SMC5883P磁传感器下采样率
 * 
 * @note 下采样率越高，输出数据的精度越高，但是功耗也会增加。
 */
typedef enum
{
    SMC5883P_DOWN_SAMPLING_RATE_1 = 0,
    SMC5883P_DOWN_SAMPLING_RATE_2 = 1,
    SMC5883P_DOWN_SAMPLING_RATE_4 = 2,
    SMC5883P_DOWN_SAMPLING_RATE_8 = 3
} smc5883p_osr2_t;

/**
 * @brief SMC5883P磁传感器量程
 * 
 * @note 量程越大，传感器能够测量的磁场范围越广，但是精度也会降低。
 */
typedef enum
{
    SMC5883P_RANGE_30_GAUSS = 0,
    SMC5883P_RANGE_12_GAUSS = 1,
    SMC5883P_RANGE_8_GAUSS = 2,
    SMC5883P_RANGE_2_GAUSS = 3
} smc5883p_rng_t;

typedef enum
{
    SMC5883P_AXIS_MODE_1 = 0, // X正向: 西, Y正向: 北, Z正向: 下
    SMC5883P_AXIS_MODE_2 = 1, // X正向: 东, Y正向: 北, Z正向: 下
    SMC5883P_AXIS_MODE_3 = 2, // X正向: 西, Y正向: 南, Z正向: 下
    SMC5883P_AXIS_MODE_4 = 3, // X正向: 东, Y正向: 南, Z正向: 下
    SMC5883P_AXIS_MODE_5 = 4, // X正向: 西, Y正向: 北, Z正向: 上
    SMC5883P_AXIS_MODE_6 = 5, // X正向: 东, Y正向: 北, Z正向: 上
    SMC5883P_AXIS_MODE_7 = 6, // X正向: 西, Y正向: 南, Z正向: 上
    SMC5883P_AXIS_MODE_8 = 7  // X正向: 东, Y正向: 南, Z正向: 上 
} smc5883p_axis_dir_t;

/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/

typedef union
{
    uint8_t raw;
    struct
    {
        uint8_t mode : 2;  // b0-b1: 操作模式, 默认为Suspend Mode
        uint8_t odr : 2;   // b2-b3: 输出数据速率, 默认为10Hz
        uint8_t osr1 : 2;  // b4-b5: 过采样率1
        uint8_t osr2 : 2;  // b6-b7: 过采样率2
    } bit;
} smc5883p_ctrl1_t;

typedef union
{
    uint8_t raw;
    struct
    {
        uint8_t set_reset_mode : 2; // b0-b1: 设置/复位模式
        uint8_t rng : 2;            // b2-b3: 
        uint8_t rfu : 2;            // b4-b5: 保留
        uint8_t self_test : 1;      // b6: 自检
        uint8_t soft_rst : 1;       // b7: 软件复位
    } bit;
} smc5883p_ctrl2_t;

typedef union
{
    uint8_t raw;
    struct 
    {
        uint8_t drdy : 1; // b0: 0-尚未有新的数据, 1-新的数据已经准备好
        uint8_t ovfl : 1; // b1: 0-未发生溢出, 1-发生溢出
    } bit;
} smc5883p_status_t;

typedef struct s_smc5883p_data
{
    int16_t x;
    int16_t y;
    int16_t z;
} smc5883p_data_t;

/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/


/******************************************************************************

                                Public Functions

 ******************************************************************************/

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
