/*********************************************************************
 * \file als.c
 * @Author: liming
 * 光敏检测驱动
 * @Date:   2025-02-19 10:37:21
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 16:39:08
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 *                                                              
 *********************************************************************/
#include <linux/types.h>
#include <linux/kernel.h>
#include <linux/delay.h>
#include <linux/init.h>
#include <linux/module.h>
#include <linux/errno.h>
#include <linux/gpio.h>
#include <linux/cdev.h>
#include <linux/device.h>
#include <linux/of.h>
#include <linux/of_device.h>
#include <linux/platform_device.h>
#include <linux/of_gpio.h>
#include <linux/semaphore.h>
#include <linux/timer.h>
#include <linux/i2c.h>
#include <asm/uaccess.h>
#include <asm/io.h>
#include "bh1730fvc_reg.h"

#define ALS_NAME "als"
#define ALS_CNT 	1
#define DEVICE_VERSION "V1.001"
struct als_struct {
	int major;
	int minor;
	dev_t devid;
	struct device *device;
	struct class *class;
	struct cdev cdev;
	void *private_data;
};

struct als_ops {
    int (*init)(struct als_struct *dev);       // 初始化传感器
    int (*read_data)(struct als_struct *dev, u16 *data); // 读取光强值
};

static struct als_struct als_drv;

static int als_read_regs(struct als_struct *dev, u8 reg, void *val, u16 len)
{
	struct i2c_client *client = (struct i2c_client *)dev->private_data;
	
	struct i2c_msg msg[] = {
		{
			.addr = client->addr,
			.flags = 0,
			.buf = &reg,
			.len = 1,
		},
		{
			.addr = client->addr,
			.flags = I2C_M_RD,
			.buf = val,
			.len = len,
		}
	};

	return i2c_transfer(client->adapter, msg, 2);
}

static int als_write_regs(struct als_struct *dev, u8 reg, u8 *buf, u16 len)
{
	u8 b[256];
	struct i2c_client *client = (struct i2c_client *)dev->private_data;
	struct i2c_msg msg[] = {
		{
			.addr = client->addr,
			.flags = 0,
			.buf = b,
			.len = len + 1,
		},
		{
		
		}
	};

	b[0] = reg;
	memcpy(&b[1], buf, len);

	return i2c_transfer(client->adapter, msg, 1);
}

static int als_write_one_reg_no_data(struct als_struct *dev, u8 reg)
{
	u8 b = reg;
	struct i2c_client *client = (struct i2c_client *)dev->private_data;
	struct i2c_msg msg[] = {
		{
			.addr = client->addr,
			.flags = 0,
			.buf = &b,
			.len = 2,
		},
		{
		
		}
	};

	return i2c_transfer(client->adapter, msg, 1);
}


static unsigned char als_read_one_reg(struct als_struct *dev, u8 reg)
{
	u8 data = 0;

	als_read_regs(dev, reg, &data, 1);

	return data;
}

static void als_write_one_reg(struct als_struct *dev, u8 reg, u8 data)
{
	u8 buf = data;

	als_write_regs(dev, reg, &buf, 1);
}

static int bh1730fvc_reg_init(struct als_struct *dev)
{
	//复位
	als_write_one_reg_no_data(dev, BH1730FVC_CMD_SPECIAL | BH1730FVC_SOFT_RESET);

	// 设置CONTROL寄存器
	als_write_one_reg(dev, BH1730FVC_CMD | BH1730FVC_REG_CONTROL, BH1730FVC_CONTROL_TYPE0_ONLY | BH1730FVC_CONTROL_ADC_EN | BH1730FVC_CONTROL_ADC_POWER_ON);

	// 设置TIMING寄存器
	als_write_one_reg(dev, BH1730FVC_CMD | BH1730FVC_REG_TIMING, 0xDA);

	// 设置ADC增益
	als_write_one_reg(dev, BH1730FVC_CMD | BH1730FVC_REG_GAIN, BH1730FVC_GAIN_1X);

	dev_info_once(dev->device, "chipid:%#x", als_read_one_reg(dev, BH1730FVC_CMD | BH1730FVC_REG_ID));

	return 0;
}

static int bh1730fvc_read_data(struct als_struct *dev, u16 *data)
{
	uint8_t control_reg = 0;
	uint8_t data0_low = 0, data0_high = 0;

	control_reg = als_read_one_reg(dev, BH1730FVC_CMD | BH1730FVC_REG_CONTROL);

	while (!(control_reg & BH1730FVC_CONTROL_ADC_VALID))
	{
		control_reg = als_read_one_reg(dev, BH1730FVC_CMD | BH1730FVC_REG_CONTROL);
	}

	data0_low = als_read_one_reg(dev, BH1730FVC_CMD | BH1730FVC_REG_DATA0LOW);
	data0_high = als_read_one_reg(dev, BH1730FVC_CMD | BH1730FVC_REG_DATA0HIGH);

	*data = (data0_high << 8) | data0_low;

	return 0;
}

static int als_open(struct inode *inode, struct file *flip)
{
	flip->private_data = &als_drv;

	return 0;
}

static ssize_t als_read(struct file *flip, char __user *user, size_t cnt, loff_t *offt)
{
	int ret;
	struct als_ops *ops;
	static uint16_t data;
	struct als_struct *dev =(struct als_struct *)flip->private_data;
	struct i2c_client *client = (struct i2c_client *)dev->private_data;

	ops = client->dev.platform_data;

	ret = ops->read_data(dev, &data);

	ret = copy_to_user(user, &data , sizeof(data));
	return 0;
}

static int als_release (struct inode *inode, struct file *flip)
{
	struct als_struct *dev =(struct als_struct *)flip->private_data;

	return 0;
}

static struct file_operations als_fops = {
	.owner = THIS_MODULE,
	.open = als_open,
	.read = als_read,
	.release = als_release,
};

static int als_probe(struct i2c_client *client, const struct i2c_device_id *of_match_table)
{
	const struct als_ops *ops;
	struct device *dev = &client->dev;
	ops = of_device_get_match_data(dev);
	if (!ops)
		ops = dev_get_platdata(dev);
	else
		dev->platform_data = (void *)ops;

	if (!ops) {
		dev_err(dev, "%s: no platform data.\n", __func__);
		return -ENODEV;
	}

	als_drv.major = 0;
	if(als_drv.major){
		als_drv.devid = MKDEV(als_drv.major, 0);
		register_chrdev_region(als_drv.devid, ALS_CNT, ALS_NAME);
	}else{
		alloc_chrdev_region(&als_drv.devid, 0, ALS_CNT, ALS_NAME);
		als_drv.major = MAJOR(als_drv.devid);
		als_drv.minor = MINOR(als_drv.devid);
	}
	
	als_drv.cdev.owner = THIS_MODULE;
	cdev_init(&als_drv.cdev, &als_fops);
	cdev_add(&als_drv.cdev, als_drv.devid, ALS_CNT);

	als_drv.class = class_create(THIS_MODULE, ALS_NAME);
	als_drv.device = device_create(als_drv.class, NULL, als_drv.devid, NULL, ALS_NAME);

	als_drv.private_data = client;

	ops->init(&als_drv);

	return 0;
}

static void als_remove(struct i2c_client *client)
{
	device_destroy(als_drv.class, als_drv.devid);
	class_destroy(als_drv.class);
	cdev_del(&als_drv.cdev);
	unregister_chrdev_region(als_drv.devid, ALS_CNT);
}

static const struct als_ops bh1730fvc_ops = {
	.init = bh1730fvc_reg_init,
	.read_data = bh1730fvc_read_data,
};

static const struct of_device_id als_match_table[] = {
	{.compatible = "victel,bh1730fvc",
	     .data = &bh1730fvc_ops,
	},
	{ /* Sential */}
	
};

static const struct i2c_device_id als_id_table[] = {
	{"victel,bh1730fvc", 0},
	{ /* Sential */}

};

static struct i2c_driver als_i2c_drv = {
	.probe = als_probe,
	.remove = als_remove,
	.driver = {
		.owner = THIS_MODULE,
		.name = "als",
		.of_match_table = als_match_table,

	},
	.id_table = als_id_table,
};


static int __init als_init(void)
{
	int ret = 0;
	ret = i2c_add_driver(&als_i2c_drv);

	return 0;
}

static void __exit als_exit(void)
{
	i2c_del_driver(&als_i2c_drv);
}

module_init(als_init);
module_exit(als_exit);
MODULE_AUTHOR("cfl");
MODULE_LICENSE("GPL v2");

MODULE_VERSION(DEVICE_VERSION);

