/*********************************************************************
 * \file gc9307.c
 * @Author: liming
 * @Date:   2025-05-22 11:41:27
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 16:39:20
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 *                                                              
 *********************************************************************/
#include <linux/backlight.h>
#include <linux/delay.h>
#include <linux/module.h>
#include <linux/gpio.h>
#include <linux/cdev.h>
#include <linux/device.h>
#include <linux/of_gpio.h>
#include <linux/spi/spi.h>
#include <linux/fb.h>
#include <linux/dma-mapping.h>
#include "gc9307.h"


#define DEVICE_VERSION      "V1.001"
static DEFINE_SPINLOCK(region_lock); 
static DEFINE_MUTEX(gc9307_mutex);
struct fb_info *fbi;
struct ipsTft_dev ipsTftdev;
struct lvgl_region region; 
unsigned long flags;

/*
 *  @description	: 打印读取缓冲区数据
 
 *  @param - dev:  ipsTft设备
 *  @param - groupsize:  分组大小
 *  @param - buf:  读取缓冲区指针
 *  @param - len:  数据长度
 *  @param - fmt:  格式化字符串
*/

void fbtft_dbg_hex(const struct device *dev, int groupsize,
		   const void *buf, size_t len, const char *fmt, ...)
{
	va_list args;
	static char textbuf[512];
	char *text = textbuf;
	size_t text_len;
	va_start(args, fmt);
	text_len = vscnprintf(text, sizeof(textbuf), fmt, args);
	va_end(args);
	hex_dump_to_buffer(buf, len, 32, groupsize, text + text_len,
			   512 - text_len, false);
	if (len > 32)
		dev_info(dev, "%s ...\n", text);
	else
		dev_info(dev, "%s\n", text);
}

/*
 * @description	: 向ipsTft多个寄存器写入数据

 * @param - dev:  ipsTft设备
 * @param - reg:  要写入的寄存器首地址
 * @param - val:  要写入的数据缓冲区
 * @param - len:  要写入的数据长度
 * @return 	  :   操作结果
 */

static int ipsTft_write_regs(struct spi_device *spi, u8 *buf, u32 len)
{
    int ret;
    struct spi_message msg;
    struct spi_transfer xfer;

    spi_message_init(&msg);
    
    memset(&xfer, 0, sizeof(xfer));
    xfer.tx_buf = buf;
    xfer.len = len;
    
    spi_message_add_tail(&xfer, &msg);

    ret = spi_sync(spi, &msg);

    return ret;
}

/*
 * @description	: 向ipsTft多个寄存器读数据

 * @param - dev:  ipsTft设备
 * @param - reg:  要读取的寄存器首地址
 * @param - val:  要读取的数据缓冲区
 * @param - len:  要读取的数据长度
 * @return 	  :   操作结果
 */
static int ipsTft_read_regs(struct spi_device *spi, u8 reg, void *buf, size_t len)
{
    int ret;
	struct spi_message *m;
	struct spi_transfer *t;
    u8 txbuf[32] = {0};
    txbuf[0] = reg;
    m = kzalloc(sizeof(struct spi_message), GFP_KERNEL);
	t = kzalloc(sizeof(struct spi_transfer), GFP_KERNEL);	/* 申请内存 */
    
    // t->speed_hz = 2000000;
	t->tx_buf = txbuf;			/* 要写入的数据 */
    t->rx_buf = buf;
	t->len = len;				/* 写入的字节数 */
    fbtft_dbg_hex(&spi->dev, sizeof(u8), txbuf, len*sizeof(u8), "%s(len=%zu) txbuf => ", __func__, len);
	spi_message_init(m);		/* 初始化spi_message */
	spi_message_add_tail(t, m);/* 将spi_transfer添加到spi_message队列 */
	ret = spi_sync(spi, m);	/* 同步发送 */
    fbtft_dbg_hex(&spi->dev, sizeof(u8), buf, len*sizeof(u8), "%s(len=%zu) buf <= ", __func__, len);
    kfree(m);
	kfree(t);					/* 释放内存 */
	return ret;
}

/*
 * @description	: 向ipsTft指定寄存器写入指定的值，写一个寄存器
 * @param - dev:  ipsTft设备
 * @param - reg:  要写的寄存器
 * @param - data: 要写入的值
 * @return   :    无
 */	

static void ipsTft_write_onereg(struct spi_device *spi, u8 buf)
{
	ipsTft_write_regs(spi,&buf, 1);
}

/*
    funciton: 写一个命令
*/
void write_command(struct spi_device *spi, u8 cmd)
{
    // dc , command:0
    gpio_set_value(ipsTftdev.dc_gpio, 0); 
    ipsTft_write_onereg(spi,cmd);
}

/*
    funciton: 写一个数据
*/
void write_data(struct spi_device *spi, u8 data)
{
    gpio_set_value(ipsTftdev.dc_gpio, 1);
    ipsTft_write_onereg(spi,data);
}
/*
    funciton: 写多个数据
*/
static void write_datas(struct spi_device *spi, u8 *data, u32 len)
{
    gpio_set_value(ipsTftdev.dc_gpio, 1);
    ipsTft_write_regs(spi,data,len);
}

static void read_command(struct spi_device *spi, u8 reg, void *buf, size_t len)
{
    gpio_set_value(ipsTftdev.dc_gpio, 0);
    ipsTft_read_regs(spi,reg,buf,len);
}

void Address_set(struct spi_device *spi,unsigned int x1,unsigned int y1,unsigned int x2,unsigned int y2)
{ 
    write_command(spi,0x2a);
    write_data(spi,x1>>8);
    write_data(spi,x1);
    write_data(spi,x2>>8);
    write_data(spi,x2);
  
    write_command(spi,0x2b);
    write_data(spi,y1>>8);
    write_data(spi,y1);
    write_data(spi,y2>>8);
    write_data(spi,y2);

    // write_command(spi,0x2C);					 						 
}

//show frambuffer
void show_fb(struct fb_info *fbi, struct spi_device *spi)
{
    u8 *p = (u8 *)(fbi->screen_base);

    // 设置显示区域为整个屏幕
    Address_set(spi, 0, 0, LCD_W-1, LCD_H-1);
    write_command(spi, 0x2C);

    write_datas(spi, p, fbi->screen_size);
}

//show frambuffer area
void show_fb_area(struct fb_info *fbi, struct spi_device *spi, struct lvgl_region lvgl) {
    u8 *p = (u8 *)(fbi->screen_base);
    int width = lvgl.x2 - lvgl.x1 + 1;
    int height = lvgl.y2 - lvgl.y1 + 1;
    int stride = fbi->fix.line_length;
    u8 *buffer;
    size_t total_size = width * height * 2;

    buffer = kzalloc(total_size, GFP_KERNEL);
    if (!buffer) {
        dev_err(&spi->dev, "Failed to allocate buffer\n");
        return;
    }

    u8 *dst = buffer;
    u8 *src = p + lvgl.y1 * stride + lvgl.x1 * 2;

    for(int i = 0;i < height;i++)
    {
        memcpy(dst, src, width * 2);
        dst += width * 2;
        src += stride;
    }

    Address_set(spi, lvgl.x1, lvgl.y1, lvgl.x2, lvgl.y2);
    write_command(spi, 0x2C);
    write_datas(spi, buffer, total_size);

    kfree(buffer);
}

static void delayed_display_work_func(struct work_struct *work)
{
    lcd_data_t *ldata = fbi->par;
    struct spi_device *spi = ldata->spi;
    struct lvgl_region local_region; 

    spin_lock_irqsave(&region_lock, flags);
    local_region = region;
    region.is_dirty = false; // 清除脏标志
    spin_unlock_irqrestore(&region_lock, flags);

    if(!local_region.is_dirty) {
        show_fb(fbi, ldata->spi);
    }else {
        show_fb_area(fbi, ldata->spi, local_region);
    }
    
    schedule_delayed_work(&ldata->delayed_display_work, msecs_to_jiffies(17));
}

static u32 pseudo_palette[LCD_W*LCD_H];
static inline unsigned int chan_to_field(unsigned int chan, struct fb_bitfield *bf)
{
	chan &= 0xffff;
	chan >>= 16 - bf->length;
	return chan << bf->offset;
}

static int tft_lcdfb_setcolreg(unsigned int regno, unsigned int red,
			     unsigned int green, unsigned int blue,
			     unsigned int transp, struct fb_info *info)
{
	unsigned int val;
	
	if (regno > 16)
	{
		return 1;
	}

	/* 用red,green,blue三原色构造出val  */
	val  = chan_to_field(red,	&info->var.red);
	val |= chan_to_field(green, &info->var.green);
	val |= chan_to_field(blue, &info->var.blue);
	
	pseudo_palette[regno] = val;
	return 0;
}

static int tft_blank(int blank_mode, struct fb_info *info)
{
    lcd_data_t *ldata = fbi->par;
    struct spi_device *spi = ldata->spi;
    struct backlight_device *bl = (struct backlight_device *)spi->dev.platform_data;

    switch (blank_mode) {
    case FB_BLANK_UNBLANK:
        write_command(spi, 0x13);
        write_command(spi, 0x29);
        backlight_enable(bl);
        break;
    
    case FB_BLANK_POWERDOWN:
        cancel_delayed_work_sync(&ldata->delayed_display_work);
        write_command(spi, 0x28);
        // msleep(120);
        write_command(spi, 0x10);
        // msleep(50);
        backlight_disable(bl);
        break;

    case FB_BLANK_NORMAL:
        write_command(spi, 0x11);
        // msleep(120);
        write_command(spi, 0x29);
        backlight_enable(bl);
        schedule_delayed_work(&ldata->delayed_display_work, msecs_to_jiffies(17));
        break;

    case FB_BLANK_HSYNC_SUSPEND:
        break;
    case FB_BLANK_VSYNC_SUSPEND:
        break;
    default:
        return 1; // 无效模式
    }

    return 0;
}

/*
static int tft_lcdfb_mmap(struct fb_info *info, struct vm_area_struct *vma)
{
	unsigned long start = vma->vm_start;
	unsigned long size = vma->vm_end - vma->vm_start;
	unsigned long offset = vma->vm_pgoff << PAGE_SHIFT;
	unsigned long page, pos;

	if (info->fbdefio)
		return fb_deferred_io_mmap(info, vma);

	if (vma->vm_pgoff > (~0UL >> PAGE_SHIFT))
		return -EINVAL;
	if (size > info->fix.smem_len)
		return -EINVAL;
	if (offset > info->fix.smem_len - size)
		return -EINVAL;

	pos = (unsigned long)info->fix.smem_start + offset;

	dev_dbg(info->dev, "mmap() framebuffer addr:%lu size:%lu\n",
		pos, size);

	while (size > 0) {
		page = vmalloc_to_pfn((void *)pos);
		if (remap_pfn_range(vma, start, page, PAGE_SIZE, PAGE_SHARED))
			return -EAGAIN;

		start += PAGE_SIZE;
		pos += PAGE_SIZE;
		if (size > PAGE_SIZE)
			size -= PAGE_SIZE;
		else
			size = 0;
	}

	return 0;
}

static int tft_lcdfb_mmap(struct fb_info *info,
		    struct vm_area_struct *vma)
{
	unsigned long start = vma->vm_start;
	unsigned long size = vma->vm_end - vma->vm_start;
	unsigned long offset = vma->vm_pgoff << PAGE_SHIFT;
	unsigned long pos;

	if (info->fbdefio)
		return fb_deferred_io_mmap(info, vma);

	if (vma->vm_pgoff > (~0UL >> PAGE_SHIFT))
		return -EINVAL;
	if (size > info->fix.smem_len)
		return -EINVAL;
	if (offset > info->fix.smem_len - size)
		return -EINVAL;

	pos = (unsigned long)info->fix.smem_start + offset;

	dev_info(info->dev, "mmap() framebuffer addr:%lu size:%lu\n",
		pos, size);

	return remap_vmalloc_range(vma, (void *)info->fix.smem_start, vma->vm_pgoff);
}
*/

struct fb_ops fops = {
    .owner		= THIS_MODULE,
    .fb_read	= fb_sys_read,
    .fb_setcolreg	= tft_lcdfb_setcolreg,
    .fb_fillrect	= cfb_fillrect,
	.fb_copyarea	= cfb_copyarea,
	.fb_imageblit	= cfb_imageblit,
    .fb_blank = tft_blank,
    // .fb_mmap = tft_lcdfb_mmap,
    // .fb_set_par =,
    // .fb_check_var =,
};

int myfb_new(struct spi_device *spi) //此函数在spi设备驱动的probe函数里被调用
{
    dma_addr_t phy_address;
    lcd_data_t *data;
    u64 dma_mask = DMA_BIT_MASK(64);
    
    // dev_info(&spi->dev, "myfb_new\n");
    spi->dev.dma_mask = &dma_mask;
    spi->dev.coherent_dma_mask = dma_mask;

    //额外分配lcd_data_t类型空间
    fbi = framebuffer_alloc(sizeof(lcd_data_t), &spi->dev);
    if(fbi == NULL){
		printk("fbi allow error!\n");
        return -1;
    }
    data = fbi->par; //data指针指向额外分配的空间
    data->spi = spi;

    INIT_DELAYED_WORK(&data->delayed_display_work, delayed_display_work_func);

    fbi->pseudo_palette = pseudo_palette;
	fbi->var.activate       = FB_ACTIVATE_NOW;

    fbi->var.xres = LCD_W;
    fbi->var.yres = LCD_H;
    fbi->var.xres_virtual = LCD_W;
    fbi->var.yres_virtual = LCD_H;
    fbi->var.bits_per_pixel = 16; 
    fbi->var.red.offset = 11;
    fbi->var.red.length = 5;
    fbi->var.green.offset = 5;
    fbi->var.green.length = 6;
    fbi->var.blue.offset = 0;
    fbi->var.blue.length = 5;

    strcpy(fbi->fix.id, "myfb");
    // fbi->fix.smem_start = 0; 
    fbi->fix.smem_len = LCD_W*LCD_H*2; 
    fbi->fix.line_length = LCD_W*2;

    fbi->fbops = &fops;
    fbi->screen_base = dma_alloc_coherent(&spi->dev, fbi->fix.smem_len, &phy_address, GFP_KERNEL | GFP_DMA); //显存虚拟地址
    // fbi->screen_base = vzalloc(fbi->fix.smem_len);
    // fbi->screen_base = dma_alloc_wc(NULL, fbi->fix.smem_len, &phy_address, GFP_KERNEL);
    // fbi->screen_base = ioremap_wc(0x8f000000, fbi->fix.smem_len);
    if (!fbi->screen_base) {
	    printk("Failed to vmalloc allocate memory\n");
	    framebuffer_release(fbi);
	    return -ENOMEM;
    }
    fbi->screen_size = LCD_W*LCD_H*2; //显存大小
    fbi->fix.smem_start = phy_address;
    // fbi->fix.smem_start = 0x8f000000;
    fbi->fix.type = FB_TYPE_PACKED_PIXELS;
    fbi->fix.visual = FB_VISUAL_TRUECOLOR;

    dev_info(&spi->dev, "myfb_new register\n");

    register_framebuffer(fbi);

    schedule_delayed_work(&data->delayed_display_work, msecs_to_jiffies(17));

    return 0;    
}

void myfb_del(void) //此函数在spi设备驱动remove时被调用
{
    lcd_data_t *data = fbi->par;
    struct spi_device *spi = data->spi;
    cancel_delayed_work_sync(&data->delayed_display_work);
    unregister_framebuffer(fbi);
    dma_free_coherent(&spi->dev, fbi->screen_size, fbi->screen_base, fbi->fix.smem_start);
    // vfree(fbi->screen_base);
    // iounmap(fbi->screen_base);
    framebuffer_release(fbi);
}


/*
 * @description		: 打开设备
 * @param - inode 	: 传递给驱动的inode
 * @param - filp 	: 设备文件，file结构体有个叫做pr似有ate_data的成员变量
 * 					  一般在open的时候将private_data似有向设备结构体。
 * @return 			: 0 成功;其他 失败
 */
static int ipsTft_open(struct inode *inode, struct file *filp)
{
	filp->private_data = &ipsTftdev; /* 设置私有数据 */
	return 0;
}

/*
 * @description		: 关闭/释放设备
 * @param - filp 	: 要关闭的设备文件(文件描述符)
 * @return 			: 0 成功;其他 失败
 */
static int ipsTft_release(struct inode *inode, struct file *filp)
{
	return 0;
}

static ssize_t ipsTft_write(struct file *flip, const char __user *user, size_t cnt, loff_t *offt)
{
    struct lvgl_region tmp_region;

    // 从用户空间拷贝数据
    if (copy_from_user(&tmp_region, user, sizeof(struct lvgl_region))) {
        return -EFAULT;
    }

    // 加锁更新region
    spin_lock_irqsave(&region_lock, flags);
    region = tmp_region;
    spin_unlock_irqrestore(&region_lock, flags);

    return sizeof(struct lvgl_region);
}

/*
 * @description		: 控制设备
 * @param - filp 	: 要控制的设备文件(文件描述符)
 * @param - cmd 	: 控制命令
 * @param - arg 	: 控制命令参数
 * @return 			: 0 成功;其他 失败
 */
static long ipsTft_ioctl(struct file *filp, unsigned int cmd , unsigned long arg)
{
    lcd_data_t *ldata = fbi->par;
    struct spi_device *spi = ldata->spi;
    struct ipsTft_dev *dev = filp->private_data;
    void __user *argp = (void __user *)arg;
    int rotation = LCD_DRV_ROTATE_0;

    switch (cmd) {
        case FBIO_SETREGION:
            if((copy_from_user(&region, argp, sizeof(struct lvgl_region)))){
                return -EFAULT;
            }
            break;
        case FBIO_SETROTATION:
            if((copy_from_user(&rotation, argp, sizeof(int)))){
                return -EFAULT;
            }
            if(dev->rotation == rotation) {
                return -1;
            }
            if (rotation != LCD_DRV_ROTATE_0 && rotation != LCD_DRV_ROTATE_180) {
                return -1;
            }
            cancel_delayed_work_sync(&ldata->delayed_display_work);
            if (rotation == LCD_DRV_ROTATE_0)
            {   
                write_command(spi, 0x36);
                write_data(spi, 0xe8);
            }
            else if (rotation == LCD_DRV_ROTATE_180)
            {
                write_command(spi, 0x36);
                write_data(spi, 0x38);
            }
            dev->rotation = rotation;
            schedule_delayed_work(&ldata->delayed_display_work, msecs_to_jiffies(17));
            break;
        default:
            break;
    }
    
    return 0;
}

static long ipsTft_unlocked_ioctl(struct file *file,
				unsigned int cmd,
				unsigned long arg)
{
	int ret;

	mutex_lock(&gc9307_mutex);
	ret = ipsTft_ioctl(file, cmd, arg);
	mutex_unlock(&gc9307_mutex);

	return ret;
}

/* ipsTft操作函数 */
static const struct file_operations ipsTft_ops = {
	.owner = THIS_MODULE,
	.open = ipsTft_open,
    .write = ipsTft_write,
	.release = ipsTft_release,
    .unlocked_ioctl = ipsTft_unlocked_ioctl,
    .compat_ioctl = compat_ptr_ioctl,
};

/*
    刷屏函数
*/
void LCD_Clear(struct ipsTft_dev *dev)
{
    u16 *memory;
    // int count = 0;
    // int split= 10;
    //
    memory = (u16 *)kzalloc(LCD_W*LCD_H*2, GFP_KERNEL);	/* 申请内存 */
    // memory = vzalloc(LCD_W*LCD_H*2);
    // printk("LCD_Clear kzalloc end\n");
	Address_set(dev->private_data,0,0,LCD_W-1,LCD_H-1);
    write_command(dev->private_data,0x2C);
    // msleep(3000);//延迟显示
    // printk("memset start\n");
    memset(memory,0x0000,LCD_W*LCD_H*2); //清黑屏
    // printk("memset end\n");
    // while (count < split)//split 用于设置分批发送数据
    // {
    //     write_datas(dev->private_data,(u8 *)memory+count*LCD_W*LCD_H*2/split,LCD_W*LCD_H*2/split);
    //     count++;
    // }
    write_datas(dev->private_data,(u8 *)memory,LCD_W*LCD_H*2);
    // printk("LCD_Clear end\n");
    // vfree(memory);
    kfree(memory);
}
/*
 * ipsTft内部寄存器初始化函数 
 * @param  	: 无
 * @return 	: 无
 */
void ipsTft_reginit(struct ipsTft_dev *dev)
{
    // unsigned char data[4];

    gpio_set_value(ipsTftdev.res_gpio, 1);
    msleep(50);
    gpio_set_value(ipsTftdev.res_gpio, 0);
    msleep(50);
    gpio_set_value(ipsTftdev.res_gpio, 1);
    msleep(120);
    
    write_command(dev->private_data, 0x11);
    msleep(120);
    
    // Initial commands
    write_command(dev->private_data, 0xfe);
    write_command(dev->private_data, 0xef);
    
    // Display control
    write_command(dev->private_data, 0x36);
    if (dev->rotation == LCD_DRV_ROTATE_0) {
        write_data(dev->private_data, 0xe8); // 0度旋转
    } else if (dev->rotation == LCD_DRV_ROTATE_180) {
        write_data(dev->private_data, 0x38); // 180度旋转
    }
    
    // Interface Pixel Format
    write_command(dev->private_data, 0x3a);
    write_data(dev->private_data, 0x05);
    
    // Power control settings
    write_command(dev->private_data, 0x86);
    write_data(dev->private_data, 0x98);
    
    write_command(dev->private_data, 0x89);
    write_data(dev->private_data, 0x03);
    
    write_command(dev->private_data, 0x8b);
    write_data(dev->private_data, 0x80);
    
    write_command(dev->private_data, 0x8d);
    write_data(dev->private_data, 0x22);
    
    write_command(dev->private_data, 0x8e);
    write_data(dev->private_data, 0x0f);

    // Frame rate control
    write_command(dev->private_data, 0xe8);
    write_data(dev->private_data, 0x12);
    write_data(dev->private_data, 0x00);

    // Power control settings
    write_command(dev->private_data, 0xc3);
    write_data(dev->private_data, 0x47);
    
    write_command(dev->private_data, 0xc4);
    write_data(dev->private_data, 0x28);
    
    write_command(dev->private_data, 0xc9);  
    write_data(dev->private_data, 0x00);

    // Extended command set
    write_command(dev->private_data, 0xff);
    write_data(dev->private_data, 0x62);

    // Display enhancement
    write_command(dev->private_data, 0x99);
    write_data(dev->private_data, 0x3e);
    
    write_command(dev->private_data, 0x9d);
    write_data(dev->private_data, 0x4b);

    // Positive Gamma Control
    write_command(dev->private_data, 0xF0);
    write_data(dev->private_data, 0x07);
    write_data(dev->private_data, 0x0b);
    write_data(dev->private_data, 0x0c);
    write_data(dev->private_data, 0x0a);
    write_data(dev->private_data, 0x06);
    write_data(dev->private_data, 0x31);

    // Negative Gamma Control  
    write_command(dev->private_data, 0xF2);
    write_data(dev->private_data, 0x07);
    write_data(dev->private_data, 0x07);
    write_data(dev->private_data, 0x04);
    write_data(dev->private_data, 0x06);
    write_data(dev->private_data, 0x06);
    write_data(dev->private_data, 0x21);

    // Positive Gamma Correction
    write_command(dev->private_data, 0xF1);
    write_data(dev->private_data, 0x4a);
    write_data(dev->private_data, 0x78);
    write_data(dev->private_data, 0x76);
    write_data(dev->private_data, 0x33);
    write_data(dev->private_data, 0x2f);
    write_data(dev->private_data, 0xaf);

    // Negative Gamma Correction
    write_command(dev->private_data, 0xF3);
    write_data(dev->private_data, 0x38);
    write_data(dev->private_data, 0x74);
    write_data(dev->private_data, 0x72);
    write_data(dev->private_data, 0x22);
    write_data(dev->private_data, 0x28);
    write_data(dev->private_data, 0x6f);

    // SPI interface setting
    // write_command(dev->private_data, 0xe9);
    // write_data(dev->private_data, 0x00);

    // Tearing Effect Line On
    // write_command(dev->private_data, 0x35);
    // write_data(dev->private_data, 0x00);

    // Tearing Effect Line Off
    write_command(dev->private_data, 0x34);

    // Set Tear Scanline
    // write_command(dev->private_data, 0x44);
    // write_data(dev->private_data, 0x00);
    // write_data(dev->private_data, 0x0a);

    LCD_Clear(dev);

    // Sleep Out
    write_command(dev->private_data, 0x11);
    msleep(120);

    // Display ON
    write_command(dev->private_data, 0x29);

    // Memory Write
    //write_command(dev->private_data, 0x2c);

    // read_command(dev->private_data, 0x04, data, 4);

    // LCD_Clear(dev);
    // msleep(120);
    printk("ips init finish!\n");
}


 /*
  * @description     : spi驱动的probe函数，当驱动与
  *                    设备匹配以后此函数就会执行
  * @param - client  : spi设备
  * @param - id      : spi设备ID
  * 
  */	
static int ipsTft_probe(struct spi_device *spi)
{
	int ret = 0;

	/* 1、构建设备号 */
	if (ipsTftdev.major) {
		ipsTftdev.devid = MKDEV(ipsTftdev.major, 0);
		register_chrdev_region(ipsTftdev.devid, ipsTft_CNT, ipsTft_NAME);
	} else {
		alloc_chrdev_region(&ipsTftdev.devid, 0, ipsTft_CNT, ipsTft_NAME);
		ipsTftdev.major = MAJOR(ipsTftdev.devid);
	}

	/* 2、注册设备 */
	cdev_init(&ipsTftdev.cdev, &ipsTft_ops);
	cdev_add(&ipsTftdev.cdev, ipsTftdev.devid, ipsTft_CNT);

	/* 3、创建类 */
	ipsTftdev.class = class_create(THIS_MODULE, ipsTft_NAME);
	if (IS_ERR(ipsTftdev.class)) {
		return PTR_ERR(ipsTftdev.class);
	}

	/* 4、创建设备 */
	ipsTftdev.device = device_create(ipsTftdev.class, NULL, ipsTftdev.devid, NULL, ipsTft_NAME);
	if (IS_ERR(ipsTftdev.device)) {
		return PTR_ERR(ipsTftdev.device);
	}

	/* 获取设备树中cs片选信号 */
	ipsTftdev.nd = of_find_node_by_path("/bus@f0000/spi@20100000/display@0");
	if(ipsTftdev.nd == NULL) {
		printk("ecspi3 node not find!\r\n");
		return -EINVAL;
	}
	/* 2、 获取设备树中的gpio属性，得到BEEP所使用的BEEP编号 */
	ipsTftdev.cs_gpio = of_get_named_gpio(ipsTftdev.nd, "cs-gpio", 0);
	if(ipsTftdev.cs_gpio < 0) {
		printk("can't get cs-gpio");
		return -EINVAL;
	}
    ipsTftdev.nd = of_find_node_by_path("/bus@f0000/spi@20100000/display@0");
	if(ipsTftdev.nd == NULL) {
		printk("res-gpio node not find!\r\n");
		return -EINVAL;
    }

    ipsTftdev.res_gpio = of_get_named_gpio(ipsTftdev.nd, "reset-gpio", 0);
    if(ipsTftdev.res_gpio < 0) {
		printk("can't get res-gpio");
		return -EINVAL;
	}

    ipsTftdev.nd = of_find_node_by_path("/bus@f0000/spi@20100000/display@0");
	if(ipsTftdev.nd == NULL) {
		printk("ipsDcgpio node not find!\r\n");
		return -EINVAL;
    }

    ipsTftdev.dc_gpio = of_get_named_gpio(ipsTftdev.nd, "dc-gpio", 0);
    if(ipsTftdev.dc_gpio < 0) {
		printk("can't get ipsDc-gpio");
		return -EINVAL;
	}

    ipsTftdev.nd = of_find_node_by_path("/bus@f0000/spi@20100000/display@0");
    if(ipsTftdev.nd == NULL) {
        printk("ipsTftdev node not find!\r\n");
        return -EINVAL;
    }

    ret = of_property_read_u32(ipsTftdev.nd, "rotate", &ipsTftdev.rotate);
    ipsTftdev.rotation = LCD_DRV_ROTATE_0;    //默认0度旋转
    if (ret < 0) {
        printk("rotate property not found, using default: %d\n", ipsTftdev.rotate);
    } else {
        printk("rotate property found: %d\n", ipsTftdev.rotate);
    }
    if (ipsTftdev.rotate == 180)
        ipsTftdev.rotation = LCD_DRV_ROTATE_180;
    
    ipsTftdev.bl = devm_of_find_backlight(&spi->dev);
    if (IS_ERR(ipsTftdev.bl)) {
		dev_err(&spi->dev,
			"cannot register backlight device (%ld)\n",
			PTR_ERR(ipsTftdev.bl));
		return PTR_ERR(ipsTftdev.bl);
	}
    spi->dev.platform_data = (void *)ipsTftdev.bl;

    ipsTftdev.nd = of_find_node_by_path("/reserved-memory/framebuffer@8f000000");
    if(ipsTftdev.nd == NULL) {
		printk("ipsLecgpio node not find!\r\n");
		return -EINVAL;
    }

	/* 3、设置GPIO1_IO20为输出，并且输出高电平 */
	ret = gpio_direction_output(ipsTftdev.cs_gpio, 1);//失能板子上的磁力计
	if(ret < 0) {
		printk("can't set cs gpio!\r\n");
	}
    gpio_set_value(ipsTftdev.cs_gpio,1);
    ret = gpio_direction_output(ipsTftdev.res_gpio, 1);
	if(ret < 0) {
		printk("can't set res gpio!\r\n");
	}
    ret = gpio_direction_output(ipsTftdev.dc_gpio, 1);
	if(ret < 0) {
		printk("can't set dc gpio!\r\n");
	}

    ret = backlight_enable(ipsTftdev.bl);
    if (ret < 0){
        dev_info(&spi->dev, "backlight enable failed:%d\n", ret);
    }

	/*初始化spi_device */
    spi->max_speed_hz = 50000000;
	spi->mode = SPI_MODE_0;	/*MODE0，CPOL=0，CPHA=0 //出问题的地方!!!*/
    spi->bits_per_word = 8;
    spi->chip_select = 0;
	spi_setup(spi);
	ipsTftdev.private_data = spi; /* 设置私有数据 */

	/* 初始化ipsTft内部寄存器 */
	ipsTft_reginit(&ipsTftdev);
   	myfb_new(spi); //fb设备初始化		
	return 0;
}

/*
 * @description     : spi驱动的remove函数，移除spi驱动的时候此函数会执行
 * @param - client 	: spi设备
 * @return          : 0，成功;其他负值,失败
 */
static void ipsTft_remove(struct spi_device *spi)
{
    myfb_del();//注销fb

	/* 删除设备 */
	cdev_del(&ipsTftdev.cdev);
	unregister_chrdev_region(ipsTftdev.devid, ipsTft_CNT);

	/* 注销掉类和设备 */
	device_destroy(ipsTftdev.class, ipsTftdev.devid);
	class_destroy(ipsTftdev.class);

}

/* 传统匹配方式ID列表 */
static const struct spi_device_id ipsTft_id[] = {
	{"victel,gc9307", 0},  
	{}
};

/* 设备树匹配列表 */
static const struct of_device_id ipsTft_of_match[] = {
	{ .compatible = "victel,gc9307" },
	{ /* Sentinel */ }
};

/* SPI驱动结构体 */	
static struct spi_driver ipsTft_driver = {
	.probe = ipsTft_probe,
	.remove = ipsTft_remove,
	.driver = {
			.owner = THIS_MODULE,
		   	.name = "gc9307",
		   	.of_match_table = ipsTft_of_match, 
		   },
	.id_table = ipsTft_id,
};

/*
 * @description	: 驱动入口函数
 * @param 		: 无
 * @return 		: 无
 */
static int __init ipsTft_init(void)
{
	return spi_register_driver(&ipsTft_driver);
}

/*
 * @description	: 驱动出口函数
 * @param 		: 无
 * @return 		: 无
 */
static void __exit ipsTft_exit(void)
{
	spi_unregister_driver(&ipsTft_driver);
}

module_init(ipsTft_init);
module_exit(ipsTft_exit);
MODULE_LICENSE("GPL");
MODULE_AUTHOR("test");
MODULE_VERSION(DEVICE_VERSION);