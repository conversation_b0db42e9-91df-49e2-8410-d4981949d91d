/*********************************************************************
 * \file timeslot.c
 * @Author: liming
 * @Date:   2025-02-19 10:37:21
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 17:22:22
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 * V1.001    liming      2024-07-23      created!
 * V1.002    liming      2024-10-26      增加与用户层异步通信
 * V1.003    liming      2024-11-29      using fixed hardware timer0    
 * V1.004    liming      2025-07-02      增加内核IO控制接口                                                        
 *********************************************************************/

#include "linux/ktime.h"
#include "linux/timekeeping.h"
#include <linux/init.h>
#include "asm-generic/int-ll64.h"
#include "linux/dev_printk.h"
#include "linux/uaccess.h"
#include <linux/clk.h>
#include <linux/err.h>
#include <linux/kernel.h>
#include <linux/module.h>
#include <linux/mutex.h>
#include <linux/of.h>
#include <linux/of_platform.h>
#include <clocksource/timer-ti-dm.h>
#include <linux/platform_data/dmtimer-omap.h>
#include <linux/platform_device.h>
#include <linux/pm_runtime.h>
#include <linux/pwm.h>
#include <linux/slab.h>
#include <linux/time.h>
#include <linux/cdev.h>
#include <linux/device.h>
#include <linux/rpmsg.h>
#include <linux/irq.h>
#include <linux/interrupt.h>
 
#include <asm/siginfo.h>
#include <linux/pid.h>
#include <linux/uaccess.h>
#include <linux/signal.h>
#include <linux/pid_namespace.h>
#include "timeslot.h"
 
#define DEVICE_CNT      1
#define DEVICE_NAME    "timeslot"
#define DEVICE_VERSION "V1.001"
#define DM_TIMER_LOAD_MIN 0xfffffffe
#define DM_TIMER_MAX      0xffffffff
 
#define DM_INT_FLAG_OVFLOW  (1 << 1)
#define DM_INT_FLAG_MATCH   (1 << 0)
 



 typedef struct _ts_timeslot_dev
 {
     dev_t dev_id;
     int major;
     int minor;
     struct class *class;
     struct device *device;
     struct cdev cdev;
     struct rpmsg_device *rpdev; /* notice M4 */
     struct device_node *dev_nd;
 
     struct omap_dm_timer *dm_timer;
     const struct omap_dm_timer_ops *timer_ops;
     struct platform_device *timer_pdev;
     unsigned long timer_rate;
     int timer_irq_no;
 
     u32 count; /* handle for timeslot counter*/
     u32 periods; /* unit:us, hold timeslot period,for pdt/dmr - 30ms, and tetra - 15ms*/  
     u32 period_count;   /*1us timer counts*/
     ts_timeslot_type slot[SLOT_MAX];
     u32 current_work_slot;
     bool match_need_work;
 
     int work_for_pid;
    ts_process_handle process_handle;
 }ts_timeslot_dev;
 
 
 
 static ts_timeslot_dev g_timeslot;
 
 static int timeslot_open(struct inode *inode, struct file *filp)
 {
     int error = 0;
 
     filp->private_data = &g_timeslot; 
 
     return error;
 }
 
 static int timeslot_close(struct inode *inode, struct file *filp)
 {
     filp->private_data = NULL;    
 
     return 0;
 }
 
 static void timeslot_send_signal(ts_timeslot_dev *dev, int signo)
 {
     int ret = 0;
     struct kernel_siginfo info;
     struct task_struct *work_task = NULL;
 
     if(0 == dev->work_for_pid)
     {
         // dev_err(dev->device, "Pleased set pid\n");
         return;
     }
 
     memset(&info, 0, sizeof(struct kernel_siginfo));
     info.si_signo = signo;
     info.si_code = dev->count;
 
     rcu_read_lock();
     work_task = pid_task(find_vpid(dev->work_for_pid), PIDTYPE_PID);
     rcu_read_unlock();
 
     if(work_task == NULL)
     {
         // dev_err(dev->device, "get pid task failed\n");
         return;
     }
 
     ret = send_sig_info(signo, (struct kernel_siginfo *)&info, work_task);
     if(ret < 0)
     {
         // dev_err(dev->device, "send signal %d failed\n",signo);
     }
 
 
 }
 
 static u32 timeslot_subslot_next(ts_timeslot_dev *dev)
 {
     u32 next_index = SLOT_MAX;
     int i = 0;
     u32 tmp = dev->periods;
     u32 match_count;
 
     for(i = 1; i < SLOT_MAX; i++)
     {
         if(dev->slot[i].period_us != 0
          && dev->slot[i].period_us < tmp)
         {
             next_index = i;
             tmp = dev->slot[i].period_us;
          }
     }
 
     if(next_index < SLOT_MAX)
     {
         dev->match_need_work = true;
         dev->current_work_slot = next_index;
         match_count = DIV_ROUND_CLOSEST_ULL((u64)dev->timer_rate * dev->slot[next_index].period_us, USEC_PER_SEC);
         match_count = match_count + dev->period_count;
         dev->timer_ops->set_match(dev->dm_timer,true, match_count);
     }
     else  
     {
         dev->current_work_slot = SLOT_MAX;
     }
 
     return next_index;
 }

static irqreturn_t timeslot_isr(int irq, void *dev_id)
{
    ts_timeslot_dev *pts= (ts_timeslot_dev*)dev_id;
    u32 interrupt_status ;
    int signo ;

    static char first = 1;
    static ktime_t pre;
    ktime_t curr;
    ktime_t diff;

   //  if(1 == first)
   //  {
   //      curr = ktime_get();
   //      pre = ktime_get();
   //      first = 0;
   //  }

   //  curr = ktime_get();
   //  diff = ktime_sub(curr, pre);
   //  pre = curr;


    interrupt_status = pts->timer_ops->read_status(pts->dm_timer);
    pts->timer_ops->write_status(pts->dm_timer,0x7);
    if(interrupt_status & DM_INT_FLAG_OVFLOW)
    {      
        signo = SIG_SLOT;
    }
    else if(interrupt_status & DM_INT_FLAG_MATCH)
    {
        if(pts->current_work_slot < SLOT_MAX)
        {
            signo = SIG_SLOT + pts->current_work_slot;
            pts->slot[pts->current_work_slot].period_us = 0; /*disable presubslot*/
        }    
        else if(pts->current_work_slot == SLOT_MAX && pts->match_need_work == true)  
        {
            pts->timer_ops->set_match(pts->dm_timer, false,0);
            pts->match_need_work = false;
        }
        timeslot_subslot_next(pts);
    }

    if(interrupt_status & DM_INT_FLAG_OVFLOW || interrupt_status & DM_INT_FLAG_MATCH)
    {
        if(pts->process_handle)
        {
            pts->process_handle(signo);
        }
        if(signo == SIG_SLOT || signo == SIG_SUB_SLOT7)
        {
            timeslot_send_signal(pts, signo);
        }
    }
    

    return IRQ_HANDLED;
}

 int timeslot_enable(ts_timeslot_dev *dev,ts_timeslot_type *slot)
 {
 
     if(slot->slot_id >= SLOT_MAX)
     {
         dev_err(dev->device,"timeslot id wrong:%d,less than %d\n", slot->slot_id,SLOT_MAX);
         return -1;
     }
 
     if(SLOT_MAIN == slot->slot_id)
     {
         if(slot->period_us == 0) //disable timeslot
         {
             dev->timer_ops->stop(dev->dm_timer);
             dev_info(dev->device,"stop timeslot!\n");
         }
         dev->current_work_slot = SLOT_MAX;
         dev->periods = slot->period_us;
         dev->period_count = DM_TIMER_MAX - DIV_ROUND_CLOSEST_ULL((u64)dev->timer_rate * slot->period_us, USEC_PER_SEC) + 1;
         dev->timer_ops->set_load(dev->dm_timer, dev->period_count);
         dev->timer_ops->enable(dev->dm_timer);
         dev->timer_ops->write_counter(dev->dm_timer, dev->period_count);
         dev->timer_ops->set_int_enable(dev->dm_timer, DM_INT_FLAG_OVFLOW|DM_INT_FLAG_MATCH);
         dev->timer_ops->start(dev->dm_timer);
 
         dev->slot[0].slot_id = 0;
         dev->slot[0].period_us = slot->period_us;
        //  dev_err(dev->device,"enable timeslot %u count:%u\n",slot->period_us,dev->period_count);
     }
     else  
     {
         if(slot->period_us >= dev->periods)
         {
             dev_err(dev->device,"subslot(%u) MUST less than timeslot(%u)count:%u\n",slot->period_us,dev->periods,dev->period_count);
         }
         else  
         {
             dev->slot[slot->slot_id].period_us = slot->period_us;
             dev->slot[slot->slot_id].slot_id = slot->slot_id;
             // dev->match_need_work = true;
             timeslot_subslot_next(dev);
            //  dev_err(dev->device,"subslot(%d) period(%u)\n",slot->slot_id,slot->period_us);
         }
     }
 
     return 0;
 }
 
 int timeslot_timer_info(ts_timeslot_dev *dev)
 {
     unsigned long clk_rate;
     struct clk *fclk;
 
     fclk = dev->timer_ops->get_fclk(dev->dm_timer);
     if (!fclk) {
         dev_err(dev->device, "invalid pmtimer fclk\n");
         return -EINVAL;
     }
 
     clk_rate = clk_get_rate(fclk);
     if (!clk_rate) {
         dev_err(dev->device, "invalid pmtimer fclk rate\n");
         return -EINVAL;
     }
     dev->timer_rate = clk_rate / 8;
     dev->period_count = clk_rate / 1000000; /*1us timer count*/
     dev_err(dev->device, "clk rate: %luHz\n", clk_rate);    
 
     return 0;
 
 }
 static long timeslot_ioctl(struct file *filp, u32 cmd, unsigned long args)
 {
     long error = 0;
     ts_timeslot_type slot;
     ts_timeslot_dev *pts = (ts_timeslot_dev *)filp->private_data;
     struct device *dev = pts->device;
 
     switch(cmd)
     {
         case TIMESLOT_CMD_TIMESLOT_INFO:
             break;
         case TIMESLOT_CMD_ENABLE:
             if(copy_from_user(&slot, (const void *)args, sizeof(ts_timeslot_type)))
             {
                 error = -EFAULT;
                 dev_err(dev,"read userspace data error");
                 goto out;
             }
             timeslot_enable(pts, &slot);
             break;   
         case TIMESLOT_CMD_REG:
             if(copy_from_user(&pts->work_for_pid, (const void *)args, sizeof(int)))
             {
                 error = -EFAULT;
                 dev_err(dev,"read userspace data error");
                 goto out;
             }            
             break;         
         default:
             error = -EINVAL;
             dev_err(dev,"%s %d:wrong command or parameters!",__FUNCTION__,__LINE__);     
             break;            
     }
 out:
     return error;
 }

long timeslot_ioctl_kernel(struct file *filp, u32 cmd, void *args)
{
    long error = 0;
    ts_timeslot_type *slot;
    ts_timeslot_dev *pts = (ts_timeslot_dev *)filp->private_data;
    struct device *dev = pts->device;

    switch(cmd)
    {
        case TIMESLOT_CMD_TIMESLOT_INFO:
            break;
        case TIMESLOT_CMD_ENABLE:
            timeslot_enable(pts, (ts_timeslot_type *)args);
            break;   
        case TIMESLOT_CMD_REG: 
            pts->work_for_pid = *(int32_t *)args;          
            break;         
        case TIMESLOT_CMD_SET_TS_PROCESS:
            pts->process_handle = (ts_process_handle)  args;
            break;
        default:
            error = -EINVAL;
            dev_err(dev,"%s %d:wrong command or parameters!",__FUNCTION__,__LINE__);     
            break;            
    }
out:
    return error;
}
EXPORT_SYMBOL(timeslot_ioctl_kernel);
 
 ssize_t timeslot_read (struct file * filp, char __user *buf, size_t size, loff_t *offset)
 {
     int err = 0;
     ts_timeslot_dev *pts = (ts_timeslot_dev *)filp->private_data;
 
     err = copy_to_user(buf, (void*) &pts->count, 4);
 
     return 4;
 }
 
 /**
  * @brief fs operation
  * 
  */
 static const struct file_operations key_fops = {
     .owner = THIS_MODULE,
     .open =  timeslot_open,
     .read = timeslot_read,
     .release = timeslot_close,
     .unlocked_ioctl = timeslot_ioctl,
 };
 
 
 static int rpmsg_timeslot_cb(struct rpmsg_device *rpdev, void *data, int len,
                         void *priv, u32 src)
 {
     return 0;
 }
 
 static int rpmsg_timeslot_probe(struct rpmsg_device *rpdev)
 {
     g_timeslot.rpdev = rpdev;
 
     // dev_info(&rpdev->dev, "new channel: 0x%x -> 0x%x!\n",
     //                 rpdev->src, rpdev->dst);
     return 0;
 }
 
 static void rpmsg_timeslot_remove(struct rpmsg_device *rpdev)
 {
     g_timeslot.rpdev = NULL;
 
     // dev_info(&rpdev->dev, "rpmsg timeslot client driver is removed\n");
 }
 
 static struct rpmsg_device_id rpmsg_driver_timeslot_id_table[] = {
     { .name    = "timeslot" },
     { },
 };
 MODULE_DEVICE_TABLE(rpmsg, rpmsg_driver_timeslot_id_table);
 
 static struct rpmsg_driver rpmsg_timeslot_client = {
     .drv.name    = "timeslot rpmsg client",
     .id_table    = rpmsg_driver_timeslot_id_table,
     .probe       = rpmsg_timeslot_probe,
     .callback    = rpmsg_timeslot_cb,
     .remove      = rpmsg_timeslot_remove,
 };
 
 static int timeslot_probe(struct platform_device *pdev)
 {
     struct device_node *np = pdev->dev.of_node;
     struct dmtimer_platform_data *timer_pdata;
     const struct omap_dm_timer_ops *pdata;
     struct platform_device *timer_pdev;
     struct pwm_omap_dmtimer_chip *omap;
     struct omap_dm_timer *dm_timer;
     struct device_node *timer;
     int ret = 0;
     u32 v;
     ts_timeslot_dev *pts;
     struct device *dev;
 
     pts = &g_timeslot;
 
 
     //申请设备号
     pts->major = 0;
     if(pts->major)
     {
         //手动指定设备号，使用指定的设备号
         pts->dev_id = MKDEV(pts->major,0);
         ret = register_chrdev_region(pts->dev_id,DEVICE_CNT,DEVICE_NAME);
     }
     else
     {
         //设备号未指定，申请设备号
         ret = alloc_chrdev_region(&pts->dev_id,0,DEVICE_CNT,DEVICE_NAME);
         pts->major = MAJOR(pts->dev_id);
         pts->minor = MINOR(pts->dev_id);
     }
 
     if(ret<0)
     {
         goto error_out;
     }
 
     //字符设备cdev初始化
     pts->cdev.owner = THIS_MODULE;
     cdev_init(&pts->cdev,&key_fops);                 //文件操作集合映射
 
     ret = cdev_add(&pts->cdev,pts->dev_id,DEVICE_CNT);
     if(ret<0){
         //cdev初始化异常，跳转至异常处理
         goto fail_cdev;
     }
 
     //自动创建设备节点
     pts->class = class_create(THIS_MODULE,DEVICE_NAME);
     if(IS_ERR(pts->class)){
         //class创建异常处理
         printk("class error!@%s %d\r\n",__FUNCTION__,__LINE__);
         ret = PTR_ERR(pts->class);
         goto fail_class;
     }
     // printk("dev class created@%s %d\r\n",__FUNCTION__,__LINE__);
     pts->device = device_create(pts->class,NULL,pts->dev_id,NULL,DEVICE_NAME);
     dev = pts->device;
     if(IS_ERR(pts->device)){
         //设备创建异常处理
         printk("device error!@%s %d\r\n",__FUNCTION__,__LINE__);
         ret = PTR_ERR(pts->device);
         goto fail_device;
     }
 
     register_rpmsg_driver(&rpmsg_timeslot_client);
 
     timer = of_parse_phandle(np, "ti,timers", 0);
     if (!timer)
     {
         dev_err(dev, "%s %d::dts miss ti/timer\n",__FUNCTION__,__LINE__);
         ret =  -ENODEV;
         goto fail_device;
     }
 
     timer_pdev = of_find_device_by_node(timer);
     if (!timer_pdev) {
         dev_err(&pdev->dev, "Unable to find Timer pdev\n");
         ret = -ENODEV;
         goto fail_device;
     }
 
     timer_pdata = dev_get_platdata(&timer_pdev->dev);
     if (!timer_pdata) {
         dev_dbg(&pdev->dev,
              "dmtimer pdata structure NULL, deferring probe\n");
         ret = -EPROBE_DEFER;
         goto fail_device;
     }
 
     pdata = timer_pdata->timer_ops;
     pts->timer_ops = timer_pdata->timer_ops;
 
     dm_timer = pdata->request_by_node(timer);
     if (!dm_timer) {
         ret = -EPROBE_DEFER;
         goto fail_device;
     }
 
     pts->dm_timer = dm_timer;
     pts->timer_pdev = timer_pdev;
 
     /*
      * Ensure that the timer is stopped before we allow PWM core to call
      * pwm_enable.
      */
     if (pm_runtime_active(&pts->timer_pdev->dev))
         pts->timer_ops->stop(pts->dm_timer);
 
     // if (!of_property_read_u32(pdev->dev.of_node, "ti,prescaler", &v))
     //     pts->timer_ops->set_prescaler(pts->dm_timer, v);
 
     // /* setup dmtimer clock source */
     // if (!of_property_read_u32(pdev->dev.of_node, "ti,clock-source", &v))
     //     pts->timer_ops->set_source(pts->dm_timer, v);
 
     platform_set_drvdata(pdev, pts);
     timeslot_timer_info(pts);
     pts->timer_irq_no = pts->timer_ops->get_irq(pts->dm_timer);
     ret = request_irq(pts->timer_irq_no,                        
                     timeslot_isr,                                
                       IRQ_LEVEL,   
                        "timeslot",              
                         pts);     
     pts->count = 0;
     pts->current_work_slot = SLOT_MAX;
     dev_info(dev,"time slot device init ok! timer_irq=%d@%s %d ret=%d \r\n",pts->timer_irq_no,__FUNCTION__,__LINE__,ret); 
     return 0;
 
 fail_device:
     //device创建失败，意味着class创建成功，应该将class销毁
     // dev_err(dev,"device create error,class destroyed@%s %d\r\n",__FUNCTION__,__LINE__);
     class_destroy(pts->class);
 fail_class:
     //类创建失败，意味着设备应该已经创建成功，此刻应将其释放掉
     // dev_err(dev,"class create error,cdev del@%s %d\r\n",__FUNCTION__,__LINE__);
     cdev_del(&pts->cdev);
 fail_cdev:
     //cdev初始化异常，意味着设备号已经申请完成，应将其释放
     // dev_err(dev,"cdev init error,chrdev register@%s %d\r\n",__FUNCTION__,__LINE__);
     unregister_chrdev_region(pts->dev_id,DEVICE_CNT);
 error_out:
     //设备号申请异常，由于是第一步操作，不需要进行其他处理
     // dev_err(dev,"dev id error@%s %d\r\n",__FUNCTION__,__LINE__);
 
     return ret;
 }
 
 static int timeslot_remove(struct platform_device *pdev)
 {
     if(pm_runtime_active(&g_timeslot.timer_pdev->dev))
     {
         g_timeslot.timer_ops->stop(g_timeslot.dm_timer);
     }
     if(g_timeslot.timer_irq_no)
     {
         free_irq(g_timeslot.timer_irq_no, &g_timeslot);
         // printk("free irq :%d\n",g_timeslot.timer_irq_no);
     }
     g_timeslot.timer_ops->free(g_timeslot.dm_timer);
 
 
     unregister_rpmsg_driver( &rpmsg_timeslot_client );
     cdev_del(&g_timeslot.cdev);
     unregister_chrdev_region(g_timeslot.dev_id,DEVICE_CNT);
 
     if(g_timeslot.class)
     {
         device_destroy(g_timeslot.class,g_timeslot.dev_id);
         class_destroy(g_timeslot.class);
         // printk("device and class destory\n");
     }
 
     put_device(&g_timeslot.timer_pdev->dev);   
 
     return 0;
 }
 
 static const struct of_device_id timeslot_of_match[] = {
     {.compatible = "victel,timeslot"},
     {}
 };
 MODULE_DEVICE_TABLE(of, timeslot_of_match);
 
 static struct platform_driver timeslot_driver = {
     .driver = {
         .name = "timeslot",
         .of_match_table = of_match_ptr(timeslot_of_match),
     },
     .probe = timeslot_probe,
     .remove    = timeslot_remove,
 };
 module_platform_driver(timeslot_driver);
 
 MODULE_LICENSE("GPL");
 MODULE_AUTHOR("LiMing");
 MODULE_DESCRIPTION("timeslot driver");
MODULE_VERSION(DEVICE_VERSION);
 
 
 
 /* end of the file:timeslot.c*/
 
 