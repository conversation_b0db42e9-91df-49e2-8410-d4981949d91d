/*********************************************************************
 * \file sa6100lp.c
 * @Author: liming
 * @Date:   2025-05-22 11:41:27
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 16:39:54
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 *                                                              
 *********************************************************************/
#include <linux/types.h>
#include <linux/kernel.h>
#include <linux/delay.h>
#include <linux/init.h>
#include <linux/module.h>
#include <linux/errno.h>
#include <linux/gpio.h>
#include <linux/cdev.h>
#include <linux/device.h>
#include <linux/of_gpio.h>
#include <linux/semaphore.h>
#include <linux/timer.h>
#include <linux/i2c.h>
#include <asm/uaccess.h>
#include <asm/io.h>
#include "linux/slab.h"
#include "sa6100lp_reg.h"

/* 加速度计qma6100 */

#define SA6100LP_NAME "sa6100lp"
#define SA6100LP_CNT 	1
#define DEVICE_VERSION "V1.001"

struct sa6100lp_struct {
	int major;
	int minor;
	dev_t devid;
	struct device *device;
	struct class *class;
	struct cdev cdev;
	void *private_data;
};

static struct sa6100lp_struct sa6100lp_drv;

static int sa6100lp_read_regs(struct sa6100lp_struct *dev, u8 reg, void *val, u16 len)
{
	struct i2c_client *client = (struct i2c_client *)dev->private_data;
	
	struct i2c_msg msg[] = {
		{
			.addr = client->addr,
			.flags = 0,
			.buf = &reg,
			.len = 1,
		},
		{
			.addr = client->addr,
			.flags = I2C_M_RD,
			.buf = val,
			.len = len,
		}
	};

	return i2c_transfer(client->adapter, msg, 2);
}

static int sa6100lp_write_regs(struct sa6100lp_struct *dev, u8 reg, u8 *buf, u16 len)
{
	u8 b[256];
	struct i2c_client *client = (struct i2c_client *)dev->private_data;
	struct i2c_msg msg[] = {
		{
			.addr = client->addr,
			.flags = 0,
			.buf = b,
			.len = len + 1,
		},
		{
		
		}
	};

	b[0] = reg;
	memcpy(&b[1], buf, len);

	return i2c_transfer(client->adapter, msg, 1);
}


static unsigned char sa6100lp_read_one_reg(struct sa6100lp_struct *dev, u8 reg)
{
	u8 data = 0;

	sa6100lp_read_regs(dev, reg, &data, 1);

	return data;
}

static void sa6100lp_write_one_reg(struct sa6100lp_struct *dev, u8 reg, u8 data)
{
	u8 buf = data;

	sa6100lp_write_regs(dev, reg, &buf, 1);
}

static void sa6100lp_reg_init(struct sa6100lp_struct *dev)
{
	sma6100p_pm_t pm = { .raw = 0 };
	sma6100p_fsr_t fsr = { .raw = 0 };
	sma6100p_int_cfg_t int_cfg = { .raw = 0 };
	sma6100p_st_t st = { .raw = 0 };
	sma6100p_fifo_cfg0_t fifo_cfg0 = { .raw = 0 };

	pm.bit.mode_bit = 1;
	fsr.bit.range = SMA6100P_RNG_4G;
	fifo_cfg0.bit.fifo_en_x = 1;
	fifo_cfg0.bit.fifo_en_y = 1;
	fifo_cfg0.bit.fifo_en_z = 1;
	fifo_cfg0.bit.fifo_mode = SMA6100P_BYPASS;

	/* 复位SA6100LP */
	sa6100lp_write_one_reg(&sa6100lp_drv, SMA6100P_REG_SOFT_RESET, 0xB6);
	mdelay(5);
	sa6100lp_write_one_reg(&sa6100lp_drv, SMA6100P_REG_SOFT_RESET, 0x00);
	mdelay(10);
    
	sa6100lp_write_one_reg(&sa6100lp_drv, SMA6100P_REG_PM, pm.raw);
	sa6100lp_write_one_reg(&sa6100lp_drv, SMA6100P_REG_FSR, fsr.raw);
	sa6100lp_write_one_reg(&sa6100lp_drv, SMA6100P_REG_INT_CFG, int_cfg.raw);
	sa6100lp_write_one_reg(&sa6100lp_drv, SMA6100P_REG_ST, st.raw);
	mdelay(3);
	sa6100lp_write_one_reg(&sa6100lp_drv, SMA6100P_REG_FIFO_CFG0, fifo_cfg0.raw);
	
}

static int sa6100lp_open(struct inode *inode, struct file *flip)
{
    flip->private_data = &sa6100lp_drv;

	return 0;
}

static ssize_t sa6100lp_read(struct file *flip, char __user *user, size_t cnt, loff_t *offt)
{
	int ret;
	sma6100p_output_t *output = kmalloc(sizeof(sma6100p_output_t), GFP_KERNEL);
	sma6100p_accel_data_t accel_data[3] = {0};
	struct sa6100lp_struct *dev =(struct sa6100lp_struct *)flip->private_data;

	sa6100lp_read_regs(dev, SMA6100P_REG_FIFO_DATA, accel_data, 6);

	output->x = accel_data[0].bit.accel_data;
	output->x = drv_sign_extend_16(output->x, 14);

	output->y = accel_data[1].bit.accel_data;
	output->y = drv_sign_extend_16(output->y, 14);

	output->z = accel_data[2].bit.accel_data;
	output->z = drv_sign_extend_16(output->z, 14);

	ret = copy_to_user(user, output, sizeof(*output));
	return 0;
}

static int sa6100lp_release (struct inode *inode, struct file *flip)
{
	struct sa6100lp_struct *dev =(struct sa6100lp_struct *)flip->private_data;

	return 0;
}

static struct file_operations sa6100lp_fops = {
	.owner = THIS_MODULE,
	.open = sa6100lp_open,
	.read = sa6100lp_read,
	.release = sa6100lp_release,
};

static int sa6100lp_probe(struct i2c_client *client, const struct i2c_device_id *of_match_table)
{
	sa6100lp_drv.major = 0;
	if(sa6100lp_drv.major){
		sa6100lp_drv.devid = MKDEV(sa6100lp_drv.major, 0);
		register_chrdev_region(sa6100lp_drv.devid, SA6100LP_CNT, SA6100LP_NAME);
	}else{
		alloc_chrdev_region(&sa6100lp_drv.devid, 0, SA6100LP_CNT, SA6100LP_NAME);
		sa6100lp_drv.major = MAJOR(sa6100lp_drv.devid);
		sa6100lp_drv.minor = MINOR(sa6100lp_drv.devid);
	}
	
	sa6100lp_drv.cdev.owner = THIS_MODULE;
	cdev_init(&sa6100lp_drv.cdev, &sa6100lp_fops);
	cdev_add(&sa6100lp_drv.cdev, sa6100lp_drv.devid, SA6100LP_CNT);

	sa6100lp_drv.class = class_create(THIS_MODULE, SA6100LP_NAME);
	sa6100lp_drv.device = device_create(sa6100lp_drv.class, NULL, sa6100lp_drv.devid, NULL, SA6100LP_NAME);

	sa6100lp_drv.private_data = client;

	sa6100lp_reg_init(&sa6100lp_drv);
	dev_info(sa6100lp_drv.device, "chipid:%#x\n",sa6100lp_read_one_reg(&sa6100lp_drv, SMA6100P_REG_CHIP_ID));

	return 0;
}

static void sa6100lp_remove(struct i2c_client *client)
{
	device_destroy(sa6100lp_drv.class, sa6100lp_drv.devid);
	class_destroy(sa6100lp_drv.class);
	cdev_del(&sa6100lp_drv.cdev);
	unregister_chrdev_region(sa6100lp_drv.devid, SA6100LP_CNT);
}


static const struct of_device_id	sa6100lp_match_table[] = {
	{.compatible = "victel,sa6100lp"},
	{ /* Sential */}
	
};

static const struct i2c_device_id sa6100lp_id_table[] = {
	{"victel,sa6100lp", 0},
	{ /* Sential */}

};


static struct i2c_driver sa6100lp_i2c_drv = {
	.probe = sa6100lp_probe,
	.remove = sa6100lp_remove,
	.driver = {
		.owner = THIS_MODULE,
		.name = "sa6100lp",
		.of_match_table = sa6100lp_match_table,

	},
	.id_table = sa6100lp_id_table,
};


static int __init sa6100lp_init(void)
{
	int ret = 0;
	ret = i2c_add_driver(&sa6100lp_i2c_drv);

	return 0;
}

static void __exit sa6100lp_exit(void)
{
	i2c_del_driver(&sa6100lp_i2c_drv);
}

module_init(sa6100lp_init);
module_exit(sa6100lp_exit);
MODULE_AUTHOR("cfl");
MODULE_LICENSE("GPL v2");
MODULE_VERSION(DEVICE_VERSION);

