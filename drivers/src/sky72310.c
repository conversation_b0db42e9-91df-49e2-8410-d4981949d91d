/*********************************************************************
 * \file sky72310.c
 * @Author: liming
 * @Date:   2025-02-19 10:37:21
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 16:40:52
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 * V1.001    2024-06-07      liming      created!
 * V1.002    2024-07-20      liming      optimization for calculating the aux/main frequency.
 * V1.003    2024-07-22      liming      add frequncy lock detected feature
 * V1.004    2024-07-31      liming      optimization for calculating the detected frequency,and add some todo items for device tree.                                                          
 *********************************************************************/


#include "linux/device/class.h"
#include "linux/export.h"
#include "linux/kern_levels.h"
#include "linux/printk.h"
#include "linux/types.h"
#include "uapi/linux/spi/spi.h"
#include <linux/init.h>
#include <linux/kernel.h>
#include <linux/module.h>

#include <linux/fs.h>
#include <linux/cdev.h>
#include <linux/uaccess.h>


#include <linux/delay.h>
#include <linux/errno.h>
#include <linux/gpio.h>
#include <linux/of.h>
#include <linux/of_address.h>
#include <linux/of_gpio.h>
#include <asm/io.h>
#include <linux/device.h>
#include <asm/uaccess.h>
#include <linux/platform_device.h>

#include <linux/spi/spi.h>

#include <linux/math.h>

#include <asm/neon.h>
#include <linux/math64.h>

#include <linux/irq.h>
#include <linux/interrupt.h>
#include "sky72310.h"

/** todo list
1. aux pll afc
2. lock detectd
 */

/**
 * @brief
 dts 
    @code  
    SKY72300@0{
        compatible = "victel,sky72310";
        spi-max-frequency = <24000000>;
        label = "sky72310";
        reg = <0>;
        ld-gpios= <&mcu_gpio0 22 GPIO_ACTIVE_HIGH>;
    };
    @code
 */

/*
The SKY72310-11 provides an active low LD signal at Pin 4 to indicate an out-of-lock condition. 
When the phase detector is locked, the LD signal is logic high.
*/

/*define char devie name*/
#define SKY_DEV_NAME "sky72310"
#define PLL_DEV_CNT (1)

static dev_t sky72310_devno;  /*device no.*/
static struct cdev sk772310_cdev; /*hold char device information*/
static struct class *sky72310_class; /*hold created class*/
static struct device *sky72310_device; /*hold created device*/

/*sky72310 relevant functions and information */
#define DIVIDER_18BIT                    262144
#define DIVIDER_10BIT                    1024

#define DIVEDEND_REG_MSB(DivReg)        ((DivReg>>8)&0x03FF)
#define DIVEDEND_REG_LSB(DivReg)        (DivReg&0xFF)
#define DIVIDEND_REG_10_BITS(DivReg)     (DivReg&0x03ff)

#define SKY7K_ADDR_MASK 0xf000
#define SKY7K_ADDR_SHIT  12

#define SKY7K_DATA_MASK 0x0fff

#define SKY72310_MAIN_REF_FRQ_DIVIDER_MASK 0x001f
#define SKY72310_MAIN_REF_FRQ_DIVIDER_SHIFT   0
#define SKY72310_AUX_REF_FRQ_DIVIDER_MASK   0x07c0
#define SKY72310_AUX_REF_FRQ_DIVIDER_SHIFT   6

#define SKY72310_MAIN_LOCK_DETECT_MASK 0x0020
#define SKY72310_MAIN_LOCK_DETECT_SHIT  5
#define SKY72310_AUX_LOCK_DETECT_MASK 0x0800
#define SKY72310_AUX_LOCK_DETECT_SHIT  11

#define SKY7K_REG_MAKER(regAddr,value)    ((u16)(((regAddr << SKY7K_ADDR_SHIT) & SKY7K_ADDR_MASK) | (value & SKY7K_DATA_MASK)))


typedef enum _te_sky72310_reg
{
    MDR   = 0,//< main divider register
    MDEMR = 1,//<main dividend msb register
    MDELR = 2,//<main dividend lsb register
    ADR   = 3,//<auxiliary divider register
    ADER  = 4,//<auxiliary dividend register
    RFDR  = 5,//<reference frequency divider register
    CPCR  = 6,//<charg pump control register
    MOSCR = 7,//<multiplexer output select control register
    MCR   = 8, //< modulation control register
    MODDR = 9, //<modulation data register
    SKY72310_REG_NUM,
}te_sky72310_reg;

typedef struct _ts_sky72310_info
{
    u32 foscin; //todo:设备树配置，默认19.2MHz

    u32 main_freq;
    u32 aux_freq;

    u32 main_phase_detect_freq;//todo:设备树配置
    u32 main_middle_freq; //todo:设备树配置
    u32 main_afc_coef;    


    u32 aux_phase_detect_freq;//todo:设备树配置
    u32 aux_middle_freq;//todo:设备树配置
    u32 aux_afc_coef;

    u16 main_chargepump;  //todo:设备树配置  
    u16 aux_chargepump;   //todo:设备树配置

    u8 main_freq_locked; /* 0 - unlocked  1 - locked*/
    u8 aux_freq_locked;  /* 0 - unlocked  1 - locked*/
}ts_sky72310_info;


typedef struct _ts_sky72310_ld_irq_desc
{
    struct gpio_desc *ld;
    int gpio;                        
    int irqnum;  
    char name[10];                      
}ts_sky72310_ld_irq_desc;

typedef struct _ts_sk72310_obj 
{
    struct device_node *np;
    struct spi_device *spi;
    ts_sky72310_ld_irq_desc lockd;
    u16 sky72310_regs[SKY72310_REG_NUM];
    ts_sky72310_info sky72310_info;
}ts_sk72310_obj;

static ts_sk72310_obj sky72310_hanlder;

static int sky72310_write_reg(struct spi_device *spi_device,u8 addr, u16 value)
{
    int error = -1;
    u16 reg = 0;
    u16 regtmp = 0;
    struct spi_message *message;   //定义发送的消息
    struct spi_transfer *transfer; //定义传输结构体

    if(addr < SKY72310_REG_NUM)
    {
        sky72310_hanlder.sky72310_regs[addr] = value;
    }
    else 
    {
        printk(KERN_ERR "%s %d: write error %02x : %04x - addr error! \n",__FUNCTION__,__LINE__,addr,value);
        error = -2;
        goto error_out;
    }

    regtmp = SKY7K_REG_MAKER(addr, value);

    /*低字节先出，因此需要将地址放到低八位，数据放到高8位*/
    reg = (regtmp >> 8) & 0xff;
    reg |= (regtmp & 0xff) <<8;
    /*申请空间*/
    message = kzalloc(sizeof(struct spi_message), GFP_KERNEL);
    transfer = kzalloc(sizeof(struct spi_transfer), GFP_KERNEL);

    if(message != 0 && transfer !=0 )
    {
        /*填充message和transfer结构体*/
        transfer->tx_buf = &reg;
        transfer->len = 2;
        spi_message_init(message);
        spi_message_add_tail(transfer, message);

        error = spi_sync(spi_device, message);
        kfree(message);
        kfree(transfer);
        if (error != 0)
        {
            error = -3;
            printk(KERN_ERR "%s %d: write error %02x : %04x! \n",__FUNCTION__,__LINE__,addr,value);
        }
        else 
        {
            printk(KERN_INFO "%s %d: %02x : %04x! \n",__FUNCTION__,__LINE__,addr,value);
        }
    }
    else 
    {
        error = -4;
         printk(KERN_ERR "%s %d: allcate memory error %02x : %04x! \n",__FUNCTION__,__LINE__,addr,value);
    }

error_out:
    return error;
}

static u16 sky72310_read_reg(u8 addr)
{
    u16 reg = 0xffff;

    if(addr < SKY72310_REG_NUM)
    {
        reg = sky72310_hanlder.sky72310_regs[addr];
    }

    return reg;
}

static void sky72310_regs_print(void)
{
    int i = 0;

    pr_alert("\r\nsky72310 regs:");
    for(i = 0; i < SKY72310_REG_NUM;i++)
    {
        pr_alert("%d:%04x ",i,(u32)sky72310_read_reg(i));
    }
    pr_alert("\r\n");
}

static void sky72310_init(void)
{
    int i = 0;

    sky72310_hanlder.sky72310_regs[0] = SKY7K_REG_MAKER(0,0x33);
    sky72310_hanlder.sky72310_regs[1] = SKY7K_REG_MAKER(1,0x56);
    sky72310_hanlder.sky72310_regs[2] = SKY7K_REG_MAKER(2,0x155);
    sky72310_hanlder.sky72310_regs[3] = SKY7K_REG_MAKER(3,0x24);
    sky72310_hanlder.sky72310_regs[4] = SKY7K_REG_MAKER(4,0x35);    
    sky72310_hanlder.sky72310_regs[5] = SKY7K_REG_MAKER(5,0x63);//<crstal frequency is 19.2MHz,4.8MHz phase dected frequency
    sky72310_hanlder.sky72310_regs[6] = SKY7K_REG_MAKER(6,0x79E);//<1uA rx and tx the same pumper current    sky72310_hanlder.sky72310_regs[7] = SKY7K_REG_MAKER(7,0x100);
    sky72310_hanlder.sky72310_regs[7] = SKY7K_REG_MAKER(7,0x100);//<fmain_ref mux out ///0x8
    sky72310_hanlder.sky72310_regs[8] = SKY7K_REG_MAKER(8,0);
    sky72310_hanlder.sky72310_regs[9] = SKY7K_REG_MAKER(9,0);

    for(i = 0;i < SKY72310_REG_NUM; i++)
    {
        sky72310_write_reg(sky72310_hanlder.spi, i, sky72310_hanlder.sky72310_regs[i]);
    }

    sky72310_hanlder.sky72310_info.aux_afc_coef  = 84618;//< need calibaration 470MHz - 84618
    sky72310_hanlder.sky72310_info.main_afc_coef = 84618;//< need calibaration 470MHz - 84618

    sky72310_hanlder.sky72310_info.main_chargepump = 0x3f;
    sky72310_hanlder.sky72310_info.aux_chargepump  = 0x1e;

    sky72310_hanlder.sky72310_info.main_phase_detect_freq = 4800000;
    sky72310_hanlder.sky72310_info.aux_phase_detect_freq  = 4800000;
}


static void sky72310_main_freq_set(u32 freq)
{
    u16 Nreg;
    int Dividend;
    long long detaD;
    long long uintDectect;
    u32 detectFreq;

    detectFreq = sky72310_hanlder.sky72310_info.main_phase_detect_freq;

    Nreg  = freq / detectFreq;
    detaD = (long long)(freq - Nreg*detectFreq) << 16;
    uintDectect = (long long)((long long)detectFreq << 16) / DIVIDER_18BIT;

    Dividend = detaD / uintDectect;
    Nreg = Nreg - 32;

    sky72310_hanlder.sky72310_regs[MDR]   = SKY7K_REG_MAKER(MDR,Nreg);
    sky72310_hanlder.sky72310_regs[MDEMR] = SKY7K_REG_MAKER(MDEMR,DIVEDEND_REG_MSB(Dividend));
    sky72310_hanlder.sky72310_regs[MDELR] = SKY7K_REG_MAKER(MDELR,DIVEDEND_REG_LSB(Dividend));

    sky72310_write_reg(sky72310_hanlder.spi, MDR, sky72310_hanlder.sky72310_regs[MDR]);
    sky72310_write_reg(sky72310_hanlder.spi, MDEMR, sky72310_hanlder.sky72310_regs[MDEMR]);
    sky72310_write_reg(sky72310_hanlder.spi, MDELR, sky72310_hanlder.sky72310_regs[MDELR]);
}
    

static void sky72310_aux_freq_set(u32 freq)
{
    u16 Nreg;
    int Dividend;
    long long detaD;
    long long uintDectect;
    u32 detectFreq;

    detectFreq = sky72310_hanlder.sky72310_info.aux_phase_detect_freq;

    Nreg  = freq / detectFreq;
    detaD = (long long)(freq - Nreg*detectFreq) << 16;
    uintDectect = (long long)((long long)detectFreq << 16) / DIVIDER_10BIT;

    Dividend = detaD / uintDectect;
    Nreg = Nreg - 32;   

    sky72310_hanlder.sky72310_regs[ADR]  = SKY7K_REG_MAKER(ADR,Nreg);
    sky72310_hanlder.sky72310_regs[ADER] = SKY7K_REG_MAKER(ADER,DIVIDEND_REG_10_BITS(Dividend));

    sky72310_write_reg(sky72310_hanlder.spi, ADR, sky72310_hanlder.sky72310_regs[ADR]);
    sky72310_write_reg(sky72310_hanlder.spi, ADER, sky72310_hanlder.sky72310_regs[ADER]);

}

static void sky72310_chargepump_set(u32 cp)
{
    sky72310_hanlder.sky72310_regs[CPCR]  = SKY7K_REG_MAKER(CPCR,cp);

    /** !!IMPORTANT!! - Charge pump sequene :MOSCR -> CPCR -> RFDR*/
    sky72310_write_reg(sky72310_hanlder.spi, MOSCR, sky72310_hanlder.sky72310_regs[MOSCR]);
    sky72310_write_reg(sky72310_hanlder.spi, CPCR, sky72310_hanlder.sky72310_regs[CPCR]);
    sky72310_write_reg(sky72310_hanlder.spi, RFDR, sky72310_hanlder.sky72310_regs[RFDR]);

}

static int sky72310_open(struct inode *inode, struct file *filp)
{
    // sky72310_init();  //move to probe
    return 0;
}

/*dummy funciton for write function*/
static ssize_t sky72310_write(struct file *filp, const char __user *buf, size_t cnt, loff_t *off)
{
    return 0;
}

static int sky72310_close(struct inode *inode, struct file *filp)
{
    return 0;
}

static long sky72310_ioctl(struct file *filp, u32 cmd, unsigned long args)
{
    long error = 0;

    // if(_IOC_TYPE(cmd) != SKY72310_CMD_MAGIC)
    // {
    //     error = -EINVAL;
    //     printk( KERN_ERR "%s %d:wrong command or parameters!",__FUNCTION__,__LINE__);
    //     goto error_out;
    // }

    switch(cmd)
    {
        case SKY72310_CMD_READ_REGS:
            sky72310_regs_print();
            break;
        case SKY72310_CMD_SET_MAIN_FREQ:
            if(copy_from_user(&(sky72310_hanlder.sky72310_info.main_freq), (u32*)args, sizeof(u32)))
            {
                error = -EFAULT;
                printk( KERN_ERR "%s %d:set main freq error!",__FUNCTION__,__LINE__);
                goto error_out;                     
            }
            sky72310_main_freq_set(sky72310_hanlder.sky72310_info.main_freq);
            break;
        case SKY72310_CMD_SET_AUX_FREQ:
            if(copy_from_user(&(sky72310_hanlder.sky72310_info.aux_freq), (u32*)args, sizeof(u32)))
            {
                error = -EFAULT;
                printk( KERN_ERR "%s %d:set aux freq error!",__FUNCTION__,__LINE__);
                goto error_out;                     
            }        
            sky72310_aux_freq_set(sky72310_hanlder.sky72310_info.aux_freq);
            break;
        case SKY72310_CMD_MAIN_FAST_CHARGE_PUMP:
            if(copy_from_user(&(sky72310_hanlder.sky72310_info.main_chargepump), (u16*)args, sizeof(u16)))
            {
                error = -EFAULT;
                printk( KERN_ERR "%s %d:set main charge pump error!",__FUNCTION__,__LINE__);
                goto error_out;                     
            }        
            sky72310_chargepump_set(sky72310_hanlder.sky72310_info.main_chargepump);
            break;
        case SKY72310_CMD_AUX_FAST_CHARGE_PUMP:
            if(copy_from_user(&(sky72310_hanlder.sky72310_info.aux_chargepump), (u16*)args, sizeof(u16)))
            {
                error = -EFAULT;
                printk( KERN_ERR "%s %d:set aux charge pump error!",__FUNCTION__,__LINE__);
                goto error_out;                     
            }        
            sky72310_chargepump_set(sky72310_hanlder.sky72310_info.aux_chargepump);
            break;
        case SKY72310_CMD_FREQ_DETECTED_READ:
            if(copy_to_user((u8*)args, &(sky72310_hanlder.sky72310_info.main_freq_locked),  sizeof(u8)))
            {
                error = -EFAULT;
                printk( KERN_ERR "%s %d:get frequency locked flag failed!",__FUNCTION__,__LINE__);
                goto error_out;                     
            }           
            break;
        default:
            error = -EINVAL;
            printk( KERN_ERR "%s %d:wrong command or parameters!",__FUNCTION__,__LINE__);
            goto error_out;        
            break;
    }

error_out :
    return error;
}

static struct file_operations sky72310_fops = {
    .owner = THIS_MODULE,
    .open = sky72310_open,
    .write = sky72310_write,
    .release = sky72310_close,
    .unlocked_ioctl = sky72310_ioctl,
};

//todo: 如何区分aux和main，需要研究。由于暂时只有main使用，因此，锁定指示将所有频率都锁定或矢锁。
static irqreturn_t ld_handle_irq(int irq, void *dev_id)
{
    ts_sk72310_obj *dev = (ts_sk72310_obj *)dev_id;
    int value;

    value = gpio_get_value(dev->lockd.gpio);
    if(1 == value)
    {
        sky72310_hanlder.sky72310_info.main_freq_locked = 1;
        sky72310_hanlder.sky72310_info.aux_freq_locked = 1;
        printk( KERN_ERR "\r\n%s %d:%u locked!\r\n",__FUNCTION__,__LINE__,sky72310_hanlder.sky72310_info.main_freq);
    }
    else 
    {
        sky72310_hanlder.sky72310_info.main_freq_locked = 0;
        sky72310_hanlder.sky72310_info.aux_freq_locked = 0;
        printk( KERN_ERR "\r\n%s %d:%u unlocked!\r\n",__FUNCTION__,__LINE__,sky72310_hanlder.sky72310_info.main_freq);
    }
    return IRQ_HANDLED;
}

static int sky72310_drv_probe(struct spi_device *spi)
{
    int error = -1;
    int gpiold = 0;
    enum of_gpio_flags flags;

    sky72310_hanlder.np = of_find_node_by_path("/bus@f0000/bus@4000000/spi@4b00000/SKY72310@0");
    if( NULL == sky72310_hanlder.np)
    {
        error = -2;
        printk( KERN_ERR "\r\n%s %d:device tree node find fail!\r\n",__FUNCTION__,__LINE__);
        goto error_out;
    }

    /*init spi*/
    sky72310_hanlder.spi = spi;
    sky72310_hanlder.spi->mode = SPI_MODE_0;
    sky72310_hanlder.spi->max_speed_hz = 2000000;
    spi_setup(sky72310_hanlder.spi);

    /*init lock dected gpio */
    gpiold = of_get_named_gpio(sky72310_hanlder.np, "ld-gpios", 0);
    if (gpiold < 0) 
    {
        printk( KERN_ERR "\r\n%s %d:of_get_named_gpio_flags fail!\r\n",__FUNCTION__,__LINE__);
        goto error_out;
    }
    printk( KERN_ERR "\r\n%s %d:of_get_named_gpio_flags pass! - %d \r\n",__FUNCTION__,__LINE__,gpiold);


    error = gpio_request(gpiold, NULL);
    if (error) 
    {
        printk( KERN_ERR "\r\nFailed to request %s GPIO%d\r\n", "ld-gpios", gpiold);
        error = -EBUSY;
        goto error_out;
    }

    sky72310_hanlder.lockd.ld = gpio_to_desc(gpiold);
    sky72310_hanlder.lockd.gpio = gpiold;
    sky72310_hanlder.lockd.irqnum = gpio_to_irq(gpiold);
    gpio_direction_input(gpiold);
    error = request_irq(sky72310_hanlder.lockd.irqnum,                        
                    ld_handle_irq,                                
                      IRQ_TYPE_EDGE_RISING|IRQ_TYPE_EDGE_FALLING,   
                       "sky72310_ld_irq",              
                        &sky72310_hanlder);   
    if(error)
    {
        printk(KERN_ERR "\r\nFailed to request IRQ %s GPIO%d\r\n", "ld-gpios", gpiold);
        goto error_out;
    } 
    printk( KERN_ERR "\r\n%s %d:ld-gpios init pass! - GPIO%d irqnum:%d\r\n",__FUNCTION__,__LINE__,gpiold,sky72310_hanlder.lockd.irqnum);

    /*register char device*/
    error = alloc_chrdev_region(&sky72310_devno, 0, PLL_DEV_CNT, SKY_DEV_NAME);
    if(error < 0)
    {
        printk( KERN_ERR "\r\n%s %d:alloc device no fail!\r\n",__FUNCTION__,__LINE__);
        goto error_out;
    }

    /*attach file operation and init char device*/
    sk772310_cdev.owner = THIS_MODULE;
    cdev_init(&sk772310_cdev, &sky72310_fops);

    /*add to char device*/
    error = cdev_add(&sk772310_cdev, sky72310_devno, PLL_DEV_CNT);
    if(error < 0)
    {
        printk( KERN_ERR "%s %d:register char device fial!",__FUNCTION__,__LINE__);
        goto error_add;        
    }

    /*register class*/
    sky72310_class = class_create(THIS_MODULE, SKY_DEV_NAME);
    sky72310_device = device_create(sky72310_class, NULL, sky72310_devno, NULL, SKY_DEV_NAME);

    error = 0;

    /*init sky72310*/
    sky72310_init();
error_add:
    unregister_chrdev_region(sky72310_devno, PLL_DEV_CNT);

error_out:
    return error;
}

static void sky72310_remove(struct spi_device *spi)
{
    free_irq(sky72310_hanlder.lockd.irqnum, &sky72310_hanlder);
    gpio_free(sky72310_hanlder.lockd.gpio);
    /*delete device */
    device_destroy(sky72310_class, sky72310_devno);           //delete device
    class_destroy(sky72310_class);                       //delete class
    cdev_del(&sk772310_cdev);                       // release device no
    unregister_chrdev_region(sky72310_devno, PLL_DEV_CNT); //deattached char device
}

/*指定 ID 匹配表*/
static const struct spi_device_id sky72310_device_id[] = {
    {"victel,sky72310", 0},
    {}
};

/*指定设备树匹配表*/
static const struct of_device_id sky72310_of_match_table[] = {
    {.compatible = "victel,sky72310"},
    {}
};
MODULE_DEVICE_TABLE(of,sky72310_of_match_table);
/*spi 总线设备结构体*/
struct spi_driver sky72310_spi_driver = {
    .probe = sky72310_drv_probe,
    .remove = sky72310_remove,
    // .id_table = sky72310_device_id,
    .driver = {
        .name = "sky72310",
        .owner = THIS_MODULE,
        .of_match_table = sky72310_of_match_table,
    },
};


static int __init sky72310_driver_init(void)
{
    int error = -1;
    error = spi_register_driver(&sky72310_spi_driver);
    pr_info("%s %d:%d\n",__FUNCTION__,__LINE__,error);

    return error;
}

static void __exit sky72310_driver_exit(void)
{
    pr_info("%s %d\n",__FUNCTION__,__LINE__);
    spi_unregister_driver(&sky72310_spi_driver);
}

module_init(sky72310_driver_init)
module_exit(sky72310_driver_exit)



MODULE_LICENSE("GPL");
MODULE_AUTHOR("liming");
MODULE_DESCRIPTION("sky72310 driver");


/*end of the file:sk72310.c*/
