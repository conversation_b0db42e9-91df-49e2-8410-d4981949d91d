/*********************************************************************
 * \file prinfo.c
 * @Author: liming
 * @Date:   2025-02-19 10:37:21
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 16:39:41
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 *                                                              
 *********************************************************************/

#include "asm-generic/int-ll64.h"
#include "linux/export.h"
#include <linux/module.h>
#include <linux/kernel.h>
#include <linux/init.h>
#include <linux/fs.h>
#include <linux/uaccess.h>
#include <linux/io.h>
#include <linux/types.h>
#include <linux/cdev.h>
#include <linux/device.h>
#include <linux/ioctl.h>
#include <fcntl.h>
#include <sys/ioctl.h>

#include "prinfo.h"

#include <mtd/mtd-user.h>

#define PRINFO_DEVICE_CNT     1
#define PRINFO_DEVICE_NAME    "prinfo"
#define PRINFO_VERSION        "v1.01"

#define PRINFO_PRODUCT_PATH "/dev/mtd4"
#define PRINFO_RADIO_PATH   "/dev/mtd5"

typedef struct _ts_prinfo_dev
{
    char *name;
    char *version;
    int dev_cnt;
    dev_t dev_id;
    int major;
    int minor;
    struct class *class;
    struct device *device;
    struct cdev cdev;
    ts_product_info product_info;
    ts_radio_info radion_info;
    int product_info_fd;
    int radio_info_fd;

}ts_prinfo_dev;


static ts_prinfo_dev prinfo_dev = 
{
    .name = PRINFO_DEVICE_NAME,
    .version = PRINFO_VERSION,
    .dev_cnt = PRINFO_DEVICE_CNT,
};

static char * build_info_get(void)
{
    return PRINFO_VERSION;
}


static int prinfo_mtd_info(void)
{
    mtd_info_t meminfo;

    ioctl(prinfo_dev.product_info_fd, MEMGETINFO, &meminfo);
    // printk("")

}

static int prinfo_dev_open(struct inode *inode, struct file *filp)
{
    int error = 0;
    struct stat st;

    prinfo_dev.product_info_fd = open(PRINFO_PRODUCT_PATH,O_RDWR);
    prinfo_dev.product_info_fd = open(PRINFO_RADIO_PATH,O_RDWR);

    if(prinfo_dev.product_info_fd < 0 || prinfo_dev.radio_info_fd < 0)
    {
        printk("\r\n open(radio/product information) fail:%d,%d\r\n"
                                                        ,prinfo_dev.product_info_fd
                                                        ,prinfo_dev.radio_info_fd);
        error = -1;
    }



    return error;
}

static int prinfo_dev_close(struct inode *inode, struct file *filp)
{
    int error = 0;

    if(prinfo_dev.product_info_fd > 0)
    {
        close(prinfo_dev.product_info_fd);
    }

    if(prinfo_dev.radio_info_fd > 0)
    {
        close(prinfo_dev.radio_info_fd);
    }    

    return error;
}

static long prinfo_ioctl(struct file *filp,u32 cmd, unsigned long args)
{
    long error = 0;

    switch(cmd)
    {
        case PRINFO_CMD_PRINT:
            break;
        case PRINFO_CMD_RADIO_INFO_READ :
            if(copy_to_user((unsigned char *)args, (unsigned char*)(&prinfo_dev.radion_info), sizeof(ts_radio_info)))
            {
                error = -EFAULT;
            }
            break;
        case PRINFO_CMD_PRODUCT_INFO_READ :
            if(copy_to_user((unsigned char *)args, (unsigned char*)(&prinfo_dev.product_info), sizeof(ts_product_info)))
            {
                error = -EFAULT;
            }        
            break;
        case PRINFO_CMD_RADIO_INFO_WRITE :
            if(copy_from_user((unsigned char*)(&prinfo_dev.radion_info), (unsigned char *)args, sizeof(ts_radio_info)))
            {
                error = -EFAULT;
            }
            break;
        case PRINFO_CMD_PRODUCT_INFO_WRITE :
            if(copy_from_user((unsigned char*)(&prinfo_dev.product_info), (unsigned char *)args, sizeof(ts_product_info)))
            {
                error = -EFAULT;
            }
            break;
        default:
            error = -EINVAL;
            printk( KERN_ERR "%s %d:wrong command or parameters!",__FUNCTION__,__LINE__);   
            break;
    }


    return error;
}

static const struct file_operations prinfo_ops = 
{
    .owner = THIS_MODULE,
    .open = prinfo_dev_open,
    .release = prinfo_dev_close,
    .unlocked_ioctl = prinfo_ioctl,
};

static int __init prinfo_init(void)
{
    int ret = 0;
    ts_prinfo_dev *dev;

    dev = &prinfo_dev;

    /*1st. request dev id*/
    dev->major = 0;
    ret = alloc_chrdev_region(&dev->dev_id,0,dev->dev_cnt,dev->name);
    dev->major = MAJOR(dev->dev_id);
    dev->minor = MINOR(dev->dev_id);

    if(ret < 0)
    {
        goto error_out;
    }


    /*2nd. init cdev*/
    dev->cdev.owner = THIS_MODULE;
    cdev_init(&dev->cdev, &prinfo_ops);

    ret = cdev_add(&dev->cdev, dev->dev_id, PRINFO_DEVICE_CNT);
    if(ret < 0)
    {
        goto fail_cdev;
    }

    /*3rd. create dev under "/dev"*/
    dev->class = class_create(THIS_MODULE,dev->name);
    if(IS_ERR(dev->class)){
        //class创建异常处理
        printk("class error!@%s %d\r\n",__FUNCTION__,__LINE__);
        ret = PTR_ERR(dev->class);
        goto fail_class;
    }
    printk("dev class created@%s %d\r\n",__FUNCTION__,__LINE__);
    dev->device = device_create(dev->class,NULL,dev->dev_id,NULL,dev->name);
    if(IS_ERR(dev->device)){
        //设备创建异常处理
        printk("device error!@%s %d\r\n",__FUNCTION__,__LINE__);
        ret = PTR_ERR(dev->device);
        goto fail_device;
    }
    printk("%s module install(/dev/prinfo) ok!",dev->name);

    return ret;

fail_device:
    //device创建失败，意味着class创建成功，应该将class销毁
    printk("device create error,class destroyed@%s %d\r\n",__FUNCTION__,__LINE__);
    class_destroy(dev->class);
fail_class:
    //类创建失败，意味着设备应该已经创建成功，此刻应将其释放掉
    printk("class create error,cdev del@%s %d\r\n",__FUNCTION__,__LINE__);
    cdev_del(&dev->cdev);
fail_cdev:
    //cdev初始化异常，意味着设备号已经申请完成，应将其释放
    printk("cdev init error,chrdev register@%s %d\r\n",__FUNCTION__,__LINE__);
    unregister_chrdev_region(dev->dev_id,dev->dev_cnt);
error_out:
    printk("%s module init error %d@%s %d\r\n",dev->name, ret, __FILE__, __LINE__);
    return ret;
}

static void __exit prinfo_exit(void)
{
    ts_prinfo_dev *dev;
    dev = &prinfo_dev;

    cdev_del(&dev->cdev);
    unregister_chrdev_region(dev->dev_id,dev->dev_cnt);

    printk("\r\n %s module uninstall @%s %d\r\n",dev->name,__FUNCTION__,__LINE__);
}

module_init(prinfo_init);
module_exit(prinfo_exit);

MODULE_LICENSE("GPL");
MODULE_AUTHOR("LiMing");
MODULE_DESCRIPTION("product and radio inforamtion process driver");
MODULE_VERSION(PRINFO_VERSION);


/*end of the file:prinfo.c*/
