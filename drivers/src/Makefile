CURRENT_PATH := $(shell pwd)/src
RELEASE_PATH := $(shell pwd)/release/
INC_PATH     := $(shell pwd)/release/include/
BUILD_PATH :=  $(shell pwd)/BUILD

obj-m := als.o gc9307.o sa6100lp.o sc5883lp.o dac124S085.o timeslot.o ak2401a.o jl7016g.o rfcontrol.o 
# rfcontrols-objs := rfcontrol.o  rfvsnd.o
# 如果一个模块有多个源文件，可以参考rfcontrols模块。rfcontrols为模块名称 最后生成rfcontrols.ko,包含两个文件 rfcontrol.c rfvsnd.c, 编译目标命名按模块-objs，
# make系统会自动搜索，rfcontrols-objs。注意，模块名称不能和源文件编译对应名称一样。要有区分。

drivers:
	@echo $(OBJ_FILES)
	$(MAKE) -j $(MAKE_JOBS) -C $(LINUXKERNEL_INSTALL_DIR) M=$(CURRENT_PATH) ARCH=arm64 CROSS_COMPILE=$(CROSS_COMPILE) modules EXTRA_CFLAGS="-I$(INC_PATH) -I$(CURRENT_PATH) -Wno-all" 
	@cp $(CURRENT_PATH)/*.ko $(RELEASE_PATH)
	@rm -f $(CURRENT_PATH)/*.cmd $(CURRENT_PATH)/*.o $(CURRENT_PATH)/*.mod $(CURRENT_PATH)/*.order $(CURRENT_PATH)/*.mod.* $(CURRENT_PATH)/*.symvers.*
	@rm -f $(CURRENT_PATH)/*.ko.* $(CURRENT_PATH)/.*.*.cmd $(CURRENT_PATH)/*.symvers
	@rm -f $(CURRENT_PATH)/*.ko
	@echo Done!

.PHONY: clean
clean :
	rm *.ko
	rm *.o
