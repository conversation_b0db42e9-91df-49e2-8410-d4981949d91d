/*********************************************************************
 * \file rdebuglog.c
 * @Author: liming
 * @Date:   2025-02-19 10:37:21
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 16:39:53
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 *                                                              
 *********************************************************************/

#include "asm-generic/int-ll64.h"
#include "linux/device/class.h"
#include "linux/err.h"
#include "linux/export.h"
#include "linux/kdev_t.h"
#include "linux/printk.h"
#include "linux/sched.h"
#include <linux/module.h>
#include <linux/kernel.h>
#include <linux/init.h>
#include <linux/fs.h>
#include <linux/uaccess.h>
#include <linux/io.h>
#include <linux/types.h>
#include <linux/cdev.h>
#include <linux/device.h>
#include <linux/of.h>
#include <linux/of_address.h>
#include <linux/of_irq.h>
#include <linux/ioctl.h>
#include <linux/rpmsg.h>


#include <linux/kthread.h>
#include <linux/kfifo.h>
#include <linux/delay.h>

#define RDEBUG_LOG_DEVICE_CNT   1
#define RDEBUG_LOG_DEVICE_NAME "rdebuglog"
#define RDEBUG_LOG_FIFO_SIZE 65536

static char rdebug_print_buffer[RDEBUG_LOG_FIFO_SIZE];

typedef struct _ts_rdebuglog_dev 
{
    dev_t dev_id;
    int major;
    int minor;
    struct class *class;
    struct device *device;
    struct cdev cdev;
    struct rpmsg_device *rpdev;
    struct task_struct *thread;
    struct kfifo fifo;
}ts_rdebuglog_dev;

static ts_rdebuglog_dev rdebuglog_dev;

static int rdebuglog_process_handler(void* args)
{
    unsigned int size_read = 0;
    while(!kthread_should_stop())
    {
        if(kfifo_is_empty(&rdebuglog_dev.fifo))
        {
            msleep(150);
            continue;
        }
        memset(rdebug_print_buffer, 0, RDEBUG_LOG_FIFO_SIZE);
        size_read = kfifo_out(&rdebuglog_dev.fifo, rdebug_print_buffer, RDEBUG_LOG_FIFO_SIZE);
        printk("%s",rdebug_print_buffer);
        msleep(50);
    }

    return 0;
}


static int rpmsg_rdebuglog_cb(struct rpmsg_device *rpdev, void *data, int len,
                        void *priv, u32 src)
{
    if(kfifo_is_full(&rdebuglog_dev.fifo))
    {
        printk("%s:fifo is full!\n",RDEBUG_LOG_DEVICE_NAME);
    }

    if( kfifo_avail(&rdebuglog_dev.fifo) > len)
    {
        kfifo_in(&rdebuglog_dev.fifo, data, len);
    }
    else 
    {
        printk("%s:fifo rest less than recieved!\n",RDEBUG_LOG_DEVICE_NAME);
    }

    return 0;
}

static int rpmsg_rdebuglog_probe(struct rpmsg_device *rpdev)
{
    int ret = 0;
    rdebuglog_dev.rpdev = rpdev;

    dev_info(&rpdev->dev, "new channel for rdebuglog: 0x%x -> 0x%x!\n",rpdev->src, rpdev->dst);

    /* MUST send msg for M4/R5 need to accepted linux end point. */
    ret = rpmsg_send( rdebuglog_dev.rpdev->ept, "ok", 2);
    if (ret) {
        dev_err(&rdebuglog_dev.rpdev->dev, "rpmsg_send failed: %d\n", ret);
    }
    return 0;
}

static void rpmsg_rdebuglog_remove(struct rpmsg_device *rpdev)
{
    rdebuglog_dev.rpdev = NULL;

    dev_info(&rpdev->dev, "rpmsg rdebuglog client driver is removed\n");
}

static struct rpmsg_device_id rpmsg_driver_rpmsg_id_table[] = {
    { .name    = "rdebuglog" },
    { },
};
MODULE_DEVICE_TABLE(rpmsg, rpmsg_driver_rpmsg_id_table);

static struct rpmsg_driver rpmsg_rdebuglog_client = {
    .drv.name    = "rdebuglog client",
    .id_table    = rpmsg_driver_rpmsg_id_table,
    .probe       = rpmsg_rdebuglog_probe,
    .callback    = rpmsg_rdebuglog_cb,
    .remove      = rpmsg_rdebuglog_remove,
};


static int __init rdebuglog_init(void)
{
    int ret;

    /*1st: define device id*/
    rdebuglog_dev.major = 0;
    ret = alloc_chrdev_region(&rdebuglog_dev.dev_id, 0, RDEBUG_LOG_DEVICE_CNT, RDEBUG_LOG_DEVICE_NAME);
    if(ret < 0)
    {
        printk("%s define device id error:%d@%s %d\r\n",RDEBUG_LOG_DEVICE_NAME,ret,__FUNCTION__,__LINE__);
        goto error_out;
    }

    rdebuglog_dev.major = MAJOR(rdebuglog_dev.dev_id);
    rdebuglog_dev.minor = MINOR(rdebuglog_dev.dev_id);
    
    /*2nd: define cdev*/
    rdebuglog_dev.cdev.owner = THIS_MODULE;
    cdev_init(&rdebuglog_dev.cdev, NULL);/*M4/R5核打印，不需要文件操作*/
    ret = cdev_add(&rdebuglog_dev.cdev, rdebuglog_dev.dev_id, RDEBUG_LOG_DEVICE_CNT);
    if(ret < 0)
    {
        printk("%s define cdev error:%d@%s %d\r\n",RDEBUG_LOG_DEVICE_NAME,ret,__FUNCTION__,__LINE__);
        unregister_chrdev_region(rdebuglog_dev.dev_id, RDEBUG_LOG_DEVICE_CNT);
        goto error_out;
    }

    /*3rd:define class*/
    rdebuglog_dev.class = class_create(THIS_MODULE, RDEBUG_LOG_DEVICE_NAME);
    if(IS_ERR(rdebuglog_dev.class))
    {
        ret = PTR_ERR(rdebuglog_dev.class);
        cdev_del(&rdebuglog_dev.cdev);
        printk("%s define class error:%d@%s %d\r\n",RDEBUG_LOG_DEVICE_NAME,ret,__FUNCTION__,__LINE__);
        goto error_out;
    }

    /*4th:define device*/
    rdebuglog_dev.device = device_create(rdebuglog_dev.class, NULL, rdebuglog_dev.dev_id, NULL, RDEBUG_LOG_DEVICE_NAME);
    if(IS_ERR(rdebuglog_dev.device))
    {
        class_destroy(rdebuglog_dev.class);
        ret = PTR_ERR(rdebuglog_dev.device);
        printk("%s define device error:%d@%s %d\r\n",RDEBUG_LOG_DEVICE_NAME,ret,__FUNCTION__,__LINE__);
        goto error_out;
    }
    

    /*5th:define thread*/
    rdebuglog_dev.thread = kthread_run(rdebuglog_process_handler, NULL, "rdebug_handler");
    if(!rdebuglog_dev.thread)
    {
        cdev_del(&rdebuglog_dev.cdev);
        unregister_chrdev_region(rdebuglog_dev.dev_id,RDEBUG_LOG_DEVICE_CNT);

        device_destroy(rdebuglog_dev.class,rdebuglog_dev.dev_id);
        class_destroy(rdebuglog_dev.class); 
        ret = -ECHILD;
        
        printk("%s define device error:%d@%s %d\r\n",RDEBUG_LOG_DEVICE_NAME,ret,__FUNCTION__,__LINE__);
        goto error_out;
    }

    /*6th:define kfifo*/
    ret = kfifo_alloc(&rdebuglog_dev.fifo, RDEBUG_LOG_FIFO_SIZE, GFP_KERNEL);
    if(ret != 0)
    {
        cdev_del(&rdebuglog_dev.cdev);
        unregister_chrdev_region(rdebuglog_dev.dev_id,RDEBUG_LOG_DEVICE_CNT);

        device_destroy(rdebuglog_dev.class,rdebuglog_dev.dev_id);
        class_destroy(rdebuglog_dev.class); 
        unregister_rpmsg_driver( &rpmsg_rdebuglog_client ); 

        printk("%s define kfifo error:%d@%s %d\r\n",RDEBUG_LOG_DEVICE_NAME,ret,__FUNCTION__,__LINE__);
        goto error_out;        
    }


    /*7th:define rpmsg*/
    register_rpmsg_driver(&rpmsg_rdebuglog_client);

    return ret;

error_out:
    printk("install %s error:%d@%s %d\r\n",RDEBUG_LOG_DEVICE_NAME,ret,__FUNCTION__,__LINE__);
    return ret;
}

static void __exit rdebuglog_exit(void)
{
    if(rdebuglog_dev.thread)
    {
        kthread_stop(rdebuglog_dev.thread);
        rdebuglog_dev.thread = NULL;
    }

    kfifo_free(&rdebuglog_dev.fifo);

    unregister_rpmsg_driver( &rpmsg_rdebuglog_client );
    cdev_del(&rdebuglog_dev.cdev);
    unregister_chrdev_region(rdebuglog_dev.dev_id,RDEBUG_LOG_DEVICE_CNT);

    device_destroy(rdebuglog_dev.class,rdebuglog_dev.dev_id);
    class_destroy(rdebuglog_dev.class); 
}


module_init(rdebuglog_init);
module_exit(rdebuglog_exit);

MODULE_LICENSE("GPL");
MODULE_AUTHOR("LiMing");
MODULE_DESCRIPTION("remote debug log driver");
MODULE_VERSION("V1.01");

/*end of the file:rdebuglog.c*/
