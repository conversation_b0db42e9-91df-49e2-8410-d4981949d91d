/*********************************************************************
 * \file lmx2572.c
 * @Author: liming
 * @Date:   2025-02-19 10:37:21
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 16:39:36
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 *                                                              
 *********************************************************************/


/*
When the device is first powered up, it must be initialized, and the ordering of this programming is important. The
sequence is listed below. After this sequence is completed, the device should be running and locked to the
proper frequency.
1. Apply power to the device and ensure all the supply pins are at the proper levels.
2. If CE is low, pull it high.
3. Wait 500 µs for the internal LDOs to become stable.
4. Ensure that a valid reference clock is applied to the OSCin pins.
5. Program register R0 with RESET = 1. This will ensure all the registers are reset to their default values. This
bit is self-clearing.
6. Program in sequence registers R125, R124, R123, ..., R1 and then R0.

The recommended sequence for changing frequencies in different scenarios is as follows:
1. If the N divider is changing, program the relevant registers and then program R0 with FCAL_EN = 1.
2. In FSK and Ramp mode, the fractional numerator is changing; program the relevant registers only.

Some register fields support double buffering. That is, the change to these fields would not be effective
immediately. To latch the new values into the device requires programming R0 again with FCAL_EN = 1. The
following register fields support double buffering, see Table 70 for details.
• MASH order (MASH_ORDER)
• Fractional numerator (PLL_NUM)
• N divider (PLL_N)
• Doubler (OSC_2X); Pre-R divider (PLL_R_PRE); Multiplier (MULT); Post-R divider (PLL_R)
For example,
1. Program PLL_R and PLL_N to new values. If double buffering for these fields is enabled, the PLL will remain
unchanged.
2. Program R0 with FCAL_EN = 1. The PLL will calibrate and lock using the new PLL_R and PLL_N values.

*/

#include "asm-generic/int-ll64.h"
#include "linux/device/class.h"
#include "linux/export.h"
#include "linux/kern_levels.h"
#include "linux/printk.h"
#include "linux/types.h"
#include "uapi/linux/spi/spi.h"
#include <linux/init.h>
#include <linux/kernel.h>
#include <linux/module.h>

#include <linux/fs.h>
#include <linux/cdev.h>
#include <linux/uaccess.h>


#include <linux/delay.h>
#include <linux/errno.h>
#include <linux/gpio.h>
#include <linux/of.h>
#include <linux/of_address.h>
#include <linux/of_gpio.h>
#include <asm/io.h>
#include <linux/device.h>
#include <asm/uaccess.h>
#include <linux/platform_device.h>

#include <linux/spi/spi.h>

#include <linux/math.h>

#include "lmx2572_regs.h"
#include "lmx2572.h"

#define LMX2572_DEV_NAME "lmx2572"
#define LMX2572_DEV_CNT (1)

static dev_t lmx2572_devno;
static struct cdev lmx2572_cdev; /*hold char device information*/
static struct class *lmx2572_class; /*hold created class*/
static struct device *lmx2572_device; /*hold created device*/

#define LMX2572_REG_NUM  126
#define LMX2572_ADDR_MASK 0x7f0000
#define LMX2572_ADDR_SHIT  16
#define LMX2572_DIRECTION_MASK 0x800000
#define LMX2572_DIRECTION_SHIT  24

#define LMX2572_DATA_MASK 0x00ffff

#define LMX2572_REG_MAKER(regAddr,value,direction)    ((u32)((direction << LMX2572_DIRECTION_SHIT) & LMX2572_DIRECTION_SHIT) | ((regAddr << LMX2572_ADDR_SHIT) & LMX2572_ADDR_MASK) | (value & LMX2572_DATA_MASK))

#define BOOT_PARA_FREQ_BAND_350M 350
#define BOOT_PARA_FREQ_BAND_150M 150
#define UI_DSP_MOD_TYPE_ANALOG 1
#define HARDWARE_SKY72310_REG_7_DEFAULT 1
#define HARDWARE_SKY72310_REG_8_DEFAULT 1
#define HARDWARE_SKY72310_PLL_REF 1
#define HARDWARE_LMX2572_CRYSTAL_FREQ 1

/*
7.5.2 Recommended Sequence for Changing Frequencies
The recommended sequence for changing frequencies in different scenarios is as follows:
1. If the N divider is changing, program the relevant registers and then program R0 with FCAL_EN = 1.
2. In FSK and Ramp mode, the fractional numerator is changing; program the relevant registers only.
 */
/* 按顺序写入lmx2572/1，0 - 第一个发送地址*/
static const u8 freq_set_relevant_regs[] = 
{
    /*reference frequency path*/
    0x9,0xa,0xb,0xc,

    /*N divider*/
    0x2a,0x2b,0xc,0x2d,0x2e,0x4b,

    /*FCAK_EN*/
    0x00
};

typedef struct _ts_lm2572_info
{
    u32 plla_freq;
    u32 pllb_freq;

    u32 plla_phase_detect_freq;
    u32 plla_middle_freq;
    u32 plla_afc_coef;    

    u32 pllb_phase_detect_freq;
    u32 pllb_middle_freq;
    u32 pllb_afc_coef;

    u16 plla_chargepump;    
    u16 pllb_chargepump;

    /* reference path parametes*/
    u32 foscin;/*default :38.4MHz*/  //todo:device config
    u8  osc2x; /*oscin Doubler*/
    u8  pllRPre; /*Pre-R Divider*/
    u8  mult; /*Multiplier */
    u8  pllR; /*Post-R divicer*/
    u32 pllDen; /* pll fractional denomiator */
}ts_lm2572_info;

typedef struct _ts_lm2572_obj 
{
    struct spi_device *spi;
    struct gpio_desc *ld;
    ts_lmx2572_regs_info lmx2572_regs;
    ts_lm2572_info lmx2572_info;
}ts_lm2572_obj;


static const ts_lmx2572_regs_info glmx2572_regs_default =
{
    /* REG0 */
    .powerdown = POWERDOWN_NORMAL_OPERATION,
    .reset = RESET_NORMAL_OPERATION,
    .muxout_ld_sel = MUXOUT_LD_SEL_LOCK_DETECT,
    .fcal_en = FCAL_EN_ENABLE,
    .reg0_reserved0 = 1,
    .fcal_lpfd_adj = 0,
    .fcal_hpfd_adj = 0,
    .out_mute = OUT_MUTE_MUTED,
    .reg0_reserved1 = 0,
    .add_hold = 0,
    .reg0_reserved2 = 2,
    .vco_phase_sync_en = VCO_PHASE_SYNC_EN_NORMAL_OPERATION,
    .ramp_en = RAMP_EN_NORMAL_OPERATION,

    /* REG1 */
    .cal_clk_div = 0,
    .reg1_reserved0 = 257,

    /* REG2 */
    .reg2_reserved0 = 1280,

    /* REG3 */
    .reg3_reserved0 = 1922,

    /* REG4 */
    .reg4_reserved0 = 2627,

    /* REG5 */
    .reg5_reserved0 = 200,
    .ipbuf_term = IPBUF_TERM_NORMAL_OPERATION,
    .ipbuf_type = IPBUF_TYPE_SINGLE_ENDED,
    .reg5_reserved1 = 1,

    /* REG6 */
    .reg6_reserved0 = 2,
    .ldo_dly = 25,

    /* REG7 */
    .reg7_reserved0 = 178,
    .out_force = OUT_FORCE_USE_OUT_MUTE,
    .reg7_reserved1 = 0,

    /* REG8 */
    .reg8_reserved0 = 0,
    .vco_capctrl_force = VCO_CAPCTRL_FORCE_NORMAL_OPERATION,
    .reg8_reserved1 = 2,
    .vco_daciset_force = VCO_DACISET_FORCE_NORMAL_OPERATION,
    .reg8_reserved2 = 0,

    /* REG9 */
    .reg9_reserved0 = 4,
    .osc_2x = 0,
    .reg9_reserved1 = 0,
    .mult_hi = 0,
    .reg9_reserved2 = 0,

    /* REG10 */
    .reg10_reserved0 = 120,
    .mult = 0,
    .reg10_reserved1 = 1,

    /* REG11 */
    .reg11_reserved0 = 8,
    .pll_r = 0,
    .reg11_reserved1 = 11,

    /* REG12 */
    .pll_r_pre = 0,
    .reg12_reserved0 = 5,

    /* REG13 */
    .reg13_reserved0 = 16384,

    /* REG14 */
    .reg14_reserved0 = 0,
    .cpg = 2,
    .reg14_reserved1 = 48,

    /* REG15 */
    .reg15_reserved0 = 1550,

    /* REG16 */
    .vco_daciset = 128,
    .reg16_reserved0 = 0,

    /* REG17 */
    .vco_daciset_strt = 150,
    .reg17_reserved0 = 0,

    /* REG18 */
    .reg18_reserved0 = 100,

    /* REG19 */
    .vco_capctrl = 183,
    .reg19_reserved0 = 39,

    /* REG20 */
    .reg20_reserved0 = 72,
    .vco_sel_force = VCO_SEL_FORCE_DISABLED,
    .vco_sel = 1,
    .reg20_reserved1 = 1,

    /* REG21 */
    .reg21_reserved0 = 1033,

    /* REG22 */
    .reg22_reserved0 = 1,

    /* REG23 */
    .reg23_reserved0 = 124,

    /* REG24 */
    .reg24_reserved0 = 1818,

    /* REG25 */
    .reg25_reserved0 = 1572,

    /* REG26 */
    .reg26_reserved0 = 2056,

    /* REG27 */
    .reg27_reserved0 = 2,

    /* REG28 */
    .reg28_reserved0 = 1160,

    /* REG29 */
    .reg29_reserved0 = 0,

    /* REG30 */
    .reg30_reserved0 = 6310,

    /* REG31 */
    .reg31_reserved0 = 50150,

    /* REG32 */
    .reg32_reserved0 = 1471,

    /* REG33 */
    .reg33_reserved0 = 7681,

    /* REG34 */
    .pll_n_upper_3_bits = 0,
    .reg34_reserved0 = 2,

    /* REG35 */
    .reg35_reserved0 = 4,

    /* REG36 */
    .pll_n_lower_16_bits = 0,

    /* REG37 */
    .reg37_reserved0 = 5,
    .pfd_dly_sel = 0,
    .reg37_reserved1 = 0,
    .mash_seed_en = MASH_SEED_EN_ENABLED,

    /* REG38 */
    .pll_den_upper = 512,

    /* REG39 */
    .pll_den_lower = 0,

    /* REG40 */
    .mash_seed_upper = 0,

    /* REG41 */
    .mash_seed_lower = 0,

    /* REG42 */
    .pll_num_upper = 0,

    /* REG43 */
    .pll_num_lower = 0,

    /* REG44 */
    .mash_order = MASH_ORDER_THIRD_ORDER,
    .reg44_reserved0 = 0,
    .mash_reset_n = MASH_RESET_N_RESET,
    .outa_pd = OUTA_PD_POWER_DOWN,
    .outb_pd = OUTB_PD_NORMAL_OPERATION,
    .outa_pwr = 63,
    .reg44_reserved1 = 0,

    /* REG45 */
    .outb_pwr = 0,
    .register45_reserved0 = 24,
    .outa_mux = OUTA_MUX_CHANNEL_DIVIDER,
    .register45_reserved1 = 6,

    /* REG46 */
    .outb_mux = OUTB_MUX_CHANNEL_DIVIDER,
    .register46_reserved0 = 508,

    /* REG47 */
    .reg47_reserved0 = 768,

    /* REG48 */
    .reg48_reserved0 = 992,

    /* REG49 */
    .reg49_reserved0 = 16768,

    /* REG50 */
    .reg50_reserved0 = 128,

    /* REG51 */
    .reg51_reserved0 = 128,

    /* REG52 */
    .reg52_reserved0 = 1057,

    /* REG53 */
    .reg53_reserved0 = 0,

    /* REG54 */
    .reg54_reserved0 = 0,

    /* REG55 */
    .reg55_reserved0 = 0,

    /* REG56 */
    .reg56_reserved0 = 0,

    /* REG57 */
    .reg57_reserved0 = 32,

    /* REG58 */
    .reg58_reserved0 = 1,
    .inpin_fmt = INPIN_FMT_SYNC_EQUALS_SYSREFREQ_EQUALS_CMOS,
    .inpin_lvl = INPIN_LVL_VIN,
    .inpin_hyst = INPIN_HYST_DISABLED,
    .inpin_ignore = 1,

    /* REG59 */
    .ld_type = LD_TYPE_VTUNE_AND_VCOCAL,
    .reg59_reserved0 = 0,

    /* REG60 */
    .ld_dly = 1000,

    /* REG61 */
    .reg61_reserved0 = 168,

    /* REG62 */
    .reg62_reserved0 = 175,
    .dblbuf_en_0 = DBLBUF_EN_0_ENABLED,
    .dblbuf_en_1 = DBLBUF_EN_1_ENABLED,
    .dblbuf_en_2 = DBLBUF_EN_2_ENABLED,
    .dblbuf_en_3 = DBLBUF_EN_3_ENABLED,
    .dblbuf_en_4 = DBLBUF_EN_4_ENABLED,
    .dblbuf_en_5 = DBLBUF_EN_5_ENABLED,

    /* REG63 */
    .reg63_reserved0 = 0,

    /* REG64 */
    .reg64_reserved0 = 5000,

    /* REG65 */
    .reg65_reserved0 = 0,

    /* REG66 */
    .reg66_reserved0 = 500,

    /* REG67 */
    .reg67_reserved0 = 0,

    /* REG68 */
    .reg68_reserved0 = 1000,

    /* REG69 */
    .mash_rst_count_upper = 0,

    /* REG70 */
    .mash_rst_count_lower = 50000,

    /* REG71 */
    .reg71_reserved0 = 1,
    .sysref_repeat = SYSREF_REPEAT_MASTER_MODE,
    .sysref_en = SYSREF_EN_DISABLED,
    .sysref_pulse = SYSREF_PULSE_DISABLED,
    .sysref_div_pre = SYSREF_DIV_PRE_DIVIDE_BY_4,
    .reg71_reserved1 = 0,

    /* REG72 */
    .sysref_div = 1,
    .reg72_reserved0 = 0,

    /* REG73 */
    .jesd_dac1_ctrl = 63,
    .jesd_dac2_ctrl = 0,
    .reg73_reserved0 = 0,

    /* REG74 */
    .jesd_dac3_ctrl = 0,
    .jesd_dac4_ctrl = 0,
    .sysref_pulse_cnt = 0,

    /* REG75 */
    .reg75_reserved0 = 0,
    .chdiv = 0,
    .reg75_reserved1 = 1,

    /* REG76 */
    .reg76_reserved0 = 12,
    
    /* REG77 */
    .reg77_reserved0 = 0,

    /* REG78 */
    .reg78_reserved0 = 1,
    .vco_capctrl_strt = 0,
    .quick_recal_en = 1,
    .reg78_reserved1 = 0,
    .ramp_thresh_33rd = 0,
    .reg78_reserved2 = 0,

    /* REG79 */
    .ramp_thresh_upper = 0,

    /* REG80 */
    .ramp_thresh_lower = 0,

    /* REG81 */
    .ramp_limit_high_33rd = 0,
    .reg81_reserved0 = 0,

    /* REG82 */
    .ramp_limit_high_upper = 0,

    /* REG83 */
    .ramp_limit_high_lower = 0,

    /* REG84 */
    .ramp_limit_low_33rd = 0,
    .reg84_reserved0 = 0,

    /* REG85 */
    .ramp_limit_low_upper = 0,

    /* REG86 */
    .ramp_limit_low_lower = 0,

    /* REG87 */
    .reg87_reserved0 = 0,

    /* REG88 */
    .reg88_reserved0 = 0,

    /* REG89 */
    .reg89_reserved0 = 0,

    /* REG90 */
    .reg90_reserved0 = 0,

    /* REG91 */
    .reg91_reserved0 = 0,

    /* REG92 */
    .reg92_reserved0 = 0,

    /* REG93 */
    .reg93_reserved0 = 0,

    /* REG94 */
    .reg94_reserved0 = 0,

    /* REG95 */
    .reg95_reserved0 = 0,

    /* REG96 */
    .reg96_reserved0 = 0,
    .ramp_burst_count = 0,
    .ramp_burst_en = RAMP_BURST_EN_DISABLED,

    /* REG97 */
    .ramp_burst_trig = RAMP_BURST_TRIG_RAMP_TRANSISTION,
    .reg97_reserved0 = 0,
    .ramp_triga = RAMP_TRIGA_DISABLED,
    .ramp_trgb = RAMP_TRGB_DISABLED,
    .reg97_reserved1 = 0,
    .ramp0_rst = RAMP0_RST_DISABLED,

    /* REG98 */
    .ramp0_dly = 0,
    .reg98_reserved0 = 0,
    .ramp0_inc_upper_14 = 0,

    /* REG99 */
    .ramp0_inc_lower = 0,

    /* REG100 */
    .ramp0_len = 0,

    /* REG101 */
    .ramp0_next_trig = RAMP0_NEXT_TRIG_TIMEOUT_COUNTER,
    .reg101_reserved0 = 0,
    .ramp0_next = RAMP0_NEXT_RAMP0,
    .ramp1_rst = RAMP1_RST_DISABLED,
    .ramp1_dly = 0,
    .reg101_reserved1 = 0,

    /* REG102 */
    .ramp1_inc_upper_14 = 0,
    .reg102_reserved0 = 0,

    /* REG103 */
    .ramp1_inc_lower = 0,

    /* REG104 */
    .ramp1_len = 0,

    /* REG105 */
    .ramp1_next_trig = RAMP1_NEXT_TRIG_TIMEOUT_COUNTER,
    .reg105_reserved0 = 0,
    .ramp1_next = RAMP1_NEXT_RAMP0,
    .ramp_manual = RAMP_MANUAL_AUTOMATIC_RAMPING,
    .ramp_dly_cnt = 273,

    /* REG106 */
    .ramp_scale_count = 7,
    .reg106_reserved0 = 0,
    .ramp_trig_cal = RAMP_TRIG_CAL_DISABLED,
    .reg106_reserved1 = 0,

    /* REG114 */
    .fsk_mode_sel = FSK_MODE_SEL_FSK_SPI_FAST,
    .fsk_spi_dev_sel = 0,
    .fsk_spi_level = 0,
    .reg114_reserved0 = 0,
    .fsk_en = FSK_EN_ENABLED,
    .reg114_reserved1 = 15,

    /* REG115 */
    .reg115_reserved0 = 0,
    .fsk_dev_scale = 0, 
    .reg115_reserved1 = 0,

    /* REG116 */
    .fsk_dev0 = 0,

    /* REG117 */
    .fsk_dev1 = 0,

    /* REG118 */
    .fsk_dev2 = 0,

    /* REG119 */
    .fsk_dev3 = 0,

    /* REG120 */
    .fsk_dev4 = 0,

    /* REG121 */
    .fsk_dev5 = 0,

    /* REG122 */
    .fsk_dev6 = 0,

    /* REG123 */
    .fsk_dev7 = 0,

    /* REG124 */
    .fsk_spi_dev = 0,

    /* REG125 */
    .reg125 = 8840
};

static ts_lm2572_obj glmx2572_handler;

static void lmx2572_init(void)
{
    memcpy(&glmx2572_handler.lmx2572_regs, &glmx2572_regs_default, sizeof(ts_lmx2572_regs_info));
    glmx2572_handler.lmx2572_info.foscin = 38400000;
    glmx2572_handler.lmx2572_info.osc2x = 0;
    glmx2572_handler.lmx2572_info.mult = 6;
    glmx2572_handler.lmx2572_info.pllRPre = 2;
    glmx2572_handler.lmx2572_info.pllR = 2;
    glmx2572_handler.lmx2572_info.pllDen = 0x2000000; //todo:需要研究是否可以在放大，目前精度是否以及足够
}

static u16 lmx2572_reg_get(uint8_t addr)
{
    u16 reg = 0;
    
    switch(addr){
        case 0:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.powerdown) & 0x1) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reset) & 0x1) << 1;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.muxout_ld_sel) & 0x1) << 2;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.fcal_en) & 0x1) << 3;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg0_reserved0) & 0x1) << 4;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.fcal_lpfd_adj) & 0x3) << 5;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.fcal_hpfd_adj) & 0x3) << 7;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.out_mute) & 0x1) << 9;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg0_reserved1) & 0x1) << 10;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.add_hold) & 0x1) << 11;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg0_reserved2) & 0x3) << 12;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.vco_phase_sync_en) & 0x1) << 14;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp_en) & 0x1) << 15;
            break;
        case 1:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.cal_clk_div) & 0x7) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg1_reserved0) & 0x1fff) << 3;
            break;
        case 2:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg2_reserved0) & 0xffff) << 0;
            break;
        case 3:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg3_reserved0) & 0xffff) << 0;
            break;
        case 4:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg4_reserved0) & 0xffff) << 0;
            break;
        case 5:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg5_reserved0) & 0x7ff) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ipbuf_term) & 0x1) << 11;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ipbuf_type) & 0x1) << 12;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg5_reserved1) & 0x7) << 13;
            break;
        case 6:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg6_reserved0) & 0x7ff) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ldo_dly) & 0x1f) << 11;
            break;
        case 7:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg7_reserved0) & 0x3fff) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.out_force) & 0x1) << 14;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg7_reserved1) & 0x1) << 15;
            break;
        case 8:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg8_reserved0) & 0x7ff) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.vco_capctrl_force) & 0x1) << 11;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg8_reserved1) & 0x3) << 12;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.vco_daciset_force) & 0x1) << 14;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg8_reserved2) & 0x1) << 15;
            break;
        case 9:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg9_reserved0) & 0xfff) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.osc_2x) & 0x1) << 12;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg9_reserved1) & 0x1) << 13;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.mult_hi) & 0x1) << 14;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg9_reserved2) & 0x1) << 15;
            break;
        case 10:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg10_reserved0) & 0x7f) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.mult) & 0x1f) << 7;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg10_reserved1) & 0xf) << 12;
            break;
        case 11:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg11_reserved0) & 0xf) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.pll_r) & 0xff) << 4;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg11_reserved1) & 0xf) << 12;
            break;
        case 12:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.pll_r_pre) & 0xfff) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg12_reserved0) & 0xf) << 12;
            break;
        case 13:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg13_reserved0) & 0xffff) << 0;
            break;
        case 14:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg14_reserved0) & 0x7) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.cpg) & 0xf) << 3;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg14_reserved1) & 0x1ff) << 7;
            break;
        case 15:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg15_reserved0) & 0xffff) << 0;
            break;
        case 16:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.vco_daciset) & 0x1ff) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg16_reserved0) & 0x7f) << 9;
            break;
        case 17:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.vco_daciset_strt) & 0x1ff) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg17_reserved0) & 0x7f) << 9;
            break;
        case 18:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg18_reserved0) & 0xffff) << 0;
            break;
        case 19:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.vco_capctrl) & 0xff) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg19_reserved0) & 0xff) << 8;
            break;
        case 20:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg20_reserved0) & 0x3ff) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.vco_sel_force) & 0x1) << 10;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.vco_sel) & 0x7) << 11;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg20_reserved1) & 0x3) << 14;
            break;
        case 21:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg21_reserved0) & 0xffff) << 0;
            break;
        case 22:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg22_reserved0) & 0xffff) << 0;
            break;
        case 23:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg23_reserved0) & 0xffff) << 0;
            break;
        case 24:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg24_reserved0) & 0xffff) << 0;
            break;
        case 25:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg25_reserved0) & 0xffff) << 0;
            break;
        case 26:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg26_reserved0) & 0xffff) << 0;
            break;
        case 27:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg27_reserved0) & 0xffff) << 0;
            break;
        case 28:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg28_reserved0) & 0xffff) << 0;
            break;
        case 29:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg29_reserved0) & 0xffff) << 0;
            break;
        case 30:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg30_reserved0) & 0xffff) << 0;
            break;
        case 31:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg31_reserved0) & 0xffff) << 0;
            break;
        case 32:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg32_reserved0) & 0xffff) << 0;
            break;
        case 33:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg33_reserved0) & 0xffff) << 0;
            break;
        case 34:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.pll_n_upper_3_bits) & 0x7) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg34_reserved0) & 0x1fff) << 3;
            break;
        case 35:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg35_reserved0) & 0xffff) << 0;
            break;
        case 36:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.pll_n_lower_16_bits) & 0xffff) << 0;
            break;
        case 37:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg37_reserved0) & 0xff) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.pfd_dly_sel) & 0x3f) << 8;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg37_reserved1) & 0x1) << 14;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.mash_seed_en) & 0x1) << 15;
            break;
        case 38:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.pll_den_upper) & 0xffff) << 0;
            break;
        case 39:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.pll_den_lower) & 0xffff) << 0;
            break;
        case 40:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.mash_seed_upper) & 0xffff) << 0;
            break;
        case 41:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.mash_seed_lower) & 0xffff) << 0;
            break;
        case 42:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.pll_num_upper) & 0xffff) << 0;
            break;
        case 43:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.pll_num_lower) & 0xffff) << 0;
            break;
        case 44:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.mash_order) & 0x7) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg44_reserved0) & 0x3) << 3;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.mash_reset_n) & 0x1) << 5;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.outa_pd) & 0x1) << 6;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.outb_pd) & 0x1) << 7;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.outa_pwr) & 0x3f) << 8;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg44_reserved1) & 0x3) << 14;
            break;
        case 45:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.outb_pwr) & 0x3f) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.register45_reserved0) & 0x1f) << 6;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.outa_mux) & 0x3) << 11;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.register45_reserved1) & 0x7) << 13;
            break;
        case 46:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.outb_mux) & 0x3) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.register46_reserved0) & 0x3fff) << 2;
            break;
        case 47:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg47_reserved0) & 0xffff) << 0;
            break;
        case 48:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg48_reserved0) & 0xffff) << 0;
            break;
        case 49:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg49_reserved0) & 0xffff) << 0;
            break;
        case 50:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg50_reserved0) & 0xffff) << 0;
            break;
        case 51:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg51_reserved0) & 0xffff) << 0;
            break;
        case 52:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg52_reserved0) & 0xffff) << 0;
            break;
        case 53:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg53_reserved0) & 0xffff) << 0;
            break;
        case 54:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg54_reserved0) & 0xffff) << 0;
            break;
        case 55:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg55_reserved0) & 0xffff) << 0;
            break;
        case 56:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg56_reserved0) & 0xffff) << 0;
            break;
        case 57:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg57_reserved0) & 0xffff) << 0;
            break;
        case 58:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg58_reserved0) & 0x1ff) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.inpin_fmt) & 0x7) << 9;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.inpin_lvl) & 0x3) << 12;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.inpin_hyst) & 0x1) << 14;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.inpin_ignore) & 0x1) << 15;
            break;
        case 59:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ld_type) & 0x1) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg59_reserved0) & 0x7fff) << 1;
            break;
        case 60:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ld_dly) & 0xffff) << 0;
            break;
        case 61:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg61_reserved0) & 0xffff) << 0;
            break;
        case 62:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg62_reserved0) & 0x3ff) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.dblbuf_en_0) & 0x1) << 10;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.dblbuf_en_1) & 0x1) << 11;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.dblbuf_en_2) & 0x1) << 12;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.dblbuf_en_3) & 0x1) << 13;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.dblbuf_en_4) & 0x1) << 14;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.dblbuf_en_5) & 0x1) << 15;
            break;
        case 63:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg63_reserved0) & 0xffff) << 0;
            break;
        case 64:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg64_reserved0) & 0xffff) << 0;
            break;
        case 65:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg65_reserved0) & 0xffff) << 0;
            break;
        case 67:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg67_reserved0) & 0xffff) << 0;
            break;
        case 68:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg68_reserved0) & 0xffff) << 0;
            break;
        case 69:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.mash_rst_count_upper) & 0xffff) << 0;
            break;
        case 70:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.mash_rst_count_lower) & 0xffff) << 0;
            break;
        case 71:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg71_reserved0) & 0x3) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.sysref_repeat) & 0x1) << 2;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.sysref_en) & 0x1) << 3;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.sysref_pulse) & 0x1) << 4;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.sysref_div_pre) & 0x7) << 5;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg71_reserved1) & 0xff) << 8;
            break;
        case 72:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.sysref_div) & 0x7ff) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg72_reserved0) & 0x1f) << 11;
            break;
        case 73:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.jesd_dac1_ctrl) & 0x3f) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.jesd_dac2_ctrl) & 0x3f) << 6;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg73_reserved0) & 0xf) << 12;
            break;
        case 74:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.jesd_dac3_ctrl) & 0x3f) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.jesd_dac4_ctrl) & 0x3f) << 6;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.sysref_pulse_cnt) & 0xf) << 12;
            break;
        case 75:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg75_reserved0) & 0x3f) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.chdiv) & 0x1f) << 6;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg75_reserved1) & 0x1f) << 11;
            break;
        case 76:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg76_reserved0) & 0xffff) << 0;
            break;
        case 77:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg77_reserved0) & 0xffff) << 0;
            break;
        case 78:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg78_reserved0) & 0x1) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.vco_capctrl_strt) & 0xff) << 1;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.quick_recal_en) & 0x1) << 9;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg78_reserved1) & 0x1) << 10;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp_thresh_33rd) & 0x1) << 11;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg78_reserved2) & 0xf) << 12;
            break;
        case 79:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp_thresh_upper) & 0xffff) << 0;
            break;
        case 80:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp_thresh_lower) & 0xffff) << 0;
            break;
        case 81:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp_limit_high_33rd) & 0x1) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg81_reserved0) & 0x7fff) << 1;
            break;
        case 82:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp_limit_high_upper) & 0xffff) << 0;
            break;
        case 83:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp_limit_high_lower) & 0xffff) << 0;
            break;
        case 84:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp_limit_low_33rd) & 0x1) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg84_reserved0) & 0x7fff) << 1;
            break;
        case 85:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp_limit_low_upper) & 0xffff) << 0;
            break;
        case 86:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp_limit_low_lower) & 0xffff) << 0;
            break;
        case 87:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg87_reserved0) & 0xffff) << 0;
            break;
        case 88:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg88_reserved0) & 0xffff) << 0;
            break;
        case 89:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg89_reserved0) & 0xffff) << 0;
            break;
        case 90:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg90_reserved0) & 0xffff) << 0;
            break;
        case 91:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg91_reserved0) & 0xffff) << 0;
            break;
        case 92:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg92_reserved0) & 0xffff) << 0;
            break;
        case 93:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg93_reserved0) & 0xffff) << 0;
            break;
        case 94:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg94_reserved0) & 0xffff) << 0;
            break;
        case 95:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg95_reserved0) & 0xffff) << 0;
            break;
        case 96:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg96_reserved0) & 0x3) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp_burst_count) & 0x1fff) << 2;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp_burst_en) & 0x1) << 15;
            break;
        case 97:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp_burst_trig) & 0x3) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg97_reserved0) & 0x1) << 2;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp_triga) & 0xf) << 3;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp_trgb) & 0xf) << 7;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg97_reserved1) & 0xf) << 11;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp0_rst) & 0x1) << 15;
            break;
        case 98:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp0_dly) & 0x1) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg98_reserved0) & 0x1) << 1;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp0_inc_upper_14) & 0x3fff) << 2;
            break;
        case 99:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp0_inc_lower) & 0xffff) << 0;
            break;
        case 100:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp0_len) & 0xffff) << 0;
            break;
        case 101:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp0_next_trig) & 0x3) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg101_reserved0) & 0x3) << 2;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp0_next) & 0x1) << 4;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp1_rst) & 0x1) << 5;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp1_dly) & 0x1) << 6;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg101_reserved1) & 0x1ff) << 7;
            break;
        case 102:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp1_inc_upper_14) & 0x3fff) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg102_reserved0) & 0x3) << 14;
            break;
        case 103:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp1_inc_lower) & 0xffff) << 0;
            break;
        case 104:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp1_len) & 0xffff) << 0;
            break;
        case 105:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp1_next_trig) & 0x3) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg105_reserved0) & 0x3) << 2;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp1_next) & 0x1) << 4;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp_manual) & 0x1) << 5;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp_dly_cnt) & 0x3ff) << 6;
            break;
        case 106:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp_scale_count) & 0x7) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg106_reserved0) & 0x1) << 3;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.ramp_trig_cal) & 0x1) << 4;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg106_reserved1) & 0x7ff) << 5;
            break;
        case 114:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.fsk_mode_sel) & 0x3) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.fsk_spi_dev_sel) & 0x7) << 2;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.fsk_spi_level) & 0x3) << 5;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg114_reserved0) & 0x7) << 7;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.fsk_en) & 0x1) << 10;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg114_reserved1) & 0x1f) << 11;
            break;
        case 115:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg115_reserved0) & 0x7) << 0;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.fsk_dev_scale) & 0x1f) << 3;
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg115_reserved1) & 0xff) << 8;
            break;
        case 116:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.fsk_dev0) & 0xffff) << 0;
            break;
        case 117:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.fsk_dev1) & 0xffff) << 0;
            break;
        case 118:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.fsk_dev2) & 0xffff) << 0;
            break;
        case 119:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.fsk_dev3) & 0xffff) << 0;
            break;
        case 120:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.fsk_dev4) & 0xffff) << 0;
            break;
        case 121:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.fsk_dev5) & 0xffff) << 0;
            break;
        case 122:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.fsk_dev6) & 0xffff) << 0;
            break;
        case 123:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.fsk_dev7) & 0xffff) << 0;
            break;
        case 124:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.fsk_spi_dev) & 0xffff) << 0;
            break;
        case 125:
            reg |= ((u16)(glmx2572_handler.lmx2572_regs.reg125) & 0x1ffff) << 0;
            break;
        }
    return reg;
}

static void lmx2572_regs_print(void)
{
    int i = 0;
    printk("\r\nlmx2572 regs:\r\n\t");
    for(i = 0; i < 126; i++)
    {
        if((i + 1) % 5 == 0)
        {
            printk("\r\n\t");
        }
        printk("%d:%04x ",i, lmx2572_reg_get(i));
    }
}


static int lmx2572_write_reg(struct spi_device *spi_device,uint8_t addr, uint16_t value, uint8_t flag)
{
    int error = -1;
    uint16_t reg = 0;
    struct spi_message *message;   //定义发送的消息
    struct spi_transfer *transfer; //定义传输结构体

    if(addr < LMX2572_REG_NUM)
    {
        if (flag == 1)
            reg = LMX2572_REG_MAKER(addr, value ,0);
        else
            reg |= value;
    }
    else 
    {
        printk(KERN_ERR "%s %d: write error %02x : %04x - addr error! \n",__FUNCTION__,__LINE__,addr,value);
        error = -2;
        goto error_out;
    }

    message = kzalloc(sizeof(struct spi_message), GFP_KERNEL);
    transfer = kzalloc(sizeof(struct spi_transfer), GFP_KERNEL);

    if(message != 0 && transfer !=0 )
    {
        /*填充message和transfer结构体*/
        transfer->tx_buf = &reg;
        if(flag == 1)
            transfer->len = 3;
        else
            transfer->len = 2;
        spi_message_init(message);
        spi_message_add_tail(transfer, message);

        error = spi_sync(spi_device, message);
        kfree(message);
        kfree(transfer);
        if (error != 0)
        {
            error = -3;
            printk(KERN_ERR "%s %d: write error %02x : %04x! \n",__FUNCTION__,__LINE__,addr,value);
        }
        else 
        {
            printk(KERN_INFO "%s %d: %02x : %04x! \n",__FUNCTION__,__LINE__,addr,value);
            error = 0;
        }
    }
    else 
    {
        error = -4;
         printk(KERN_ERR "%s %d: allcate memory error %02x : %04x! \n",__FUNCTION__,__LINE__,addr,value);
    }

error_out:
    return error;
}

static void lmx2572_reset(ts_lm2572_obj* rfChannel)
{
    lmx2572_write_reg(rfChannel->spi, 0, 0x231E, 1);
}

static void lmx2572_plla_set_freq(u32 freq)
{
    //NOT USED,FOR FUTURE
}


static void lmx2572_pllb_set_freq(u32 freq)
{
    u16 chdiv = 0;
    long long vco_freq = 0;
    long long fpd = 0;
    long long detaD = 0;
    u32 Nreg = 0;
    u32 Dividend = 0;
    u32 uintDectect = 0;

	if(freq > 1600000000)
    {
		glmx2572_handler.lmx2572_regs.chdiv = CHDIV_DIVIDE_BY_2;
        chdiv = 1;
    }
	else if((freq > 800000000) && (freq <= 1600000000))
    {
		glmx2572_handler.lmx2572_regs.chdiv = CHDIV_DIVIDE_BY_4;
        chdiv = 2;
    }
	else if((freq > 400000000) && (freq <= 800000000))
    {
		glmx2572_handler.lmx2572_regs.chdiv = CHDIV_DIVIDE_BY_8;
        chdiv = 3;
    }
	else if((freq > 200000000) && (freq <= 400000000))
    {
		glmx2572_handler.lmx2572_regs.chdiv = CHDIV_DIVIDE_BY_16;
        chdiv = 4;
    }
	else if((freq > 100000000) && (freq <= 200000000))
    {
		glmx2572_handler.lmx2572_regs.chdiv = CHDIV_DIVIDE_BY_32;
        chdiv = 5;
    }
	else if((freq > 50000000) && (freq <= 100000000))
    {
		glmx2572_handler.lmx2572_regs.chdiv = CHDIV_DIVIDE_BY_64;
        chdiv = 6;
    }
	else if((freq > 25000000)&&(freq <= 50000000))
    {
		glmx2572_handler.lmx2572_regs.chdiv = CHDIV_DIVIDE_BY_128;
        chdiv = 7;
    }
	else
    {
		glmx2572_handler.lmx2572_regs.chdiv = CHDIV_DIVIDE_BY_256;
        chdiv = 8;
    }

    vco_freq = freq * (1 << chdiv);
    if(1 == glmx2572_handler.lmx2572_info.osc2x)
    {
        fpd = (glmx2572_handler.lmx2572_info.foscin * 2) / (glmx2572_handler.lmx2572_info.pllR * glmx2572_handler.lmx2572_info.pllRPre);
    }
    else 
    {
        fpd = (glmx2572_handler.lmx2572_info.foscin * glmx2572_handler.lmx2572_info.mult) / (glmx2572_handler.lmx2572_info.pllR * glmx2572_handler.lmx2572_info.pllRPre);
    }

    Nreg = vco_freq / fpd;

    detaD = (vco_freq - Nreg * fpd) << 32;
    uintDectect = (long long)((long long)fpd << 32) / glmx2572_handler.lmx2572_info.pllDen;
    Dividend = detaD / uintDectect;

    glmx2572_handler.lmx2572_regs.osc_2x = glmx2572_handler.lmx2572_info.osc2x;
    glmx2572_handler.lmx2572_regs.pll_r_pre = glmx2572_handler.lmx2572_info.pllRPre;
    glmx2572_handler.lmx2572_regs.pll_r = glmx2572_handler.lmx2572_info.pllR;
    glmx2572_handler.lmx2572_regs.pll_den_lower = glmx2572_handler.lmx2572_info.pllDen & 0xffff;
    glmx2572_handler.lmx2572_regs.pll_den_upper = (glmx2572_handler.lmx2572_info.pllDen >> 16) & 0xffff;
    glmx2572_handler.lmx2572_regs.pll_num_lower = Dividend & 0xffff;
    glmx2572_handler.lmx2572_regs.pll_num_upper = (Dividend >> 16) & 0xffff;
    glmx2572_handler.lmx2572_regs.pll_n_lower_16_bits = Nreg & 0xffff;
    glmx2572_handler.lmx2572_regs.pll_n_upper_3_bits = (Nreg >> 16) & 0x3;
    if(vco_freq >= 4900000000)
    {
        glmx2572_handler.lmx2572_regs.pfd_dly_sel = 3;
    }
    else 
    {
        glmx2572_handler.lmx2572_regs.pfd_dly_sel = 2;
    }

    if(fpd <= 37500000)
    {
        glmx2572_handler.lmx2572_regs.fcal_hpfd_adj = 0;
    }
    else if (fpd > 37500000 && fpd <= 75000000) 
    {
        glmx2572_handler.lmx2572_regs.fcal_hpfd_adj = 1;
    }
    else if (fpd > 75000000 && fpd <= 100000000) 
    {
        glmx2572_handler.lmx2572_regs.fcal_hpfd_adj = 2;
    }
    else 
    {
        glmx2572_handler.lmx2572_regs.fcal_hpfd_adj = 3;
    }

    if(fpd >= 10000000)
    {
        glmx2572_handler.lmx2572_regs.fcal_lpfd_adj = 0;
    }
    else if (fpd < 10000000 && fpd >= 5000000) 
    {
        glmx2572_handler.lmx2572_regs.fcal_lpfd_adj = 1;
    }
    else if (fpd < 5000000 && fpd <= 2500000) 
    {
        glmx2572_handler.lmx2572_regs.fcal_lpfd_adj = 2;
    }
    else 
    {
        glmx2572_handler.lmx2572_regs.fcal_lpfd_adj = 3;
    }

}


static int lmx2572_open(struct inode *inode, struct file *filp)
{
    return 0;
}

static ssize_t lmx2572_write(struct file *filp, const char __user *buf, size_t cnt, loff_t *off)
{
    return 0;
}

static int lmx2572_close(struct inode *inode, struct file *filp)
{
    return 0;
}

static long lmx2572_ioctl(struct file *filp, u32 cmd, unsigned long args)
{
    long error = 0;

    switch(cmd) {
        case LMX2572_PLL_A_SET_FREQ:
            if(copy_from_user(&(glmx2572_handler.lmx2572_info.plla_freq), (u32*)args, sizeof(u32)))
            {
                error = -EFAULT;
                printk( KERN_ERR "%s %d:set main freq error!",__FUNCTION__,__LINE__);
                goto error_out;                     
            }
            lmx2572_plla_set_freq(glmx2572_handler.lmx2572_info.plla_freq);
            break;
        case LMX2572_PLL_B_SET_FREQ:
            if(copy_from_user(&(glmx2572_handler.lmx2572_info.pllb_freq), (u32*)args, sizeof(u32)))
            {
                error = -EFAULT;
                printk( KERN_ERR "%s %d:set aux freq error!",__FUNCTION__,__LINE__);
                goto error_out;                     
            }        
            lmx2572_pllb_set_freq(glmx2572_handler.lmx2572_info.pllb_freq);
            break;
        case LMX2572_PRINT_REGS:
            lmx2572_regs_print();
            break;
        default:
            error = -EINVAL;
    }

error_out :
    return error;
}

static struct file_operations lmx2572_fops = {
    .owner = THIS_MODULE,
    .open = lmx2572_open,
    .write = lmx2572_write,
    .release = lmx2572_close,
    .unlocked_ioctl = lmx2572_ioctl,
};

static int lmx2572_drv_probe(struct spi_device *spi)
{
    int error = -1;
    //modify 8
    glmx2572_handler.spi = spi;
    glmx2572_handler.spi->mode = SPI_MODE_0;
    glmx2572_handler.spi->max_speed_hz = 10000000;
    spi_setup(glmx2572_handler.spi);


    /*register char device*/
    error = alloc_chrdev_region(&lmx2572_devno, 0, LMX2572_DEV_CNT, LMX2572_DEV_NAME);
    if(error < 0)
    {
        printk( KERN_ERR "%s %d:alloc device no fail!",__FUNCTION__,__LINE__);
        goto error_out;
    }

    /*attach file operation and init char device*/
    lmx2572_cdev.owner = THIS_MODULE;
    cdev_init(&lmx2572_cdev, &lmx2572_fops);

    /*add to char device*/
    error = cdev_add(&lmx2572_cdev, lmx2572_devno, LMX2572_DEV_CNT);
    if(error < 0)
    {
        printk( KERN_ERR "%s %d:register char device fial!",__FUNCTION__,__LINE__);
        goto error_add;        
    }

    /*register class*/
    lmx2572_class = class_create(THIS_MODULE, LMX2572_DEV_NAME);
    lmx2572_device = device_create(lmx2572_class, NULL, lmx2572_devno, NULL, LMX2572_DEV_NAME);

    error = 0;

    /*init lmx2572*/
    lmx2572_reset(&glmx2572_handler);
error_add:
    unregister_chrdev_region(lmx2572_devno, LMX2572_DEV_CNT);

error_out:
    return error;
}

static void lmx2572_remove(struct spi_device *spi)
{
    /*delete device */
    device_destroy(lmx2572_class, lmx2572_devno);           //delete device
    class_destroy(lmx2572_class);                       //delete class
    cdev_del(&lmx2572_cdev);                       // release device no
    unregister_chrdev_region(lmx2572_devno, LMX2572_DEV_CNT); //deattached char device
}

/*指定 ID 匹配表*/
static const struct spi_device_id lmx2572device_id[] = {
    {"victel,lmx2572", 0},
    {}
};

/*指定设备树匹配表*/
static const struct of_device_id lmx2572_of_match_table[] = {
    {.compatible = "victel,lmx2572"},
    {}
};
MODULE_DEVICE_TABLE(of,lmx2572_of_match_table);
/*spi 总线设备结构体*/
struct spi_driver lmx2572_spi_driver = {
    .probe = lmx2572_drv_probe,
    .remove = lmx2572_remove,
    // .id_table = lmx2572_device_id,
    .driver = {
        .name = "lmx2572",
        .owner = THIS_MODULE,
        .of_match_table = lmx2572_of_match_table,
    },
};

static int __init lmx2572_driver_init(void)
{
    int error = -1;

    pr_info("%s %d\n",__FUNCTION__,__LINE__);
    error = spi_register_driver(&lmx2572_spi_driver);

    return error;
}

static void __exit lmx2572_driver_exit(void)
{
    pr_info("%s %d\n",__FUNCTION__,__LINE__);
    spi_unregister_driver(&lmx2572_spi_driver);
}

module_init(lmx2572_driver_init)
module_exit(lmx2572_driver_exit)

MODULE_LICENSE("GPL");
MODULE_AUTHOR("liming");
MODULE_DESCRIPTION("lmx2572 driver");

/*end of the file:lmx2572.c*/
