/*********************************************************************
 * \file ak2401a.c
 * @Author: liming
 * @Date:   2025-02-27 15:40:11
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 16:39:07
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 *V001  liming   2025-06-27 1525     锁定指示命令时，重新读取锁定管脚状态        
 *V002  liming   2025-07-08 1510     增加收发切换接口
 *********************************************************************/
#include "asm-generic/int-ll64.h"
#include "linux/container_of.h"
#include "linux/dev_printk.h"
#include "linux/device/class.h"
#include "linux/export.h"
#include "linux/kern_levels.h"
#include "linux/mod_devicetable.h"
#include "linux/overflow.h"
#include "linux/printk.h"
#include "linux/types.h"
#include "uapi/linux/spi/spi.h"
#include <linux/init.h>
#include <linux/kernel.h>
#include <linux/module.h>
 
#include <linux/fs.h>
#include <linux/cdev.h>
#include <linux/uaccess.h>
 
 
#include <linux/delay.h>
#include <linux/errno.h>
#include <linux/gpio.h>
#include <linux/of.h>
#include <linux/of_address.h>
#include <linux/of_gpio.h>
#include <asm/io.h>
#include <linux/device.h>
#include <asm/uaccess.h>
#include <linux/platform_device.h>
 
#include <linux/spi/spi.h>
 
#include <linux/irq.h>
#include <linux/interrupt.h>
 
#include <sound/tlv320aic32x4.h>
#include <sound/core.h>
#include <sound/pcm.h>
#include <sound/pcm_params.h>
#include <sound/soc.h>
#include <sound/soc-dapm.h>
#include <sound/initval.h>
#include <sound/tlv.h>
 
#include "ak2401a_regs.h"
#include "ak2401a.h"
 
#define DEVICE_VERSION      "V1.002"
#define AK2401A_NAME "ak2401a"
#define AK2401A_DEV_CNT 2
 
 /*
  *todo list
  *
  */
 
 typedef struct _ts_ak2401a_info_type
 {
     unsigned int ref_clock;
     unsigned int phase_detect_freq;
     unsigned int frequency;
     unsigned int offset;
     unsigned int ld_flag;/* 1 == locked, 0  unlocked*/
 }ts_ak2401a_info_type;
 
 typedef struct _ts_ak2401a_dev
 {
     char name[16];
     char *version;
     int dev_cnt;
     dev_t dev_id;
     int major;
     int minor;
     struct class *class;
     struct device *device;
     struct cdev cdev;
 
     struct spi_device *spi;
     struct device_node *np;
 
     /*数据处理模拟ASOC */
     struct snd_soc_card card;
     struct snd_soc_dai_link dai_link;
     struct snd_soc_dai_link_component cpu;
     struct snd_soc_dai_link_component codec;
     struct snd_soc_dai_link_component platform;
 
     int ld_pin;
     int ld_irq_num;
     int rst_pin;
     int rxpdn_pin;
     int txpdn_pin;
     int agc_pin;
 
     ts_ak2401a_regs_info reg;
     ts_ak2401a_info_type info;
     struct hrtimer ld_timer;
     unsigned int ld_period_ns;
 
     ts_ak2401a_regs regs;
 }ts_ak2401a_dev;
 
 static enum hrtimer_restart ld_timer_process_handler(struct hrtimer *timer);
 static ts_ak2401a_dev gak2401a_handler[AK2401A_DEV_CNT] =
 { 
 {
     .version = DEVICE_VERSION,
     .dev_cnt = AK2401A_DEV_CNT,
     .info.phase_detect_freq = 4608000,//1152000,
     .info.ref_clock = 18432000,
     .ld_period_ns = 200000000, //ns 20ms?
 },
 {
     .version = DEVICE_VERSION,
     .dev_cnt = AK2401A_DEV_CNT,
     .info.phase_detect_freq = 4608000,
     .info.ref_clock = 18432000,
     .ld_period_ns = 200000000,//ns 20ms?
 }
 };
 static dev_t ak2401a_dev_id;
 static struct class * ak2401a_dev_class;
 static unsigned char gRegisterCount = 0;
 static const ts_ak2401a_regs_info ak2401a_regs_default = 
 {
     .cpof = 0,
     .cpfast = 29,
     .cpfine = 14,
     .dummy1 = 0,
     .fasten = 1,
     .cphiz = 0,
     .dsmon = 1,
     .ld = 0,
     .fast_time = 0x4e20,
     .txolv = 1,
     .mod = 0x3ffff,
     .dfil_clk = 1,/*0 - 19.2MHz  1 - 18.432MHz */
     .rdiv = 4, /* fpd : 19.2MHz / 8 2.4MHz   18.432/8 = 2.304M*/
 
 };
 unsigned char gak2401regs_default[] = 
 {
#if 1 // 375 tx
 0x01, 0x03,
 0x02, 0x8E,
 0x03, 0x38,
 0x04, 0x03,
 0x05, 0xFF,
 0x06, 0xFF,
 0x07, 0x02,
 0x08, 0xFB,
 0x09, 0x04,
 0x0A, 0x08,
 0x0B, 0x1F,
 0x0C, 0x32,
 0x0D, 0x90,
 0x0E, 0x00,
 0x0F, 0x00,
 0x10, 0x00,
 0x11, 0x00,
 0x12, 0x01,
 0x13, 0x01,
 0x14, 0xFF,
 0x15, 0x1c,
 0x16, 0x1c,
 0x18, 0x00,
 0x19, 0x00,
 0x1A, 0x00,
 0x1B, 0x00,
 0x1C, 0x00,
 0x1D, 0x00,
 0x1E, 0x00,
 0x1F, 0x60,
 0x20, 0x3B,
 0x21, 0x02,
 0x22, 0x18,
 0x24, 0x00,
 0x25, 0x00,
 0x26, 0x53,
 0x27, 0x23,
 0x28, 0x60,
 0x29, 0x00,
 0x2A, 0x00,
 0x2B, 0x44,
 0x2C, 0x1C,
 0x2E, 0xff,//
 0x3F, 0x40,
 0x40, 0x40,
 0x42, 0x20,
 0x44, 0x05,
 0x45, 0x01,
 0x46, 0xC0,
 0x47, 0x08,
 0x48, 0x1A,
 0x49, 0x00,
 0x4D, 0x00,
 0x4E, 0x00,
 0x4b,0x02,
 0x51, 0x00,
 
 
#endif 
#if 0 //rx 375
 0x01, 0x03,
 0x02, 0x0A,
 0x03, 0xAA,
 0x04, 0x03,
 0x05, 0xFF,
 0x06, 0xFF,
 0x07, 0x00,
 0x08, 0xA2,
 0x09, 0x04,
 0x0A, 0x1D, 
 0x0B, 0x1F,
 0x0C, 0x02,
 0x0D, 0x4E,
 0x0E, 0x20,
 0x0F, 0x00,
 0x10, 0x00,
 0x11, 0x00,
 0x12, 0x01,
 0x13, 0x01,
 0x14, 0x02, 
 0x15, 0x00,
 0x16, 0x00,
 0x18, 0x00,
 0x19, 0x00,
 0x1A, 0x00,
 0x1B, 0x00,
 0x1C, 0x00,
 0x1D, 0x00,
 0x1E, 0x00,
 0x1F, 0x60,
 0x20, 0x3B,
 0x21, 0x02,
 0x22, 0x18,
 0x24, 0x00,
 0x25, 0x00,
 0x26, 0x53,
 0x27, 0x23,
 0x28, 0x64,
 0x29, 0x00,
 0x2A, 0x00,
 0x2B, 0x00,
 0x2C, 0x1C,
 0x2E, 0xEF,
 0x3F, 0x40,
 0x40, 0x40,
 0x42, 0x20,
 0x44, 0x05,
 0x45, 0x01,
 0x46, 0xC0,
 0x47, 0x00,
 0x48, 0x1A,
 0x49, 0x00,
 0x4D, 0x00,
 0x4E, 0x00,
 0x51, 0x00
 
 
#endif 
 };
 
 static u8 ak2401_frequency_regs[] = 
 {
     0x01,0x02,0x03,0x4,0x5,0x6,0x07,0x08
 };
 
 static void ak2401_freq_set(ts_ak2401a_dev *dev,u32 freq, u8 src);
 static void ak2401_init(ts_ak2401a_dev *dev, u8 src);
 static int ak2401a_rssi_get(ts_ak2401a_dev *dev,int *rssi);
 
 static enum hrtimer_restart ld_timer_process_handler(struct hrtimer *timer)
 {
     ts_ak2401a_dev *dev = (ts_ak2401a_dev *)container_of(timer, ts_ak2401a_dev, ld_timer);
     enum hrtimer_restart status = HRTIMER_RESTART;
     int value;
 
     hrtimer_forward_now(timer, ktime_set(0,dev->ld_period_ns));
 
     value = gpio_get_value(dev->ld_pin);
     if(value != dev->info.ld_flag)
     {
         if(1 == value)
         {
            //  printk( KERN_ERR "!!! %s:%u Hz locked! \n",dev->name,dev->info.frequency);
             dev->info.ld_flag = 1;
         }
         else 
         {
            //  printk( KERN_ERR "!!!%s:%u Hz unlocked! \n",dev->name,dev->info.frequency);
             dev->info.ld_flag = 0;
         }
     }
 
     status = HRTIMER_RESTART;
     return status;  
 }
 
 static u8 ak2401a_reg_get(ts_ak2401a_dev *dev,u8 addr)
 {
     u8 reg = 0x0;
 
     switch(addr)
     {
         case 0x01:
             reg = (dev->reg.frac >> 16) & 0x3;
             break;
         case 0x02:
             reg = (dev->reg.frac >> 8) & 0xff;
             break;
         case 0x03:
             reg = (dev->reg.frac >> 0) & 0xff;
             break;  
         case 0x04:
             reg = (dev->reg.mod >> 16) & 0x3;
             break;
         case 0x05:
             reg = (dev->reg.mod >> 8) & 0xff;
             break;
         case 0x06:
             reg = (dev->reg.mod >> 0) & 0xff;
             break;    
         case 0x07:
             reg = (dev->reg.integer >> 8) & 0xf;
             break;
         case 0x08:
             reg = (dev->reg.integer >> 0) & 0xff;
             break;  
         case 0x09:
             reg = dev->reg.rdiv;
             break;    
         case 0x0a:
             reg  = (dev->reg.cpof & 0x3) << 5;
             reg |= dev->reg.cpfine & 0x1f;
             break; 
         case 0x0b:
             reg = dev->reg.cpfine & 0x1f;
             break;      
         case 0x0c:
             reg  = (dev->reg.ld & 0x1);
             reg |= (dev->reg.dsmon & 0x1)  << 1;
             reg |= (dev->reg.cphiz & 0x1)  << 3;
             reg |= (dev->reg.dummy1 & 0x1) << 4;
             reg |= (dev->reg.fasten & 0x1) << 5;
             break; 
         case 0x0d:
             reg = (dev->reg.fast_time >> 8) & 0xf;
             break;
         case 0x0e:
             reg = (dev->reg.fast_time >> 0) & 0xff;
             break;     
         case 0x0f:
             reg = (dev->reg.ofset1 >> 16) & 0x3;
             break;
         case 0x10:
             reg = (dev->reg.ofset1 >> 8) & 0xff;
             break;
         case 0x11:
             reg = (dev->reg.ofset1 >> 0) & 0xff;
             break; 
         case 0x12:
             reg  = (dev->reg.divsel   & 0x3) << 0;
             reg |= (dev->reg.i_lobuf  & 0x1) << 2;
             reg |= (dev->reg.i_lodiv  & 0x1) << 3;
             break;           
         case 0x13:
             reg  = (dev->reg.txolv   & 0x3) << 0;
             reg |= (dev->reg.dummy2  & 0x1) << 2;
             reg |= (dev->reg.daccnt  & 0x1) << 3;
             break;     
         case 0x14:
             reg  = (dev->reg.lpmode_lna & 0x1) << 0;
             reg |= (dev->reg.main_path  & 0x1) << 1;
             reg |= (dev->reg.ana_path   & 0x1) << 2;
             reg |= (dev->reg.iq_sel     & 0x1) << 3;
             reg |= (dev->reg.rxlpf_fc   & 0x1) << 4;
             reg |= (dev->reg.dummy3     & 0x1) << 5;
             reg |= (dev->reg.lpmode_dem & 0x1) << 6;
             break;       
         case 0x15:
             reg = dev->reg.pga_gain_i & 0x1f;
             break;  
         case 0x16:
             reg = dev->reg.pga_gain_q & 0x1f;
             break;    
         case 0x17:
             reg  = (dev->reg.ofscal1   & 0x1) << 0;
             reg |= (dev->reg.ofscal2   & 0x1) << 1;
             reg |= (dev->reg.ofscal3   & 0x1) << 2;
             reg |= (dev->reg.ofscal4   & 0x1) << 3;
             reg |= (dev->reg.ofs2reg   & 0x1) << 4;
             reg |= (dev->reg.cal_lnapd & 0x1) << 5;
             break; 
         case 0x18:
             reg  = (dev->reg.chofs_ave   & 0x3) << 0;
             reg |= (dev->reg.agc_ofs_ave & 0x3) << 2;
             break;   
         case 0x19:
             reg = (dev->reg.ofst_i >> 16) & 0xff;
             break;
         case 0x1a:
             reg = (dev->reg.ofst_i >> 8) & 0xff;
             break;
         case 0x1b:
             reg = (dev->reg.ofst_i >> 0) & 0xff;
             break;   
         case 0x1c:
             reg = (dev->reg.ofst_q >> 16) & 0xff;
             break;
         case 0x1d:
             reg = (dev->reg.ofst_q >> 8) & 0xff;
             break;
         case 0x1e:
             reg = (dev->reg.ofst_q >> 0) & 0xff;
             break;  
         case 0x1f:
             reg  = (dev->reg.agc_off      & 0x1) << 0;
             reg |= (dev->reg.agc_keepr    & 0x1) << 1;
             reg |= (dev->reg.agc_keep_sel & 0x1) << 2;
             reg |= (dev->reg.agc_hys      & 0x3) << 3;
             reg |= (dev->reg.agc_tim      & 0x7) << 5;
             break;     
         case 0x20:
             reg  = (dev->reg.agc_tgt     & 0x7) << 0;
             reg |= (dev->reg.agc_max     & 0x7) << 3;
             reg |= (dev->reg.lna_lgmode  & 0x1) << 6;
             reg |= (dev->reg.lna_agc_off & 0x1) << 7;
             break;  
         case 0x21:
             reg  = (dev->reg.agc_trw    & 0x7) << 0;
             reg |= (dev->reg.agckp_mode & 0x3) << 3;
             reg |= (dev->reg.fb_rdoc    & 0x1) << 5;
             break;  
         case 0x22:
             reg  = (dev->reg.dfil_sel  & 0xf) << 0;
             reg |= (dev->reg.dfil_clk  & 0x1) << 4;
             reg |= (dev->reg.dfil_prog & 0x1) << 5;
             reg |= (dev->reg.dfil_sr   & 0x3) << 6;
             break; 
         case 0x23:
             reg  = (dev->reg.pfil_sift & 0x7) << 0;
             reg |= (dev->reg.pfil_sat  & 0x7) << 3;
             break; 
         case 0x24:
             reg  = (dev->reg.test11 & 0x1) << 0;
             reg |= (dev->reg.test10 & 0xf) << 3;
             reg |= (dev->reg.test9  & 0x1) << 5;
             break;  
         case 0x25:
             reg  = (dev->reg.test14 & 0x7) << 0;
             reg |= (dev->reg.test13 & 0x3) << 3;
             reg |= (dev->reg.test12 & 0x3) << 5;
             break;   
         case 0x26:
             reg  = (dev->reg.rdoc      & 0x1) << 0;
             reg |= (dev->reg.rdoc4     & 0x3) << 1;
             reg |= (dev->reg.rdoc3     & 0x3) << 3;
             reg |= (dev->reg.keep_rdoc & 0x1) << 5;
             reg |= (dev->reg.rdoc2     & 0x1) << 6;
             reg |= (dev->reg.rdoc1     & 0x1) << 7;
             break;   
         case 0x27:
             reg  = (dev->reg.rdoc7  & 0x3) << 0;
             reg |= (dev->reg.rdoc6  & 0x3) << 2;
             reg |= (dev->reg.rdoc5  & 0x7) << 4;
             reg |= (dev->reg.rdoc18 & 0x1) << 7;
             break;     
         case 0x28:
             reg  = (dev->reg.rdoc9     & 0x3) << 0;
             reg |= (dev->reg.rodc_fm   & 0x1) << 2;
             reg |= (dev->reg.ofst_rsel & 0x3) << 3;
             reg |= (dev->reg.rdoc8     & 0x3) << 5;
             break; 
         case 0x29:
             reg = (dev->reg.ofset2 >> 16) & 0x3;
             break;
         case 0x2a:
             reg = (dev->reg.ofset2 >> 8) & 0xff;
             break;
         case 0x2b:
             reg = (dev->reg.ofset2 >> 0) & 0xff;
             break; 
         case 0x2c:
             reg  = (dev->reg.rssiavg  & 0x7) << 0;
             reg |= (dev->reg.rssi_low & 0x3) << 3;
             break;    
         case 0x2d:
             reg  = (dev->reg.coef_st  & 0x1)  << 0;
             reg |= (dev->reg.coef_num & 0x7f) << 1;
             break;   
         case 0x2e:
             reg  = (dev->reg.pd_ref_n    & 0x1) << 0;
             reg |= (dev->reg.pd_dac_n    & 0x1) << 1;
             reg |= (dev->reg.pd_adc_n    & 0x1) << 2;
             reg |= (dev->reg.pd_synth_n  & 0x1) << 3;
             reg |= (dev->reg.pd_txr_n    & 0x1) << 4;
             reg |= (dev->reg.pd_rxr_n    & 0x1) << 5;
             reg |= (dev->reg.pd_lna_n    & 0x1) << 6;
             reg |= (dev->reg.pd_clkbuf_n & 0x1) << 7;
             break; 
         case 0x2f:
             reg  = (dev->reg.rpga_i       & 0x3f) << 0;
             reg |= (dev->reg.r_lna_lgmode & 0x1)  << 6;
             break;     
         case 0x30:
             reg = dev->reg.rpga_q & 0x3f;
             break;    
         case 0x31:
             reg = (dev->reg.r_ofst_i >> 16) & 0xff;
             break;
         case 0x32:
             reg = (dev->reg.r_ofst_i >> 8) & 0xff;
             break;
         case 0x33:
             reg = (dev->reg.r_ofst_i >> 0) & 0xff;
             break; 
         case 0x34:
             reg = (dev->reg.r_ofst_q >> 16) & 0xff;
             break;
         case 0x35:
             reg = (dev->reg.r_ofst_q >> 8) & 0xff;
             break;
         case 0x36:
             reg = (dev->reg.r_ofst_q >> 0) & 0xff;
             break;    
         case 0x37:
             reg = dev->reg.tapnum & 0x7f;
             break;   
         case 0x38:
             reg = (dev->reg.r_coef >> 8) & 0xff;
             break;
         case 0x39:
             reg = (dev->reg.r_coef >> 0) & 0xff;
             break;    
         case 0x3a: 
             reg = dev->reg.rssi;
             break;         
         case 0x3b: 
             reg = dev->reg.ofst1h_i & 0x3f;
             break;    
         case 0x3c: 
             reg = dev->reg.ofst1h_q & 0x3f;
             break;  
         case 0x3d: 
             reg = dev->reg.ofst1l_i & 0x3f;
             break;    
         case 0x3e: 
             reg = dev->reg.ofst1l_q & 0x3f;
             break; 
         case 0x3f: 
             reg = dev->reg.ld_lockcnt;
             break; 
         case 0x40: 
             reg = dev->reg.ld_unlockcnt;
             break;  
         case 0x41: 
             reg = dev->reg.dummy4;
             break;  
         case 0x42: 
             reg = dev->reg.ph_adj & 0x3f;
             break;     
         case 0x43: 
             reg = dev->reg.r_ph_adj & 0x3f;
             break;    
         case 0x44:
             reg  = (dev->reg.rdoc12 & 0x7) << 0;
             reg |= (dev->reg.rdoc11 & 0x3) << 3;
             reg |= (dev->reg.rdoc10 & 0x3) << 5;
             reg |= (dev->reg.r_rdoc & 0x1) << 7;
             break;  
         case 0x45: 
             reg = dev->reg.rdoc13;
             break;  
         case 0x46:
             reg  = (dev->reg.rdoc17 & 0x3) << 0;
             reg |= (dev->reg.rdoc16 & 0x3) << 2;
             reg |= (dev->reg.rdoc15 & 0x3) << 4;
             reg |= (dev->reg.rdoc14 & 0x3) << 6;
             break;   
         case 0x47: 
             reg = dev->reg.lna_tgt_h & 0x3f;
             break; 
         case 0x48: 
             reg = dev->reg.lna_tgt_l & 0x3f;
             break; 
         case 0x49: 
             reg = (dev->reg.pre_tstwe & 0x1) << 7;
             break;     
         case 0x4a:
             reg  = (dev->reg.dfil_clkg & 0x1) << 0;
             reg |= (dev->reg.dfil_acc  & 0x1) << 1;
             reg |= (dev->reg.test1     & 0x1) << 2;
             reg |= (dev->reg.test8     & 0x1) << 3;
             reg |= (dev->reg.test7     & 0x3) << 4;
             reg |= (dev->reg.test6     & 0x1) << 6;
             reg |= (dev->reg.do_mode   & 0x1) << 7;
             break; 
         case 0x4b:
             reg  = (dev->reg.test15    & 0x1) << 0;
             reg |= (dev->reg.rssi_sts  & 0x1) << 1;
             reg |= (dev->reg.agc_sts   & 0x1) << 2;
             reg |= (dev->reg.lnalg_sts & 0x1) << 3;
             reg |= (dev->reg.test5     & 0x1) << 4;
             reg |= (dev->reg.test4     & 0x1) << 5;
             reg |= (dev->reg.test3     & 0x1) << 6;
             reg |= (dev->reg.test2     & 0x1) << 7;
             break;  
         case 0x4c: 
             reg = dev->reg.keep_rd_dly & 0x3f;
             break;  
         case 0x4d:
             reg  = (dev->reg.rdoc23 & 0x3) << 0;
             reg |= (dev->reg.rdoc22 & 0x3) << 2;
             reg |= (dev->reg.rdoc21 & 0x3) << 4;
             reg |= (dev->reg.rdoc20 & 0x1) << 6;
             reg |= (dev->reg.rdoc19 & 0x1) << 7;
             break;   
         case 0x4e:
             reg  = (dev->reg.keep_hpf2 & 0x1) << 0;
             reg |= (dev->reg.hpf2_fc   & 0xf) << 1;
             reg |= (dev->reg.hpf2sel   & 0x1) << 5;
             break; 
         case 0x4f: 
             reg = dev->reg.keep_hpf2_dly_1 & 0x3f;
             break;      
         case 0x50: 
             reg = dev->reg.keep_hpf2_dly_2 & 0x3f;
             break; 
         case 0x51: 
             reg = dev->reg.test16;
             break; 
         case 0x52: 
             reg = dev->reg.srst;
             break;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   
     }
 
     return reg;
 }
 
 
 static int ak2401a_read_reg(struct spi_device *spi_device,u8 addr, u8 *value)
 {
     int error = -1;
     u16 reg = 0;
     struct spi_message *message;   //定义发送的消息
     struct spi_transfer *transfer; //定义传输结构体
     u16 read_value;
 
     if(addr < AK2401A_REGS_NUM)
     {
     }
     else 
     {
         printk(KERN_ERR "%s %d: read error %02x  - addr error! \n",__FUNCTION__,__LINE__,addr);
         error = -2;
         goto error_out;
     }
 
     reg = 0x80;
     reg |= (addr & 0x7f) << 0;  /* MSB 0 -  */
      
     /*申请空间*/
     message = kzalloc(sizeof(struct spi_message), GFP_KERNEL);
     transfer = kzalloc(sizeof(struct spi_transfer), GFP_KERNEL);
 
     if(message != 0 && transfer !=0 )
     {
         /*填充message和transfer结构体*/
         transfer->tx_buf = &reg;
         transfer->rx_buf = &read_value;
         transfer->len = 2;
         spi_message_init(message);
         spi_message_add_tail(transfer, message);
 
         error = spi_sync(spi_device, message);
         kfree(message);
         kfree(transfer);
         if (error != 0)
         {
             error = -3;
             printk(KERN_ERR "%s %d: read error %02x! \n",__FUNCTION__,__LINE__,addr);
         }
         else 
         {
             *value = (u8)((read_value >>8) & 0xff);
            //  printk(KERN_INFO "%s %d: %02x : %02x(%04x)! \n",__FUNCTION__,__LINE__,addr,*value,read_value);
         }
     }
     else 
     {
         error = -4;
          printk(KERN_ERR "%s %d: allcate memory error %02x ! \n",__FUNCTION__,__LINE__,addr);
     }
 
 error_out:
     return error;
 }
 
 static int ak2401a_write_reg(struct spi_device *spi_device,u8 addr, u8 value)
 {
     int error = -1;
     u16 reg = 0;
     struct spi_message *message;   //定义发送的消息
     struct spi_transfer *transfer; //定义传输结构体
     u8 regv[3] = {0};
 
     if(addr < AK2401A_REGS_NUM)
     {
     }
     else 
     {
         printk(KERN_ERR "%s %d: write error %02x : %02x - addr error! \n",__FUNCTION__,__LINE__,addr,value);
         error = -2;
         goto error_out;
     }
    reg = (value & 0xff) << 8;
    reg |= (addr & 0x7f) << 0;  /* MSB 0 -  */
 
    regv[0] = addr & 0x7f;
    regv[1] = value & 0xff;
      
    /*申请空间*/
    message = kzalloc(sizeof(struct spi_message), GFP_KERNEL);
    transfer = kzalloc(sizeof(struct spi_transfer), GFP_KERNEL);
 
    if(message != 0 && transfer !=0 )
    {
        /*填充message和transfer结构体*/
        transfer->tx_buf = &reg;
        transfer->len = 2;
        spi_message_init(message);
        spi_message_add_tail(transfer, message);
 
        error = spi_sync(spi_device, message);
        kfree(message);
        kfree(transfer);
        if (error != 0)
        {
            error = -3;
            printk(KERN_ERR "%s %d: write error %02x : %02x! \n",__FUNCTION__,__LINE__,addr,value);
        }
        else 
        {
            // printk(KERN_INFO "%s: %02x : %02x! \n",spi_device->dev.init_name,addr,value);
            // dev_err(&spi_device->dev, " %02x : %02x! \n",addr,value);
        }
    }
    else 
    {
        error = -4;
        printk(KERN_ERR "%s %d: allcate memory error %02x : %02x! \n",__FUNCTION__,__LINE__,addr,value);
    }
 
 error_out:
     return error;
 }
 
 static ts_ak2401a_freq_regs freq_regs;
 /* src : 0 - spi. 1 - mcasp*/
 static void ak2401_freq_set(ts_ak2401a_dev *dev,u32 freq,u8 src)
 {
    u16 Nreg;
    u32 Dividend;
    long long detaD;
    long long uintDectect;
    u32 detectFreq;
    int i = 0;
    u8 reg_addr;
    u8 reg_value;

    detectFreq = dev->info.phase_detect_freq;

    Nreg  = freq / detectFreq;
    detaD = (long long)(freq - Nreg*detectFreq) << 16;
    uintDectect = (long long)((long long)detectFreq << 16) / 262144;

    Dividend = detaD / uintDectect;

    dev->reg.integer = Nreg;
    dev->reg.frac = Dividend;

    // pr_err("ak2401a set frequency:%u pdf:%u mod:%x,detaD = %llu uintDectect = %llu integer =  %u, frac = %u\n",freq,detectFreq,dev->reg.mod,detaD,uintDectect,Nreg, Dividend);
    for(i = 0;i < sizeof(ak2401_frequency_regs);i++)
    {
        reg_addr = ak2401_frequency_regs[i];
        reg_value = ak2401a_reg_get(dev, reg_addr);
        if( 0 == src)
        {
            ak2401a_write_reg(dev->spi, reg_addr, reg_value);
        }
        freq_regs.reg[i].addr = reg_addr;
        freq_regs.reg[i].value = reg_value;
    }
    // printk("done! \r\n");
 }
 
 static void ak2401_freq_offset_set(ts_ak2401a_dev *dev,u32 freq,u8 src)
 {
     u16 Nreg = 0;
     u32 Dividend = 0;
     long long detaD = 0;
     long long uintDectect = 0;
     u32 detectFreq = dev->info.phase_detect_freq;
     int i = 0;
     u8 reg_addr;
     u8 reg_value;
 
     if(freq != 0)
     {
         detaD = (long long)(freq) << 16;
         uintDectect = (long long)((long long)detectFreq << 16) / 262144;
 
         Dividend = detaD / uintDectect;
 
         dev->reg.ofset1 = Dividend;
     }
     else  
     {
         Dividend = 0;
         dev->reg.ofset1 = 0;
     }
 
 
 
     freq_regs.reg[0].addr = 0x0f;
     freq_regs.reg[0].value = ak2401a_reg_get(dev, 0x0f);    
 
     freq_regs.reg[1].addr = 0x10;
     freq_regs.reg[1].value = ak2401a_reg_get(dev, 0x10);  
 
     freq_regs.reg[2].addr = 0x11;
     freq_regs.reg[2].value = ak2401a_reg_get(dev, 0x11);  
 
    //  pr_err("\r\nak2401a set frequency:%u Hz offset %u Hz == pdf:%u mod:%x,detaD = %llu uintDectect = %llu offset_value = %u(%0x ==? %x%x%x)\n"
    //                                           ,dev->info.frequency,freq,dev->info.phase_detect_freq,dev->reg.mod
    //                                           ,detaD,uintDectect
    //                                           ,Dividend,Dividend
    //                                           ,freq_regs.reg[0].value,freq_regs.reg[1].value,freq_regs.reg[2].value);
 }
 
 static int ak2401_dev_open(struct inode *inode, struct file *filp)
 {
     ts_ak2401a_dev *dev = container_of(inode->i_cdev, ts_ak2401a_dev, cdev);
     filp->private_data = dev;
     // printk("\r\n%s open.",dev->name);
     return 0;
 }
 
 static int ak2401_dev_close(struct inode *inode, struct file *filp)
 {
     return 0;
 }
 
 void ak2401a_rst_onoff(ts_ak2401a_dev *dev,u32 flag)
 {
     if(flag)
     {
         gpiod_set_raw_value(gpio_to_desc(dev->rst_pin), 1);
     }
     else 
     {
         gpiod_set_raw_value(gpio_to_desc(dev->rst_pin), 0);
     }
    //  pr_err("\r\n%s rst_pin(%d)%s \n",dev->name, dev->rst_pin,flag == 0 ? "Off":"On");
 }
 
 
 void ak2401a_rxpdn_onoff(ts_ak2401a_dev *dev,u32 flag)
 {
     if(flag)
     {
         gpiod_set_raw_value(gpio_to_desc(dev->rxpdn_pin), 1);
     }
     else 
     {
         gpiod_set_raw_value(gpio_to_desc(dev->rxpdn_pin), 0);
     }
    //  pr_err("\r\n%s rxpdn_pin(%d)%s \n",dev->name, dev->rxpdn_pin,flag == 0 ? "Off":"On");
 }
 
 void ak2401a_txpdn_onoff(ts_ak2401a_dev *dev,u32 flag)
 {
     if(flag)
     {
         gpiod_set_raw_value(gpio_to_desc(dev->txpdn_pin), 1);
     }
     else 
     {
         gpiod_set_raw_value(gpio_to_desc(dev->txpdn_pin), 0);
     }
    //  pr_err("\r\n%s txpdn_pin(%d)%s \n",dev->name, dev->txpdn_pin,flag == 0 ? "Off":"On");
 }
 
 
 void ak2401a_agc_onoff(ts_ak2401a_dev *dev,u32 flag)
 {
     if(flag)
     {
         gpiod_set_raw_value(gpio_to_desc(dev->agc_pin), 1);
     }
     else 
     {
         gpiod_set_raw_value(gpio_to_desc(dev->agc_pin), 0);
     }
    //  pr_err("\r\n%s agc_pin(%d)%s \n",dev->name, dev->agc_pin,flag == 0 ? "Off":"On");
 }

static void ak2401_work_mode_process(ts_ak2401a_dev *dev,ts_ak2401_mode_type *mode)
{
    if(AK2401_WORK_MODE_IDLE == mode->work_mode)
    {
        gpiod_set_raw_value(gpio_to_desc(dev->rxpdn_pin), 0);
        gpiod_set_raw_value(gpio_to_desc(dev->txpdn_pin), 0);
    }
    else if(AK2401_WORK_MODE_RX == mode->work_mode)
    {
        gpiod_set_raw_value(gpio_to_desc(dev->txpdn_pin), 0);
        gpiod_set_raw_value(gpio_to_desc(dev->rxpdn_pin), 1);
    }
    else if(AK2401_WORK_MODE_TX == mode->work_mode)
    {
        gpiod_set_raw_value(gpio_to_desc(dev->txpdn_pin), 1);
        gpiod_set_raw_value(gpio_to_desc(dev->rxpdn_pin), 0);
    }
    else 
    {
        //wrong info,do nothing
        dev_warn(dev->device, "akm work mode(%d) err \n",mode->work_mode);
    }

    /*确定是否需要配置频率*/
    if( AK2401_WORK_MODE_RX == mode->work_mode
     || AK2401_WORK_MODE_TX == mode->work_mode)
    {
        if(mode->freq_info.freq != dev->info.frequency)
        {
            /*freq change */
            dev->info.frequency = mode->freq_info.freq;
            freq_regs.path = mode->freq_info.path;
            ak2401_freq_set(dev, dev->info.frequency * 2, freq_regs.path);       
            memcpy(&mode->freq_info,&freq_regs,  sizeof(ts_ak2401a_freq_regs));    
        }
        else  
        {
            mode->freq_info.freq = 0;/*如果不需要配置频率，则置0*/
        }
    }


}
 
 static long ak2401_ioctl(struct file *filp,u32 cmd, unsigned long args)
 {
    long error = 0;
    unsigned int value;
    ts_ak2401_reg_type reg;
    ts_ak2401a_dev *dev = (ts_ak2401a_dev *)filp->private_data;
    int rssi;
    ts_ak2401a_ld_type ld;    
     
     switch(cmd)
     {
         case AK2401A_CMD_PRINT_REGS:
             break;
         case AK2401A_CMD_SET_FREQ:
             if(copy_from_user(&(dev->info.frequency), (u32*)args, sizeof(u32)))
             {
                 error = -EFAULT;
                 printk( KERN_ERR "%s %d:set freq error!\n",__FUNCTION__,__LINE__);
                 goto error_out;                     
             }    
             ak2401_freq_set(dev, dev->info.frequency * 2, 0);    
             break;
         case AK2401A_CMD_WIRTE_REG:            
             if(copy_from_user(&reg, (ts_ak2401_reg_type*)args, sizeof(ts_ak2401_reg_type)))
             {
                 error = -EFAULT;
                 printk( KERN_ERR "%s %d:set freq error!\n",__FUNCTION__,__LINE__);
                 goto error_out;                     
             }         
             ak2401a_write_reg(dev->spi,reg.addr, reg.value );
             break;
         case AK2401A_CMD_READ_REG:            
             if(copy_from_user(&reg, (ts_ak2401_reg_type*)args, sizeof(ts_ak2401_reg_type)))
             {
                 error = -EFAULT;
                 printk( KERN_ERR "%s %d:set freq error!\n",__FUNCTION__,__LINE__);
                 goto error_out;                     
             }         
             ak2401a_read_reg(dev->spi,reg.addr, &reg.value);
             if(copy_to_user((ts_ak2401_reg_type*)args, &reg, sizeof(ts_ak2401_reg_type)))
             {
                 error = -EFAULT;
                 printk( KERN_ERR "%s %d:ak2401a read reg %02x error\n",__FUNCTION__,__LINE__,reg.addr);
                 goto error_out;                  
             }
             break;            
         case AK2401A_CMD_RST_CTRL:
             if(copy_from_user(&dev->regs, (ts_ak2401a_regs*)args, sizeof(ts_ak2401a_regs)))
             {
                 error = -EFAULT;
                 printk( KERN_ERR "%s %d:reset error!\n",__FUNCTION__,__LINE__);
                 goto error_out;                     
             } 
             value = dev->regs.rst_flag;
             if(value)
             {
                 ak2401_init(dev, dev->regs.path);
             }        
             else 
             {
                 ak2401a_rst_onoff(dev, 0);
                 ak2401a_rxpdn_onoff(dev, 0);
                 ak2401a_txpdn_onoff(dev, 0);            
             }
 
             if(1 == dev->regs.path)
             {
                 if(copy_to_user((ts_ak2401a_regs*)args, &dev->regs, sizeof(ts_ak2401a_regs)))
                 {
                     error = -EFAULT;
                     printk( KERN_ERR "%s %d:ak2401a regs error\n",__FUNCTION__,__LINE__);
                     goto error_out;                  
                 }            
             }
             pr_err("\r\nakm2401a %s %s\r\n",dev->regs.path == 0? "2nd":"1st",value == 0 ?"off." : "reset.");
             break;   
         case AK2401A_CMD_RXPDN_CTRL:
             if(copy_from_user(&value, (u32*)args, sizeof(u32)))
             {
                 error = -EFAULT;
                 printk( KERN_ERR "%s %d:set freq error!\n",__FUNCTION__,__LINE__);
                 goto error_out;                     
             } 
             ak2401a_rxpdn_onoff(dev, value);        
             break;
         case AK2401A_CMD_TXPDN_CTRL:
             if(copy_from_user(&value, (u32*)args, sizeof(u32)))
             {
                 error = -EFAULT;
                 printk( KERN_ERR "%s %d:set freq error!\n",__FUNCTION__,__LINE__);
                 goto error_out;                     
             } 
             ak2401a_txpdn_onoff(dev, value);        
             break;       
         case AK2401A_CMD_AGC_KEEP_CTRL:
             if(copy_from_user(&value, (u32*)args, sizeof(u32)))
             {
                 error = -EFAULT;
                 printk( KERN_ERR "%s %d:set freq error!\n",__FUNCTION__,__LINE__);
                 goto error_out;                     
             } 
             ak2401a_agc_onoff(dev, value);        
             break;  
         case AK2401A_CMD_LD_CTRL:
             ld.ld_flag = gpio_get_value(dev->ld_pin);// dev->info.ld_flag;
             ld.freq = dev->info.frequency;
             if(copy_to_user((u32*)args, &ld, sizeof(ts_ak2401a_ld_type)))
             {
                 error = -EFAULT;
                 printk( KERN_ERR "%s %d:ak2401a read frequency detect flag!\n",__FUNCTION__,__LINE__);
                 goto error_out;  
             }
             break;
         case AK2401A_CMD_RSSI_CTRL:
             ak2401a_rssi_get(dev,&rssi);
             if(copy_to_user((u32*)args, &rssi, sizeof(u32)))
             {
                 error = -EFAULT;
                 printk( KERN_ERR "%s %d:ak2401a read frequency detect flag!\n",__FUNCTION__,__LINE__);
                 goto error_out;  
             }
             break;
         case AK2401A_CMD_INIT_REGS:               
             if(copy_to_user((ts_ak2401a_regs*)args, &dev->regs, sizeof(ts_ak2401a_regs)))
             {
                 error = -EFAULT;
                 printk( KERN_ERR "%s %d:ak2401a  regs error\n",__FUNCTION__,__LINE__);
                 goto error_out;                  
             }
             break;
         case AK2401A_CMD_FREQ_REGS: 
             if(copy_from_user(&freq_regs, (ts_ak2401a_freq_regs*)args, sizeof(ts_ak2401a_freq_regs)))
             {
                 error = -EFAULT;
                 printk( KERN_ERR "%s %d:set freq error from mcasp!\n",__FUNCTION__,__LINE__);
                 goto error_out;                     
             }         
             dev->info.frequency = freq_regs.freq;
             ak2401_freq_set(dev, dev->info.frequency * 2, freq_regs.path);       
             if(copy_to_user((ts_ak2401a_freq_regs*)args, &freq_regs, sizeof(ts_ak2401a_freq_regs)))
             {
                 error = -EFAULT;
                 printk( KERN_ERR "%s %d:ak2401a frequency %u Hz error \n",__FUNCTION__,__LINE__,freq_regs.freq);
                 goto error_out;                  
             }
             break;       
         case AK2401A_CMD_OFFSET_REGS:
             if(copy_from_user(&freq_regs, (ts_ak2401a_freq_regs*)args, sizeof(ts_ak2401a_freq_regs)))
             {
                 error = -EFAULT;
                 printk( KERN_ERR "%s %d:set freq error from mcasp!\n",__FUNCTION__,__LINE__);
                 goto error_out;                     
             }         
             dev->info.offset = freq_regs.freq;
             ak2401_freq_offset_set(dev, dev->info.offset * 2, freq_regs.path);       
             if(copy_to_user((ts_ak2401a_freq_regs*)args, &freq_regs, sizeof(ts_ak2401a_freq_regs)))
             {
                 error = -EFAULT;
                 printk( KERN_ERR "%s %d:ak2401a frequency %u Hz error \n",__FUNCTION__,__LINE__,freq_regs.freq);
                 goto error_out;                  
             }        
             break;       
         default:
             break;                          
     }
 error_out:
     return error;
 }



long ak2401_ioctl_kenerl(struct file *filp,u32 cmd, void *args)
{
    long error = 0;
    unsigned int value;
    ts_ak2401_reg_type reg;
    ts_ak2401a_dev *dev = (ts_ak2401a_dev *)filp->private_data;
    int rssi;
    ts_ak2401a_ld_type ld;

    switch(cmd)
    {
        case AK2401A_CMD_PRINT_REGS:
            break;
        case AK2401A_CMD_SET_FREQ:
            // memcpy(&freq_regs, args,sizeof(ts_ak2401a_freq_regs));
            dev->info.frequency =* (unsigned int*)args;
            // dev_err(dev->device,"Locking %u Hz By %s(%d).\n",dev->info.frequency,freq_regs.path == 1?"mcasp":"spi",freq_regs.path);
            ak2401_freq_set(dev, dev->info.frequency * 2, 0);  
            break;
        case AK2401A_CMD_WIRTE_REG:        /**only akm 2401 config by spi,if mcasp config @ rfcontrol */           
            memcpy(&reg, (const void *)args, sizeof(ts_ak2401_reg_type));
            ak2401a_write_reg(dev->spi,reg.addr, reg.value );
            break;
        case AK2401A_CMD_READ_REG:                     
            memcpy(&reg, (const void *)args, sizeof(ts_ak2401_reg_type));
            ak2401a_read_reg(dev->spi,reg.addr, &reg.value);
            memcpy(args, (const void *)&reg, sizeof(ts_ak2401_reg_type));   
            break;            
        case AK2401A_CMD_RST_CTRL:
            memcpy(&dev->regs, (const void *)args, sizeof(ts_ak2401a_regs));
            value = dev->regs.rst_flag;
            if(value)
            {
                ak2401_init(dev, dev->regs.path);
            }        
            else 
            {
                ak2401a_rst_onoff(dev, 0);
                ak2401a_rxpdn_onoff(dev, 0);
                ak2401a_txpdn_onoff(dev, 0);            
            }

            if(1 == dev->regs.path)
            {
                memcpy((void *)args, &dev->regs, sizeof(ts_ak2401a_regs));          
            }
            // pr_err("\r\nakm2401a %s %s\r\n",dev->regs.path == 0? "2nd":"1st",value == 0 ?"off." : "reset.");
            break;   
        case AK2401A_CMD_RXPDN_CTRL:
            value = * ((u32*)args);
            ak2401a_rxpdn_onoff(dev, value);        
            break;
        case AK2401A_CMD_TXPDN_CTRL:
            value = * ((u32*)args);
            ak2401a_txpdn_onoff(dev, value);        
            break;       
        case AK2401A_CMD_AGC_KEEP_CTRL:
            value = * ((u32*)args);
            ak2401a_agc_onoff(dev, value);        
            break;  
        case AK2401A_CMD_LD_CTRL:
            ld.ld_flag = gpio_get_value(dev->ld_pin);// dev->info.ld_flag;
            ld.freq = dev->info.frequency;
            // dev_err(dev->device,"%u Hz is %s!\n",ld.freq,ld.ld_flag == 1?"Locked":"Unlocked" );
            memcpy((void *)args, &ld, sizeof(ts_ak2401a_ld_type));   
            break;
        case AK2401A_CMD_RSSI_CTRL:
            ak2401a_rssi_get(dev,&rssi);
            *((u32*)args) = rssi;
            break;
        case AK2401A_CMD_INIT_REGS:               

            break;
        case AK2401A_CMD_FREQ_REGS: 
            memcpy(&freq_regs, (const void *)args, sizeof(ts_ak2401a_freq_regs));        
            dev->info.frequency = freq_regs.freq;
            // dev_err(dev->device,"Locking %u Hz By %s.\n",dev->info.frequency,freq_regs.path == 1?"mcasp":"spi");
            ak2401_freq_set(dev, dev->info.frequency * 2, freq_regs.path);       
            memcpy((void *)args,&freq_regs,  sizeof(ts_ak2401a_freq_regs));
            break;       
        case AK2401A_CMD_OFFSET_REGS:
            memcpy(&freq_regs, (const void *)args, sizeof(ts_ak2401a_freq_regs));        
            dev->info.offset = freq_regs.freq;
            ak2401_freq_offset_set(dev, dev->info.offset * 2, freq_regs.path);    
            memcpy((void *)args,&freq_regs,  sizeof(ts_ak2401a_freq_regs));
            break;       
        case AK2401A_CMD_MODE_SET:
            ak2401_work_mode_process(dev,(ts_ak2401_mode_type*)args);
            break;
        default:
            break;                          
    }

    return error;
}
EXPORT_SYMBOL(ak2401_ioctl_kenerl);
 
 static struct file_operations ak2401_fops = {
     .owner = THIS_MODULE,
     .open = ak2401_dev_open,
     .release = ak2401_dev_close,
     .unlocked_ioctl = ak2401_ioctl,
 };
 
 static irqreturn_t ld_handle_irq(int irq, void *dev_id)
 {
     ts_ak2401a_dev *dev = (ts_ak2401a_dev *)dev_id;
     int value;
 
     value = gpio_get_value(dev->ld_pin);
     if(1 == value)
     {
         pr_err("%s:%u Hz locked! ",dev->name,dev->info.frequency);
         dev->info.ld_flag = 1;
     }
     else 
     {
         pr_err("%s:%u Hz unlocked! ",dev->name,dev->info.frequency);
         dev->info.ld_flag = 0;
     }
     return IRQ_HANDLED;
 }
 /* src : 0 - spi 1 - mcasp*/
 static void ak2401_init(ts_ak2401a_dev *dev, u8 src)
 {
     int i = 0;
     int j = 0;
 
     dev->info.frequency = 375000000;
 
     dev->reg.mod = 0x3ffff; /*2^18 = 262144*/
 
     ak2401a_rst_onoff(dev, 0);
     mdelay(10);
     ak2401a_rst_onoff(dev, 1);
 
     ak2401a_rxpdn_onoff(dev, 0);
     ak2401a_txpdn_onoff(dev, 0);
 
     // while(1)
     // {
     //     ak2401a_write_reg(dev->spi,0xaa,0x55);
     //     mdelay(5);
     // }
 
     //if(0 == src)
     {
         //ak2401a_write_reg(dev->spi,0x2e,0xef);
     }
     mdelay(10);
     dev->regs.num = ARRAY_SIZE(gak2401regs_default) >> 1;
     for(i = 0,j = 0; i < ARRAY_SIZE(gak2401regs_default);)
     {
         if(0 == src)
         {
             ak2401a_write_reg(dev->spi,gak2401regs_default[i],gak2401regs_default[i+1]);
         }
         dev->regs.reg[j].addr = gak2401regs_default[i];
         dev->regs.reg[j].value = gak2401regs_default[i+1];
         i = i + 2;
         j++;
     }    
 }
 
 static int ak2401a_rssi_get(ts_ak2401a_dev *dev,int *rssi)
 {
     int rssi_tmp;
     u8 output_code;
     u8 rssi_low;
     int ret;
 
     ret = ak2401a_read_reg(dev->spi, 0x3a, &output_code);
     ret = ak2401a_read_reg(dev->spi, 0x2c, &rssi_low);
     rssi_low = rssi_low & 0x3;
     rssi_tmp = (output_code - 290) >> 1;
     rssi_tmp += rssi_low;
 
     *rssi = rssi_tmp;
 
     return ret;
 }
 /*********************************以下模拟SND设备*****************************/
 static int ak2401a_component_probe(struct snd_soc_component *component)
 {
     // dev_info(component->dev,"ak2401a_component_probe\n");
     //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
     return 0;
 }
 
 static int ak2401a_set_bias_level(struct snd_soc_component *component, enum snd_soc_bias_level level)
 {
     // dev_info(component->dev,"ak2401a_set_bias_level\n");
     //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
     return 0;
 }
 
 
 static unsigned int ak2401a_snd_reg_read(struct snd_soc_component *component, unsigned int reg)
 {
     // dev_info(component->dev,"ak2401a_snd_reg_read:%02x\n", reg);
     //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
     return 0;
 }
 static int ak2401a_snd_reg_write(struct snd_soc_component *component, unsigned int reg, unsigned int val)
 {
     // dev_info(component->dev,"ak2401a_snd_reg_write:%02x:%02x\n", reg, val);
     //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
     return 0;    
 }
 
 static const struct snd_kcontrol_new ak2401a_snd_controls[] = {
     SOC_SINGLE("Power off", 0x21, 7, 1, 0), //todo:0x21 for temp
 };
 
 static const struct snd_soc_dapm_widget ak2401a_dapm_widgets[] = {
     SND_SOC_DAPM_DAC("DATA DAC", "AK2401A DAC", 0x21, 7, 0), //todo: 0x21 tmp
     SND_SOC_DAPM_ADC("DATA ADC", "AK2401A ADC", 0x21, 7, 0), //todo: 0x21 tmp
     SND_SOC_DAPM_OUTPUT("DA_SDO"),
     SND_SOC_DAPM_INPUT("AD_SDI"),
 };

 static const struct snd_soc_dapm_route aK2401A_dapm_routes[] = {
    /* Sink   ， Control，  Source*/
     {"DATA ADC", NULL, "AD_SDI"},
     {"DA_SDO", NULL, "DATA DAC"},    
 };
 
 static const struct snd_soc_component_driver soc_component_dev_ak2401a = 
 {
     .probe = ak2401a_component_probe,
     .set_bias_level = ak2401a_set_bias_level,
     .legacy_dai_naming = 0,
     .controls = ak2401a_snd_controls,
     .num_controls = ARRAY_SIZE(ak2401a_snd_controls),
     .dapm_widgets = ak2401a_dapm_widgets,
     .num_dapm_widgets = ARRAY_SIZE(ak2401a_dapm_widgets),
     .dapm_routes = aK2401A_dapm_routes,
     .num_dapm_routes = ARRAY_SIZE(aK2401A_dapm_routes),
     .read = ak2401a_snd_reg_read,
     .write = ak2401a_snd_reg_write,
 
 };
 
 
#define AK2401A_RATES    SNDRV_PCM_RATE_8000_576000
#define AK2401A_FORMATS (SNDRV_PCM_FMTBIT_S8 | \
                 SNDRV_PCM_FMTBIT_U8 | \
                 SNDRV_PCM_FMTBIT_S16_LE | \
                 SNDRV_PCM_FMTBIT_U16_LE | \
                 SNDRV_PCM_FMTBIT_S20_LE | \
                 SNDRV_PCM_FMTBIT_U20_LE | \
                 SNDRV_PCM_FMTBIT_S24_LE | \
                 SNDRV_PCM_FMTBIT_U24_LE | \
                 SNDRV_PCM_FMTBIT_S24_3LE | \
                 SNDRV_PCM_FMTBIT_U24_3LE | \
                 SNDRV_PCM_FMTBIT_S32_LE | \
                 SNDRV_PCM_FMTBIT_U32_LE)
 
 static int ak2401a_hw_params(struct snd_pcm_substream *substream,
                  struct snd_pcm_hw_params *params,
                  struct snd_soc_dai *dai)
 {
     struct snd_soc_component *component = dai->component;
     // dev_info(component->dev,"%s %d:: Pass ,%s rate = %u period_size=%u\n",__FUNCTION__,__LINE__
     //                                     ,substream->pcm->name
     //                                     ,params_rate(params),params_period_size(params));
     //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
     return 0;
 }
 static int ak2401a_mute(struct snd_soc_dai *dai, int mute, int direction)
 {
     struct snd_soc_component *component = dai->component;
     // dev_info(component->dev,"ak2401a_mute mute = %d dir = %d \n", mute, direction);
     //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
     return 0;
 }
 static int ak2401a_set_dai_fmt(struct snd_soc_dai *codec_dai, unsigned int fmt)
 {
     struct snd_soc_component *component = codec_dai->component;
     // dev_info(component->dev,"ak2401a_fixed I2S, ADC-I/Q 2 TDM  64BIT / DAC 192K/96K 16BIT:fmt = 0x%08x\n",fmt);
     //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
     return 0;
 }
 static int ak2401a_set_dai_sysclk(struct snd_soc_dai *codec_dai,
                   int clk_id, unsigned int freq, int dir)
 {
     struct snd_soc_component *component = codec_dai->component;
     // dev_info(component->dev,"ak2401a set_dai_sysclk clk_id = %d freq = %u dir = %d\n",clk_id, freq, dir);
     //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
     return 0;
 }
 static int ak2401a_pcm_new_cb(struct snd_soc_pcm_runtime *rtd, struct snd_soc_dai *dai)
 {
     struct snd_soc_component *component = dai->component;
 
     // dev_info(component->dev,"ak2401a_pcm_new_cb\r\n\trtd->pcm->name :%s\r\n\trtd->dai_link->name:%s\r\n\t"\
     //                                     "rtd->dai_link->stream_name:%s\r\n"
     //                                                                  ,rtd->pcm->name
     //                                                                  ,rtd->dai_link->name
     //                                                                  ,rtd->dai_link->stream_name);
     //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
     return 0;
 }
 static const struct snd_soc_dai_ops ak2401a_dai_ops = {
     .hw_params = ak2401a_hw_params,
     .mute_stream = ak2401a_mute,
     .set_fmt = ak2401a_set_dai_fmt,
     .set_sysclk = ak2401a_set_dai_sysclk,
     .no_capture_mute = 1,
 };             
 static struct snd_soc_dai_driver ak2401a_dai[] = {
     {
         .name = "ak2401a_playback",
         .playback = {
             .stream_name = "Playback",
             .channels_min = 1,
             .channels_max = 2,
             .rates = AK2401A_RATES,
             .formats = AK2401A_FORMATS,            
         },
         .capture = {
             .stream_name = "Capture",
             .channels_min = 1,
             .channels_max = 2,
             .rates = AK2401A_RATES,
             .formats = AK2401A_FORMATS,        
         },        
         .ops = &ak2401a_dai_ops,
         .pcm_new = ak2401a_pcm_new_cb,
         .symmetric_rate = 0,
         // .id = 1,/* avoid call to fmt_single_name() */
     },
     {
         .name = "ak2401a_capture",
         .playback = {
             .stream_name = "Playback1",
             .channels_min = 1,
             .channels_max = 2,
             .rates = AK2401A_RATES,
             .formats = AK2401A_FORMATS,            
         },        
         .capture = {
             .stream_name = "Capture1",
             .channels_min = 1,
             .channels_max = 2,
             .rates = AK2401A_RATES,
             .formats = AK2401A_FORMATS,        
         },
         .pcm_new = ak2401a_pcm_new_cb,
         .ops = &ak2401a_dai_ops,
         .symmetric_rate = 0,
         // .id = 1,/* avoid call to fmt_single_name() */        
     }
 };
 
 static int simple_soc_probe(struct snd_soc_card *card)
 {
     dev_info(card->dev,"simple_soc_probe\n");
     //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
     return 0;    
 }
 
 int ak2401a_asoc_dai_init(struct snd_soc_pcm_runtime *rtd)
 {
     dev_info(rtd->dev,"ak2401a_asoc_dai_init\n");
     //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
     return 0;       
 }
 
 /*初始化类codec驱动
  *初始化类machine 驱动
  *platform驱动mcasp使用ti提供的驱动
  */
 static int ak2401a_data_card(ts_ak2401a_dev *dev)
 {
     int ret = 0;
 
     /* 类codec注册component
      * ak2401a1st 收发，注册两个
      * ak2401a2nd 仅收，注册capture
      */
     if(0 == strncmp("ak2401a1st", dev->np->name, 10)) 
     {
         // dev_err(dev->device,"ak2401a1st dai register capture and playback  %s!", ret == 0? "Pass" : "Fail" );
         ret = devm_snd_soc_register_component(&(dev->spi->dev), &soc_component_dev_ak2401a, ak2401a_dai, 2);
     }
     else if (0 == strncmp("ak2401a2nd", dev->np->name, 10)) {
         // dev_err(dev->device,"ak2401a2nd dai register capture  %s!", ret == 0? "Pass" : "Fail" );
         ret = devm_snd_soc_register_component(&(dev->spi->dev), &soc_component_dev_ak2401a, &ak2401a_dai[1], 1);
     }
     else
     {
         ret = 1;
         dev_err(dev->device,"cant found %s dai register %s!", dev->np->name, ret == 0? "Pass" : "Fail" );
     }
     
 
 err_out:    
     dev_err(dev->device,"%s dai register %s!", dev->np->name, ret == 0? "Pass" : "Fail" );
     return ret;
 }
 /*********************************以上模拟SND设备*****************************/
 
 static int ak2401a_drv_probe(struct spi_device *spi)
 {
     int ret = 0;
     int i = 0;
     ts_ak2401a_dev *dev;
     struct device_node *np;
     char *label;
 
     if(gRegisterCount > AK2401A_DEV_CNT)
     {
         printk("\r\nMore than system support.\r\n");
         goto error_out;
     }
   
     /*1st. request dev id*/
     if( gRegisterCount == 0)
     {
         ret = alloc_chrdev_region(&ak2401a_dev_id,0,AK2401A_DEV_CNT,AK2401A_NAME);// 0 --> 动态申请
         if(ret < 0)
         {
             goto error_out;
         }
     }
 
     /*2nd. init cdev*/
     dev = &gak2401a_handler[gRegisterCount];
     dev->dev_id = ak2401a_dev_id + gRegisterCount;
     dev->cdev.owner = THIS_MODULE;
     dev->dev_cnt = 1;
     snprintf(dev->name, 16, "%s%d", AK2401A_NAME,gRegisterCount);
     cdev_init(&dev->cdev, &ak2401_fops);
     ret = cdev_add(&dev->cdev, dev->dev_id, dev->dev_cnt);
     if(ret < 0)
     {
         goto fail_cdev;
     }        
     // printk("\r\ncdev ok:%s\r\n",dev->name);
     /*3rd. create dev under "/sys/class"*/
     if(0 == gRegisterCount)
     {
         ak2401a_dev_class = class_create(THIS_MODULE,AK2401A_NAME);
         if(IS_ERR(dev->class)){
             //class创建异常处理
             printk("class error!@%s %d\r\n",__FUNCTION__,__LINE__);
             ret = PTR_ERR(dev->class);
             goto fail_class;
         }
         // printk("dev class created@%s %d\r\n",__FUNCTION__,__LINE__);
     }
 
     dev->device = device_create(ak2401a_dev_class,NULL,dev->dev_id,NULL,"%s",dev->name);
     if(IS_ERR(dev->device)){
         //设备创建异常处理
         printk("device error!@%s %d\r\n",__FUNCTION__,__LINE__);
         ret = PTR_ERR(dev->device);
         goto fail_device;
     }
     
 
     /*init spi*/
     dev->spi = spi;
     dev->spi->mode = SPI_MODE_0;
     dev->spi->max_speed_hz = 2000000;
     spi_setup(dev->spi);
 
     /*init 2401a io*/
     dev->np = spi->dev.of_node;
     // dev_info(&spi->dev, "of node name:%s\n",dev->np->name);
     dev->ld_pin = of_get_named_gpio(dev->np, "ld-gpio", 0);
     if(dev->ld_pin < 0)
     {
         printk("\r\nGet ak2401 ld-gpio fail.\r\n");
     }
     ret = gpio_request(dev->ld_pin, "ak2401a_ld");
     if(ret)
     {
         printk("\r\nFailed to request ak2401a_ld GPIO%d.\r\n",dev->ld_pin);
     }
 
     // dev->ld_irq_num = gpio_to_irq(dev->ld_pin);
     // gpio_direction_input(dev->ld_pin);
     // ret = request_irq(dev->ld_irq_num,                        
     //                 ld_handle_irq,                                
     //                   IRQ_TYPE_EDGE_RISING|IRQ_TYPE_EDGE_FALLING|IRQF_SHARED,   
     //                    "ak2401_ld_iqr",              
     //                     dev); 
 
     dev->rst_pin = of_get_named_gpio(dev->np, "rst-gpio", 0);
     if(dev->rst_pin < 0)
     {
         printk("\r\nGet ak2401 rst-gpio fail.\r\n");
     }
     ret = gpio_request(dev->rst_pin, "ak2401a_rst");
     if(ret)
     {
         printk("\r\nFailed to request ak2401a_rst GPIO%d.\r\n",dev->rst_pin);
     }
     gpio_direction_output(dev->rst_pin, 0);
 
 
     dev->rxpdn_pin = of_get_named_gpio(dev->np, "rxpdn-gpio", 0);
     if(dev->rxpdn_pin < 0)
     {
         printk("\r\nGet ak2401 rxpdn-gpio fail.\r\n");
     }
     ret = gpio_request(dev->rxpdn_pin, "ak2401a_rxpdn");
     if(ret)
     {
         printk("\r\nFailed to request ak2401a_ld GPIO%d.\r\n",dev->rxpdn_pin);
     }
     gpio_direction_output(dev->rxpdn_pin, 0);
 
     dev->txpdn_pin = of_get_named_gpio(dev->np, "txpdn-gpio", 0);
     if(dev->txpdn_pin < 0)
     {
         printk("\r\nGet ak2401 txpdn-gpio fail.\r\n");
     }
     ret = gpio_request(dev->txpdn_pin, "ak2401a_txpdn");
     if(ret)
     {
         printk("\r\nFailed to request ak2401a_txpdn GPIO%d.\r\n",dev->txpdn_pin);
     } 
     gpio_direction_output(dev->txpdn_pin, 0);    
 
     dev->agc_pin = of_get_named_gpio(dev->np, "agc-gpio", 0);
     if(dev->agc_pin < 0)
     {
         printk("\r\nGet ak2401 agc-gpio fail.\r\n");
     }
     ret = gpio_request(dev->agc_pin, "ak2401a_agc");
     if(ret)
     {
         printk("\r\nFailed to request ak2401a_agc GPIO%d.\r\n",dev->agc_pin);
     }
     gpio_direction_output(dev->agc_pin, 0);  
 
     // printk("%s module install ok!(ld_pin:%d,%d rst_pin:%d rxpdn_pin:%d txpdn_pin:%d agc_pin:%d)"
     //                                                                                ,dev->name
     //                                                                                ,dev->ld_pin,dev->ld_irq_num
     //                                                                                ,dev->rst_pin
     //                                                                                ,dev->rxpdn_pin
     //                                                                                ,dev->txpdn_pin
     //                                                                                ,dev->agc_pin);
 
     /* device tree config spi clk/cs/dout,make it as gpio and input*/
 
     int gpio_clk;
     int gpio_cs;
     int gpio_dout;
     gpio_clk = of_get_named_gpio(dev->np, "clk-gpio", 0);
     if(gpio_clk > 0)
     {
         gpio_direction_input(gpio_clk);
         dev_err(dev->device, "make clk as input. Pass\n");
     }
 
     gpio_cs = of_get_named_gpio(dev->np, "cs-gpio", 0);
     if(gpio_cs > 0)
     {
         gpio_direction_input(gpio_cs);
         dev_err(dev->device, "make cs as input. Pass\n");
     }
 
     gpio_dout = of_get_named_gpio(dev->np, "dout-gpio", 0);
     if(gpio_dout > 0)
     {
         gpio_direction_input(gpio_dout);
         dev_err(dev->device, "make dout as input. Pass\n");
     }
 
     ret = ak2401a_data_card(dev);
     if(ret != 0)
     {
         dev_err(dev->device,"ak2401a_data_card:%d\n",ret);
         goto error_out;
     }
     gRegisterCount++;
 
     hrtimer_init(&dev->ld_timer, CLOCK_MONOTONIC, HRTIMER_MODE_HARD);
     dev->ld_timer.function = ld_timer_process_handler;
     dev->ld_period_ns = 20000000;
     hrtimer_start(&dev->ld_timer,ktime_set(0,dev->ld_period_ns),HRTIMER_MODE_HARD);
 
 
     return ret;
 
 fail_device:
     //device创建失败，意味着class创建成功，应该将class销毁
     printk("device create error,class destroyed@%s %d\r\n",__FUNCTION__,__LINE__);
     class_destroy(dev->class);
 fail_class:
     //类创建失败，意味着设备应该已经创建成功，此刻应将其释放掉
     printk("class create error,cdev del@%s %d\r\n",__FUNCTION__,__LINE__);
     cdev_del(&dev->cdev);
 fail_cdev:
     //cdev初始化异常，意味着设备号已经申请完成，应将其释放
     printk("cdev init error,chrdev register@%s %d\r\n",__FUNCTION__,__LINE__);
     unregister_chrdev_region(dev->dev_id,dev->dev_cnt);
 error_out:
     printk("%s module init error %d@%s %d\r\n",dev->name, ret, __FILE__, __LINE__);
     return ret;    
 }
 
 static void ak2401a_remove(struct spi_device *spi)
 {
     static int indicateRelease = 0;
  
     if(spi == gak2401a_handler[0].spi)
     {
         dev_err(&spi->dev, "release %s\n",gak2401a_handler[0].name);
 
         free_irq(gak2401a_handler[0].ld_irq_num, &gak2401a_handler[0]);
 
         /*release gpio*/
         gpio_free(gak2401a_handler[0].ld_pin);
         gpio_free(gak2401a_handler[0].agc_pin);
         gpio_free(gak2401a_handler[0].rst_pin);
         gpio_free(gak2401a_handler[0].rxpdn_pin);
         gpio_free(gak2401a_handler[0].txpdn_pin);
 
         /*delete device */
         device_destroy(ak2401a_dev_class, gak2401a_handler[0].dev_id);           //delete device
         cdev_del(&gak2401a_handler[0].cdev);                       // release device no   
         indicateRelease++;    
     }
 
     if(spi == gak2401a_handler[1].spi)
     {
         dev_err(&spi->dev, "release %s\n",gak2401a_handler[1].name);
 
         free_irq(gak2401a_handler[1].ld_irq_num, &gak2401a_handler[1]);
         /*release gpio*/
         gpio_free(gak2401a_handler[1].ld_pin);
         gpio_free(gak2401a_handler[1].agc_pin);
         gpio_free(gak2401a_handler[1].rst_pin);
         gpio_free(gak2401a_handler[1].rxpdn_pin);
         gpio_free(gak2401a_handler[1].txpdn_pin);        
         /*delete device */
         device_destroy(ak2401a_dev_class, gak2401a_handler[1].dev_id); 
         cdev_del(&gak2401a_handler[1].cdev);
         indicateRelease++;        
     }
 
     if(indicateRelease == 2)
     {
         indicateRelease = 0;
         dev_err(&spi->dev, "release final!\n");
         class_destroy(ak2401a_dev_class);                       //delete class        
         unregister_chrdev_region(ak2401a_dev_id,AK2401A_DEV_CNT); //deattached char device
     }
 }
 
 static const struct of_device_id ak2401a_of_match_table[] = {
     {.compatible = "victel,ak2401a"},
     {}
 };
 MODULE_DEVICE_TABLE(of,ak2401a_of_match_table);
 
 struct spi_driver ak2401a_spi_driver = {
     .probe = ak2401a_drv_probe,
     .remove = ak2401a_remove,
     .driver = {
         .name = "ak2401a",
         .owner = THIS_MODULE,
         .of_match_table = ak2401a_of_match_table,
     },
 };
 
 
 static int __init ak2401a_driver_init(void)
 {
     int error = -1;
 
     error = spi_register_driver(&ak2401a_spi_driver);
     pr_info("%s %d:%d\n",__FUNCTION__,__LINE__,error);    
 
     return error;
 }
 
 static void __exit ak2401a_driver_exit(void)
 {
     pr_info("%s %d\n",__FUNCTION__,__LINE__);
     spi_unregister_driver(&ak2401a_spi_driver);
 }
 
 module_init(ak2401a_driver_init)
 module_exit(ak2401a_driver_exit)
 
 
 
MODULE_LICENSE("GPL");
MODULE_AUTHOR("liming");
MODULE_DESCRIPTION("ak2401a driver");
MODULE_VERSION(DEVICE_VERSION);

 /*end of the file:ak2401a.c*/
 