/*********************************************************************
 * \file rfcontrol.c
 * @Author: liming
 * @Date:   2025-06-11 16:47:54
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 16:44:49
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 *V001   李明  2025-06-11    created file    
 *V002   李明  2025-06-22    增加AKM相关操作 
 *V003   李明  2025-06-26 1450 增加alsa接口，rfpwr默认开启     
 *V004   李明  2025-07-02 2154 修正akm路径使用错位问题，调整时隙中断处理方式   
 *V005   李明  2025-07-03 0935 1. 停止时隙中断时，重置tdma信息
 *                             2. akm设置频率时，更新设备频率
 *V005   李明  2025-07-09 1058 1. 增加全时隙相关接口
 *V006   李明  2025-07-16 2008 1. 完善entry_idle
 *V007   李明  2025-07-18 1636 1. 增加射频电路组合控制命令
 *********************************************************************/
#include <linux/types.h> 
#include "asm-generic/int-ll64.h"
#include <linux/init.h> 
#include <linux/err.h>
#include <linux/kernel.h>
#include "linux/dev_printk.h"
#include <linux/cdev.h>
#include <linux/platform_device.h>
#include <linux/device.h>
#include <linux/module.h>
#include <linux/gpio.h>
#include <linux/of.h>
#include <linux/of_address.h>
#include <linux/of_gpio.h>
#include <asm/io.h>
#include <linux/uaccess.h>
#include <linux/fs.h>
#include <linux/module.h>
#include <linux/slab.h>
#include <linux/device.h>
#include <linux/delay.h>
#include <linux/ctype.h>
#include <linux/random.h>
#include <linux/syscalls.h>

#include "rfvsnd.h"
#include "rfcontrol.h"
#include "timeslot.h"
#include "ak2401a.h"

#include <sound/core.h>
#include <sound/pcm.h>
#include <sound/pcm_params.h>
#include <uapi/sound/asound.h>
#include <linux/gpio/consumer.h>


#define DEVICE_CNT          (1)
#define DEVICE_NAME         "rfcontrol"

#define DEVICE_VERSION      "V1.007"

#define RF_PREPARE_STAGE_NONE          (0)
#define RF_PREPARE_STAGE_TX_BEGIN      (1)
#define RF_PREPARE_STAGE_TX_USING      (2)
#define RF_PREPARE_STAGE_TX_END        (3)
#define RF_PREPARE_STAGE_RX_BEGIN      (4)
#define RF_PREPARE_STAGE_RX_USING      (5)
#define RF_PREPARE_STAGE_RX_END        (6)

#define RF_TX_SLOT_NONE             (0xff)

#define AKM_1ST    (1)
#define AKM_2ND    (2)

typedef void (*subslot_process_fn)(int signo);

typedef struct _ts_rf_snd_dev
{
    char                        *name;
    char                        *pcm_path;
    char                        *ctrl_path;
    struct file                 *filp;
    struct snd_pcm_substream    *substream;
    int                access;
    int                format;
    int                channels;
    int                rate;
    snd_pcm_uframes_t  period_size;
    snd_pcm_uframes_t  buffer_size;
    unsigned int card; 
    unsigned int device;
    struct pcm *pcm_handler;
    struct pcm_config config;
    int flags;
}ts_rf_snd_dev;

typedef struct _ts_pll_dev
{
    char *name;
    char *path;
    struct file *filp;
    uint32_t freq_main;
    uint32_t freq_aux;/*for two pll in one device*/
    int lock_status_main;
    int lock_status_aux;
    int rssi; /* one chip rf chip*/
}ts_pll_dev;

typedef struct _ts_rfcontrol_dev
{
    dev_t dev_id;
    int major;
    int minor;
    struct class *class;
    struct device *device;
    struct cdev cdev;   
    struct device_node *dev_nd;

    ts_rfcontrol_parameters rfc;
    struct file *timeslot;
    uint32_t timeslot_status;
    uint32_t timeslot_counter;
    ts_tdma_time ts;
    uint32_t work_slot_no;   /*指定工作时隙编号*/
    uint32_t prepare_slot_no; /* 依据工作时隙，确定准备时隙，为工作时隙前一时隙编号*/
    te_rf_work_mode work_mode;
    uint32_t rf_prepare_stage;
    te_rfstate_type rfstate_next;
    te_rfstate_type rfstate;
    subslot_process_fn timeslot_process;
    subslot_process_fn subslot_process;
    ts_rf_snd_dev tx_playback;
    void *tx_buffer;
    uint8_t full_slot_tx_enable_data_send;/* 0 - disable  1 - enable*/

    ts_rf_snd_dev rx_capture;
    ts_rf_snd_dev dac_placyback;

    ts_transfer_type transfer_data;
    ts_pa_data_type pa_data;
    uint8_t akm_pn9_data_set;

    struct work_struct work_prepare_tx_ramp_up_data;
    struct work_struct work_prepare_ramp_down_data;

    int rfpwr_pin;
    int txon_pin;
    int rxon_pin;
    int rxon2_pin;
    int rxvco_pin;
    int rxvco2_pin;
    int psapc_pin;

    /*AKM DEVICE - pll & modulate device*/
    ts_pll_dev pll1st;
    ts_pll_dev pll2nd;

    /*frequency info*/
    uint32_t tx_frequency;
    uint32_t rx_frequency;
    uint32_t rx2_frequency; /*仅双收需要*/

    uint8_t pll1st_freq_lock_status;
    uint8_t pll2nd_frq_lock_status;


    struct file   *akm1st;
    struct file   *akm2nd;
    uint32_t akm_1st_freq;
    int akm_1st_freq_lock_status;
    uint32_t akm_2nd_freq;
    int akm_2nd_freq_lock_status;

    unsigned int PowerMV; /*a*/
    unsigned int XrefVoltageMV; /*d*/
    unsigned int Xref26MVoltageMV;/*c - reseverd for 26M - 4819/ N/A*/
    unsigned int RxTv1MV; /*a*/
    unsigned int RxTv2MV; /*b*/
}ts_rfcontrol_dev;

static ts_rfcontrol_dev g_rfcontrol_dev;

/*interal function declare*/
static void akm_reset(struct file *filp, int path,unsigned int flag);
static void akm_freq(struct file *filp, int path,unsigned int freq);

/*amk2401 内部函数*/
extern long ak2401_ioctl_kenerl(struct file *filp,u32 cmd, void *args);

/*timeslot 内部函数*/
extern long timeslot_ioctl_kernel(struct file *filp, u32 cmd, void *args);
static void rfcontrol_dac_set(int channel, uint32_t voltage);
int rfcontrol_akm2401a_modulate_dataMaker(ts_rfcontrol_dev *rfdev);
int rfcontrol_akm2401a_modulate_pn9_dataMaker(ts_rfcontrol_dev *rfdev);
static int rfcontrol_dac_data_set(ts_rfcontrol_dev *rfdev, uint8_t ramp_flag);
static void akm_reg_write(struct file *filp, int path, ts_ak2401_reg_type *reg);
int pcm_state(struct pcm *pcm);
static int rfcontrol_open(struct inode *inode, struct  file *filp)
{
    filp->private_data = &g_rfcontrol_dev;
    return 0;
}

static int rfcontrol_close(struct inode *inode, struct file *filp)
{
    filp->private_data = NULL;
    return 0;
}

static void rfcontrol_rfpwr_onoff(ts_rfcontrol_dev *rfdev, u32 flag)
{
     if(flag)
     {
         gpiod_set_raw_value(gpio_to_desc(rfdev->rfpwr_pin), 1);
     }
     else 
     {
         gpiod_set_raw_value(gpio_to_desc(rfdev->rfpwr_pin), 0);
     }    
    // dev_err(rfdev->device,"rfpwr %d\n",flag);
}

static void rfcontrol_txon_onoff(ts_rfcontrol_dev *rfdev, u32 flag)
{
    if(flag)
    {
        gpiod_set_raw_value(gpio_to_desc(rfdev->txon_pin), 1);
    }
    else 
    {
        gpiod_set_raw_value(gpio_to_desc(rfdev->txon_pin), 0);
    }    
    // dev_err(rfdev->device,"txon_pin %d\n",flag);
}
static void rfcontrol_rxon_onoff(ts_rfcontrol_dev *rfdev, u32 flag)
{
     if(flag)
     {
         gpiod_set_raw_value(gpio_to_desc(rfdev->rxon_pin), 1);
     }
     else 
     {
         gpiod_set_raw_value(gpio_to_desc(rfdev->rxon_pin), 0);
     }   
    // dev_err(rfdev->device,"rxon_pin %d\n",flag); 
}
static void rfcontrol_rxon2_onoff(ts_rfcontrol_dev *rfdev, u32 flag)
{
     if(flag)
     {
         gpiod_set_raw_value(gpio_to_desc(rfdev->rxon2_pin), 1);
     }
     else 
     {
         gpiod_set_raw_value(gpio_to_desc(rfdev->rxon2_pin), 0);
     }    
        // dev_err(rfdev->device,"rxon2_pin %d\n",flag); 
}
static void rfcontrol_rxvco_onoff(ts_rfcontrol_dev *rfdev, u32 flag)
{
     if(flag)
     {
         gpiod_set_raw_value(gpio_to_desc(rfdev->rxvco_pin), 1);
     }
     else 
     {
         gpiod_set_raw_value(gpio_to_desc(rfdev->rxvco_pin), 0);
     }   
    // dev_err(rfdev->device,"rxvco_pin %d\n",flag);  
}
static void rfcontrol_rxvco2_onoff(ts_rfcontrol_dev *rfdev, u32 flag)
{
     if(flag)
     {
         gpiod_set_raw_value(gpio_to_desc(rfdev->rxvco2_pin), 1);
     }
     else 
     {
         gpiod_set_raw_value(gpio_to_desc(rfdev->rxvco2_pin), 0);
     }    
    // dev_err(rfdev->device,"rxvco2_pin %d\n",flag); 
}
static void rfcontrol_psapc_onoff(ts_rfcontrol_dev *rfdev, u32 flag)
{
     if(flag)
     {
         gpiod_set_raw_value(gpio_to_desc(rfdev->psapc_pin), 1);
     }
     else 
     {
         gpiod_set_raw_value(gpio_to_desc(rfdev->psapc_pin), 0);
     }    
    // dev_err(rfdev->device,"psapc_pin %d\n",flag); 
}


static void entry_rx(void)
{
    ts_ak2401_mode_type akm_mode;

    gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.rxon_pin), 1);
    gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.rxvco_pin), 1);
    if(g_rfcontrol_dev.akm1st)
    {
        akm_mode.work_mode = AK2401_WORK_MODE_RX;
        akm_mode.freq_info.freq = g_rfcontrol_dev.akm_1st_freq;
        akm_mode.freq_info.path = 1;
        ak2401_ioctl_kenerl(g_rfcontrol_dev.akm1st,AK2401A_CMD_MODE_SET, &akm_mode);
        if(akm_mode.freq_info.freq != 0) /*监控是否为0，如果为0，则不需要外部配置频率，待优化数据口及配置都放到ak2401内部*/
        {
            akm_freq(g_rfcontrol_dev.akm1st,AKM_1ST,g_rfcontrol_dev.transfer_data.work_freq);
        }
        // dev_err(g_rfcontrol_dev.device,"entry rx ok rx frequency= %u !\n",g_rfcontrol_dev.akm_1st_freq);
    }
}

static void entry_tx(void)
{
    ts_ak2401_mode_type akm_mode;

    gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.txon_pin), 1);
    gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.rxvco_pin), 1);
    gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.psapc_pin), 1);
    if(g_rfcontrol_dev.akm1st)
    {
        akm_mode.work_mode = AK2401_WORK_MODE_TX;
        akm_mode.freq_info.freq = g_rfcontrol_dev.akm_1st_freq;
        akm_mode.freq_info.path = 1;
        ak2401_ioctl_kenerl(g_rfcontrol_dev.akm1st,AK2401A_CMD_MODE_SET, &akm_mode);
        if(akm_mode.freq_info.freq != 0) /*监控是否为0，如果为0，则不需要外部配置频率，待优化数据口及配置都放到ak2401内部*/
        {
            akm_freq(g_rfcontrol_dev.akm1st,AKM_1ST,g_rfcontrol_dev.transfer_data.work_freq);
        }
        // dev_err(g_rfcontrol_dev.device,"entry rx ok rx frequency= %u !\n",g_rfcontrol_dev.akm_1st_freq);
    }
}

static void rfcontrol_pll_pump_switch(int flag)
{
    // ts_ak2401_reg_type reg;

    // if(g_rfcontrol_dev.akm1st)
    // {
    //     reg.path = 1;
    //     reg.addr = 0x0a;
    //     reg.value = flag == 1 ?  0x1f: 0x0a;

    //     akm_reg_write(g_rfcontrol_dev.akm1st,AKM_1ST, &reg);
    // }
}

static void entry_tx_only_config_freq(void)
{
    ts_ak2401_mode_type akm_mode;

    // gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.txon_pin), 1);
    gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.rxvco_pin), 1);
    
    // gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.psapc_pin), 1);
    if(g_rfcontrol_dev.akm1st)
    {
        rfcontrol_pll_pump_switch(1);/*发射，泵电流快锁开启*/
        akm_mode.work_mode = AK2401_WORK_MODE_TX;
        akm_mode.freq_info.freq = g_rfcontrol_dev.akm_1st_freq;
        akm_mode.freq_info.path = 1;
        ak2401_ioctl_kenerl(g_rfcontrol_dev.akm1st,AK2401A_CMD_MODE_SET, &akm_mode);
        if(akm_mode.freq_info.freq != 0) /*监控是否为0，如果为0，则不需要外部配置频率，待优化数据口及配置都放到ak2401内部*/
        {
            akm_freq(g_rfcontrol_dev.akm1st,AKM_1ST,g_rfcontrol_dev.transfer_data.work_freq);
        }
        // dev_err(g_rfcontrol_dev.device,"entry rx ok rx frequency= %u !\n",g_rfcontrol_dev.akm_1st_freq);
    }
}



static void rfcontrol_rf_tx_close(void)
{
    int flag = 0;

    // gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.rxvco_pin), 0);
    gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.txon_pin), 0);
    gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.psapc_pin), 0);    

    if(g_rfcontrol_dev.akm1st)
    {
        flag = 0;
        // ak2401_ioctl_kenerl(g_rfcontrol_dev.akm1st, AK2401A_CMD_TXPDN_CTRL, &flag);
    } 
}

static void entry_idle(void)
{
    ts_ak2401_mode_type akm_mode;

    /*时隙回调清空*/
    g_rfcontrol_dev.timeslot_process = NULL;
    g_rfcontrol_dev.subslot_process = NULL;

    /*停发数据*/
    if(g_rfcontrol_dev.dac_placyback.pcm_handler)
    {
        pcm_stop(g_rfcontrol_dev.dac_placyback.pcm_handler);
    }

    if(g_rfcontrol_dev.tx_playback.pcm_handler)
    {
        pcm_stop(g_rfcontrol_dev.tx_playback.pcm_handler);
    }

    gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.rxon_pin), 0);
    gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.rxvco_pin), 0);
    gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.txon_pin), 0);
    gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.rxvco2_pin), 0);
    gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.psapc_pin), 0);
    if(g_rfcontrol_dev.akm1st)
    {
        akm_mode.work_mode = AK2401_WORK_MODE_IDLE;
        akm_mode.freq_info.freq = g_rfcontrol_dev.akm_1st_freq;
        akm_mode.freq_info.path = 1;
        ak2401_ioctl_kenerl(g_rfcontrol_dev.akm1st,AK2401A_CMD_MODE_SET, &akm_mode);
        // dev_err(g_rfcontrol_dev.device,"entry idle ok!\n");
    }    
}



static void rfcontrol_info_print(void)
{
    struct device *dev = g_rfcontrol_dev.device;
    ts_rfcontrol_parameters *prfc = &(g_rfcontrol_dev.rfc);

    dev_err(dev, "rfcontro parameters,Timeslot=%d us, freq=%u us\n",prfc->timeslot_period,prfc->freq_locked_time);
}

static void rfcontrol_tx_prepare(void)
{
    /* 准备发射时隙，设置频率锁定开始时间 */
    if(g_rfcontrol_dev.timeslot)
    {
        ts_timeslot_type slot;
        slot.slot_id = SLOT_SUB_1;
        slot.period_us = g_rfcontrol_dev.rfc.freq_locked_time;
        timeslot_ioctl_kernel(g_rfcontrol_dev.timeslot, TIMESLOT_CMD_ENABLE,  &slot); 


        slot.slot_id = SLOT_SUB_2;
        slot.period_us = 29800;
        timeslot_ioctl_kernel(g_rfcontrol_dev.timeslot, TIMESLOT_CMD_ENABLE,  &slot); 

        g_rfcontrol_dev.rf_prepare_stage = RF_PREPARE_STAGE_TX_BEGIN;
    } /* end if(g_rfcontrol_dev.timeslot)*/
}

static void rfcontrol_tx_judge_set(void)
{
        ts_timeslot_type slot;
        slot.slot_id = SLOT_SUB_7;
        slot.period_us = g_rfcontrol_dev.rfc.timeslot_judge_period;
        timeslot_ioctl_kernel(g_rfcontrol_dev.timeslot, TIMESLOT_CMD_ENABLE, &slot);     
}

static void rfcontrol_timeslot_process(void)
{
    g_rfcontrol_dev.timeslot_counter++;
    g_rfcontrol_dev.ts.tsNo++;
    if(g_rfcontrol_dev.ts.tsNo > g_rfcontrol_dev.rfc.num_of_timeslot_one_frame)
    {
        g_rfcontrol_dev.ts.tsNo = 1;
        g_rfcontrol_dev.ts.frameNo++;
        if(g_rfcontrol_dev.ts.frameNo > g_rfcontrol_dev.rfc.num_of_frame_one_superframe)
        {
            g_rfcontrol_dev.ts.frameNo = 1;
            g_rfcontrol_dev.ts.superframeNo++;
        }
    }

    rfcontrol_tx_judge_set();

    if(g_rfcontrol_dev.timeslot_process)
    {
        g_rfcontrol_dev.timeslot_process(SLOT_MAIN);
    }
}


static void rfcontrol_process_tx_judge(void)
{
    if(g_rfcontrol_dev.ts.tsNo  == g_rfcontrol_dev.prepare_slot_no)
    {
        if(RF_PREPARE_STAGE_NONE == g_rfcontrol_dev.rf_prepare_stage
        || RF_PREPARE_STAGE_TX_END == g_rfcontrol_dev.rf_prepare_stage)
        {
            rfcontrol_tx_prepare();
        }/*end if(RF_PREPARE_STAGE_NONE == g_rfcontrol_dev.tx_prepare_stage)*/
        else  
        {
            //donothing
        }/*end if(RF_PREPARE_STAGE_NONE == g_rfcontrol_dev.tx_prepare_stage) else */
        
    }/*end if(g_rfcontrol_dev.timeslot_no == g_rfcontrol_dev.tx_prepare_slot_no)*/
}

void rfcontrol_process(int signo)
{
    // dev_err(g_rfcontrol_dev.device, "%s %d:: signo=%d\n",__func__,__LINE__,signo);
    if(SIG_SLOT == signo)
    {
        rfcontrol_timeslot_process();
    }
    else  
    {
        if(g_rfcontrol_dev.subslot_process)
        {
            g_rfcontrol_dev.subslot_process(signo - SIG_SLOT);
        }
    }
}


static int rfcontrol_timeslot_enable(uint32_t flag)
{
    // int result = 0;
    ts_timeslot_type slot;

    if(NULL == g_rfcontrol_dev.timeslot)
    {
        return -1;
    }
    slot.slot_id = SLOT_MAIN;

    if(0 == flag)
    {
        slot.period_us = 0;
        g_rfcontrol_dev.timeslot_counter = 0;

        /*关闭时隙中断后，重置tdma信息*/
        g_rfcontrol_dev.ts.frameNo = 0;
        g_rfcontrol_dev.ts.hyperframeNo = 0;
        g_rfcontrol_dev.ts.superframeNo = 0;
        g_rfcontrol_dev.ts.tsNo = 0;
        g_rfcontrol_dev.ts.symbolNo = 0;
    }
    else  
    {
        slot.period_us = g_rfcontrol_dev.rfc.timeslot_period;        
    }
    timeslot_ioctl_kernel(g_rfcontrol_dev.timeslot, TIMESLOT_CMD_ENABLE,  &slot); 

    return 0;
}

/** 单时隙发射射频控制 开始-------------------------------------------------------------------------------- */
static void rfcontrol_tx_single_slot_prepare(void)
{
    /**依据射频参数，提前准备发射频率及功放
        SLOT_SUB_1 -> 锁频准备
        SLOT_SUB_2 -> 提前使能爬坡及滚降数据，考虑使能代码执行时间，需要提前100-200us左右
    */
    if(g_rfcontrol_dev.timeslot)
    {
        ts_timeslot_type slot;
        slot.slot_id = SLOT_SUB_1;
        slot.period_us = 20000;//g_rfcontrol_dev.rfc.freq_locked_time;
        timeslot_ioctl_kernel(g_rfcontrol_dev.timeslot, TIMESLOT_CMD_ENABLE,  &slot); 


        slot.slot_id = SLOT_SUB_2;
        slot.period_us = 29800;
        timeslot_ioctl_kernel(g_rfcontrol_dev.timeslot, TIMESLOT_CMD_ENABLE,  &slot); 

        g_rfcontrol_dev.rf_prepare_stage = RF_PREPARE_STAGE_TX_BEGIN;
        // pr_err("\r\nslot=%d tx prepare setting \n",g_rfcontrol_dev.ts.tsNo);
    } /* end if(g_rfcontrol_dev.timeslot)*/
}

static void rfcontrol_tx_single_slot_timeslot_process(int signo)
{
    if(g_rfcontrol_dev.ts.tsNo  == g_rfcontrol_dev.work_slot_no)
    {
        /*当前为工作时隙及正在发射数据，则确定功率滚降*/
        ts_timeslot_type slot;
        slot.slot_id = SLOT_SUB_1;
        slot.period_us = 28500;//g_rfcontrol_dev.rfc.power_amp_delay_time;
        timeslot_ioctl_kernel(g_rfcontrol_dev.timeslot, TIMESLOT_CMD_ENABLE,  &slot); 
        // dev_err(g_rfcontrol_dev.device, "\r\npa ramp down prepare!slot=%d\n",g_rfcontrol_dev.ts.tsNo);
        pr_err("\nslot=%d, txing!\n",g_rfcontrol_dev.ts.tsNo);
    }
    else if(g_rfcontrol_dev.ts.tsNo  == g_rfcontrol_dev.prepare_slot_no)
    {
        /*关闭射频*/
        rfcontrol_rf_tx_close();

        /*直接进入IDLE状态，等待发射判决，确定是否下一次发射*/
        g_rfcontrol_dev.subslot_process = NULL;
        g_rfcontrol_dev.timeslot_process = NULL;        
        g_rfcontrol_dev.rfstate = RF_STATE_IDLE;
        g_rfcontrol_dev.work_mode = RF_WORK_MODE_IDLE;
        pr_err("\nslot=%d, tx end!\n",g_rfcontrol_dev.ts.tsNo);

    }
}

static void rfcontrol_tx_single_slot_sub_timeslot_process(int signo)
{
    switch(signo)
    {
        case SLOT_SUB_1:
            if(g_rfcontrol_dev.ts.tsNo  == g_rfcontrol_dev.prepare_slot_no)
            {
                //发射配频
                entry_tx_only_config_freq();
                //填写数据
                schedule_work(&g_rfcontrol_dev.work_prepare_tx_ramp_up_data);
                pr_err("\nslot=%d pa ramp up prepare and tx prepare!\n",g_rfcontrol_dev.ts.tsNo);
            }   
            else  if(g_rfcontrol_dev.ts.tsNo  == g_rfcontrol_dev.work_slot_no)
            {
                //使能功放滚降数据发送
                schedule_work(&g_rfcontrol_dev.work_prepare_ramp_down_data);
            }
            break;
        case SLOT_SUB_2:
            if(g_rfcontrol_dev.ts.tsNo  == g_rfcontrol_dev.prepare_slot_no)
            {
                /*使能txon 及功放开关*/
                rfcontrol_pll_pump_switch(0);/*发射，泵电流快锁关闭*/
                gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.txon_pin), 1);
                gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.psapc_pin), 1);
                
                //发射数据使能
                if(g_rfcontrol_dev.tx_playback.pcm_handler)
                {
                    pcm_start(g_rfcontrol_dev.tx_playback.pcm_handler);
                }
                //爬坡数据使能
                if(g_rfcontrol_dev.dac_placyback.pcm_handler)
                {
                    pcm_start(g_rfcontrol_dev.dac_placyback.pcm_handler);
                }
                pr_err("\nslot=%d tx data and pa ramp up start !\n",g_rfcontrol_dev.ts.tsNo);
            }
            break;
        case SLOT_SUB_3:
            break;
        case SLOT_SUB_4:
            break;
        case SLOT_SUB_5:
            break;
        case SLOT_SUB_6:
            break;
        case SLOT_SUB_7:
            //rfcontrol_process_tx_judge();
            break;
        default :
            break;
    }
}

/** 单时隙发射射频控制 结束-------------------------------------------------------------------------------- */



/** 单时隙接收射频控制 开始-------------------------------------------------------------------------------- */
static void rfcontrol_rx_single_slot_prepare(void)
{
    /**依据射频参数，提前准备发射频率及功放
        SLOT_SUB_1 -> 锁频准备
        SLOT_SUB_2 -> 提前使能爬坡及滚降数据，考虑使能代码执行时间，需要提前100-200us左右
    */
    if(g_rfcontrol_dev.timeslot)
    {
        ts_timeslot_type slot;
        slot.slot_id = SLOT_SUB_1;
        slot.period_us = g_rfcontrol_dev.rfc.freq_locked_time;
        timeslot_ioctl_kernel(g_rfcontrol_dev.timeslot, TIMESLOT_CMD_ENABLE, &slot); 


        slot.slot_id = SLOT_SUB_2;
        slot.period_us = 29800;
        timeslot_ioctl_kernel(g_rfcontrol_dev.timeslot, TIMESLOT_CMD_ENABLE,  &slot); 

        g_rfcontrol_dev.rf_prepare_stage = RF_PREPARE_STAGE_TX_BEGIN;
    } /* end if(g_rfcontrol_dev.timeslot)*/
}

static void rfcontrol_rx_single_slot_timeslot_process(int signo)
{
    /*时隙中断更新当前时隙 射频状态*/
    g_rfcontrol_dev.rfstate = g_rfcontrol_dev.rfstate_next;

    if(g_rfcontrol_dev.ts.tsNo  == g_rfcontrol_dev.prepare_slot_no)
    {
        /*直接进入IDLE状态，等待发射判决，确定是否下一次发射*/
        g_rfcontrol_dev.subslot_process = NULL;
        g_rfcontrol_dev.timeslot_process = NULL;        
        g_rfcontrol_dev.rfstate = RF_STATE_IDLE;
    }
}

static void rfcontrol_rx_single_slot_sub_timeslot_process(int signo)
{
    switch(signo)
    {
        case SLOT_SUB_1:
            break;
        case SLOT_SUB_2:
            break;
        case SLOT_SUB_3:
            break;
        case SLOT_SUB_4:
            break;
        case SLOT_SUB_5:
            break;
        case SLOT_SUB_6:
            break;
        case SLOT_SUB_7:
            rfcontrol_process_tx_judge();
            break;
        default :
            break;
    }
}

/** 单时隙接收射频控制 结束-------------------------------------------------------------------------------- */

/* 全时隙发射射频控制 开始--------------------------------------------------------------------------------- */
static void rfcontrol_tx_full_slot_timeslot_process(int signo)
{
    if( 0 == g_rfcontrol_dev.full_slot_tx_enable_data_send)
    {       
        if(g_rfcontrol_dev.tx_playback.pcm_handler)
        {
            dev_err(g_rfcontrol_dev.device, "before Tx data path start ok state=%d!\n",pcm_state(g_rfcontrol_dev.tx_playback.pcm_handler));
            pcm_start(g_rfcontrol_dev.tx_playback.pcm_handler);
            dev_err(g_rfcontrol_dev.device, "Tx data path start ok state=%d!\n",pcm_state(g_rfcontrol_dev.tx_playback.pcm_handler));
            if(PCM_STATE_RUNNING == pcm_state(g_rfcontrol_dev.tx_playback.pcm_handler))
            {
                g_rfcontrol_dev.full_slot_tx_enable_data_send = 1;
            }
        }
        else  
        {
            dev_err(g_rfcontrol_dev.device, "Tx data path not prepare ok!\n");
        }
    }
}
/* 全时隙发射射频控制 结束--------------------------------------------------------------------------------- */

static void rfcontrol_idle_to_(te_rf_work_mode wanted_work_mode)
{
    switch(wanted_work_mode)
    {
        case RF_WORK_MODE_SINGLE_SLOT_TX:
            /*进入单时隙发射*/
            g_rfcontrol_dev.akm_1st_freq = g_rfcontrol_dev.transfer_data.work_freq;
            g_rfcontrol_dev.tx_frequency = g_rfcontrol_dev.akm_1st_freq; 
            g_rfcontrol_dev.subslot_process = rfcontrol_tx_single_slot_sub_timeslot_process;
            g_rfcontrol_dev.timeslot_process = rfcontrol_tx_single_slot_timeslot_process;
            rfcontrol_tx_single_slot_prepare();
            g_rfcontrol_dev.rfstate = RF_STATE_TX_PREPARE;
            break;
        case RF_WORK_MODE_FULL_SLOT_TX:
            g_rfcontrol_dev.full_slot_tx_enable_data_send = 0;
            g_rfcontrol_dev.akm_1st_freq = g_rfcontrol_dev.transfer_data.work_freq;
            g_rfcontrol_dev.tx_frequency = g_rfcontrol_dev.akm_1st_freq; 
            entry_tx();
            rfcontrol_dac_set(0, g_rfcontrol_dev.transfer_data.tx_power_mv);
            if(g_rfcontrol_dev.akm_pn9_data_set)
            {
                rfcontrol_akm2401a_modulate_pn9_dataMaker(&g_rfcontrol_dev);
            }
            else  
            {
                rfcontrol_akm2401a_modulate_dataMaker(&g_rfcontrol_dev);
            }
            g_rfcontrol_dev.timeslot_process = rfcontrol_tx_full_slot_timeslot_process;
            if(g_rfcontrol_dev.tx_playback.pcm_handler)
            {
                pcm_start(g_rfcontrol_dev.tx_playback.pcm_handler);
            }
            break;
        case RF_WORK_MODE_RC_TX:
            g_rfcontrol_dev.akm_1st_freq = g_rfcontrol_dev.transfer_data.work_freq;
            g_rfcontrol_dev.tx_frequency = g_rfcontrol_dev.akm_1st_freq; 
            break;
        case RF_WORK_MODE_SINGLE_SLOT_RX:
            g_rfcontrol_dev.subslot_process = NULL;
            g_rfcontrol_dev.timeslot_process = NULL;    
            g_rfcontrol_dev.akm_1st_freq = g_rfcontrol_dev.transfer_data.work_freq;
            g_rfcontrol_dev.rx_frequency = g_rfcontrol_dev.akm_1st_freq;         
            break;
        case RF_WORK_MODE_FULL_SLOT_RX:
            /**更新频率 */
            g_rfcontrol_dev.akm_1st_freq = g_rfcontrol_dev.transfer_data.work_freq;
            g_rfcontrol_dev.rx_frequency = g_rfcontrol_dev.akm_1st_freq; 
            entry_rx();
            break;
        case RF_WORK_MODE_SINGLE_SLOT_RX_RC:
            g_rfcontrol_dev.akm_1st_freq = g_rfcontrol_dev.transfer_data.work_freq;
            g_rfcontrol_dev.rx_frequency = g_rfcontrol_dev.akm_1st_freq; 
            break;
        case RF_WORK_MODE_IDLE:
            /*do  nothing*/
            break;
        default:
            break;
    }
}

/* full slot rx*/
static void rfcontrol_fsr_to_(te_rf_work_mode wanted_work_mode)
{
    switch(g_rfcontrol_dev.work_mode)
    {
        case RF_STATE_IDLE:
            /*关闭所有射频电路*/
            entry_idle();
            break;
        case RF_WORK_MODE_FULL_SLOT_RX:
            /*已经是全时隙接收状态，则确认是否需要变更频率*/
            if(g_rfcontrol_dev.transfer_data.work_freq != g_rfcontrol_dev.akm_1st_freq)
            {
                /*如果当前频率与设置频率不一致，则进行配置频率 */
                if(g_rfcontrol_dev.akm1st)
                {
                    akm_freq(g_rfcontrol_dev.akm1st,AKM_1ST,g_rfcontrol_dev.transfer_data.work_freq);
                }
                g_rfcontrol_dev.akm_1st_freq = g_rfcontrol_dev.transfer_data.work_freq;
                g_rfcontrol_dev.rx_frequency = g_rfcontrol_dev.akm_1st_freq; 
            }
            break;
        default:
            break;
    }
}

/*full slot tx*/
static void rfcontrol_fsx_to_(te_rf_work_mode wanted_work_mode)
{
    switch(g_rfcontrol_dev.work_mode)
    {
        case RF_STATE_IDLE:
            /*关闭所有射频电路*/
            entry_idle();
            break;
        case RF_WORK_MODE_FULL_SLOT_TX:
            /*已经是全时隙发射状态，填数据*/
            if(g_rfcontrol_dev.akm_pn9_data_set)
            {
                rfcontrol_akm2401a_modulate_pn9_dataMaker(&g_rfcontrol_dev);
            }
            else  
            {
                rfcontrol_akm2401a_modulate_dataMaker(&g_rfcontrol_dev);
            }
            break;
        default:
            break;
    }
}

static void rfcontrol_fsm(te_rf_work_mode wanted_work_mode)
{
    te_rfstate_type current_rfstate = g_rfcontrol_dev.rfstate;

    if(RF_WORK_MODE_IDLE == wanted_work_mode)
    {
        entry_idle();
        g_rfcontrol_dev.work_mode = RF_WORK_MODE_IDLE;
        return;
    }
    // dev_err(g_rfcontrol_dev.device, "work_mode = %d, wanted_work_mode=%d\n",g_rfcontrol_dev.work_mode,wanted_work_mode);
    switch(g_rfcontrol_dev.work_mode)
    {
        case RF_WORK_MODE_IDLE:
            rfcontrol_idle_to_(wanted_work_mode);
            break;
        case RF_WORK_MODE_FULL_SLOT_RX:
            rfcontrol_fsr_to_(wanted_work_mode);
            // rfcontrol_fst_to_wanted_rfstate(wanted_rfstate);
            break;
        case RF_WORK_MODE_FULL_SLOT_TX:
            rfcontrol_fsx_to_(wanted_work_mode);
            break;
        default:
            break;
    }
    g_rfcontrol_dev.work_mode = wanted_work_mode;
}



/** SND Device Initialize */
#define FILE_PCM_AK2401_PLAYBACK        "/dev/snd/pcmC1D0p"
#define FILE_PCM_AK2401_1_CAPTURE       "/dev/snd/pcmC1D0c"
#define FILE_PCM_AK2401_2_CAPTURE       "/dev/snd/pcmC2D0c"
#define FILE_PCM_DAC_PLACYBACK          "/dev/snd/pcmC2D0p"

#define FILE_PCM_TX_PLAYBACK     FILE_PCM_AK2401_PLAYBACK
#define FILE_PCM_RX_1_CAPTURE    FILE_PCM_AK2401_1_CAPTURE
#define FILE_PCM_RX_2_CAPTURE    FILE_PCM_AK2401_2_CAPTURE

#define PCM_TX_PLACYBACK_NAME    "ak2401 modulate path"
#define PCM_RX_1_CAPTURE_NAME    "ak2401 rx data 1st path"
#define PCM_RX_2_CAPTURE_NAME    "AK2401 rx data 2nd path"
#define PCM_DAC_PLACYBACK_NAME   "rf ramp path"

#define FILE_RF_RX_1_ON         "/sys/class/leds/rx_on/brightness"
#define FILE_RF_RX_2_ON         "/sys/class/leds/rx_on_2/brightness"
#define FILE_RF_TX_ON           "/sys/class/leds/tx_on/brightness"
#define FILE_RF_RX_VCO_1_ON     "/sys/class/leds/rx_vco_on/brightness"
#define FILE_RF_RX_VCO_2_ON     "/sys/class/leds/rx_vco_on_2/brightness"
#define FILE_RF_RFPWR_ON        "/sys/class/leds/rf_power/brightness"
#define FILE_RF_PSAPC_ON        "/sys/class/leds/ps_apc/brightness"
#define FILE_RF_AK2401_1        "/dev/ak2401a0"
#define FILE_RF_AK2401_2        "/dev/ak2401a1"


/******************DAC SND Configure***********************/
#define DAC_FRMAE_RATE               (192000)
#define DAC_PROCESS_TIME             (1.25) //ms
#define DAC_PROCESS_PERIO_TIME       (0.25) //ms
#define DAC_SND_PERIOD_NUM           (DAC_PROCESS_PERIO_TIME*DAC_FRMAE_RATE/1000)
#define DAC_PROCESS_PERIOD_NUM       (DAC_PROCESS_TIME/DAC_PROCESS_PERIO_TIME)
#define DAC_SND_BUFFER_NUM           (DAC_PROCESS_PERIOD_NUM*DAC_SND_PERIOD_NUM)
#define DAC_SND_FORMAT               (SNDRV_PCM_FORMAT_S32_LE)
#define DAC_SND_CHANNEL              (1)


/************************AKM2401 Master TX SND Configure********************/
/*ak2401
 * format使用 SND_PCM_FORMAT_U20
 * channels: 固定1通道
 * frame: chaanels*SND_PCM_FORMAT_U20
 * Interleaved: 一个通道无所谓，一般选择交错模式(SND_PCM_ACCESS_RW_INTERLEAVED)
 * Period: 指定一次处理帧数。设计：命令行设置ak2401时使用 一次处理一帧，调制数据发送使用2.5ms数据一次
 * Buffer Size:alsa底层DMA处理缓存，一般为周期整数倍，固定为5ms(周期两次)
 * 采样率使用:192K/96K,视情况
 * !!! 调制数据发送时，不能配置，（TBD:确实需要配置看是否可以内嵌到调制数据内)
 */
#define MODULATE_MULT_NUM_192K  (3)
#define MODULATE_MULT_NUM_38P4K (5)

#if MODULATE_DATA_RATE == MODULATE_RATE_38P4K
#define MODULATE_RATE    (MODULATE_DATA_RATE * 5)
#define MODULE_MULT_NUM MODULATE_MULT_NUM_38P4K
#else
#define MODULATE_RATE    (MODULATE_DATA_RATE * 3)
#define MODULE_MULT_NUM MODULATE_MULT_NUM_192K
#endif

#define MODULATE_DATA_PHSYCIAL_SIZE_30MS MODULATE_DATA_SIZE_30MS*MODULE_MULT_NUM
#define MODULATE_DATA_PHSYCIAL_SIZE_60MS MODULATE_DATA_SIZE_60MS*MODULE_MULT_NUM


/*PN9 Demo data sample rate is FIXED  38.4k sps,
 * mutiple 5 to 192K sps 
 * mutiple 15 to 576K sps
 */
#define PN9_MODULATE_RATE                           (192000)
#define PN9_MODULATE_DATA_PROCESS_PERIOD_SIZE       (PN9_MODULATE_RATE*MODULATE_PROCESS_TIME/1000)
#define PN9_MODULATE_DATA_BUFERR_SIZE               (PN9_MODULATE_DATA_PROCESS_PERIOD_SIZE * 6)

#define MODULATE_PROCESS_TIME                       (10)    //2.5   /*unit:ms*/
#define MODULATE_PROCESS_TIME_NS                    ((long long)(MODULATE_PROCESS_TIME*1000000))    //2.5   /*unit:ns*/
#define CONFIG_PERIOD_SIZE                          (1)
#define MODULATE_DATA_PROCESS_PERIOD_SIZE           (MODULATE_RATE*MODULATE_PROCESS_TIME/1000)
#define MODULATE_DATA_BUFERR_SIZE                   (MODULATE_DATA_PROCESS_PERIOD_SIZE * 6)
#define MODULATE_DATA_PERIOD_SIZE                   (MODULATE_DATA_RATE*MODULATE_PROCESS_TIME/1000)
#define MODULATE_DATA_CHANNEL                       (1)
#define MODULATE_DATA_FORMAT                        (SNDRV_PCM_FORMAT_S32_LE)

/************************AKM2401 Master/SLVAE RX SND Configure********************/
/* 19.2MHz 晶振 75K采样率
 * 方案1 1.2ms * 75 = 90  * 8 = 720  Bytes
 * 方案2 2ms   * 75 = 150 * 8 = 1200 Bytes
 * 
 *
 * 18.432MHz 72K采样率
 * 方案1 1.25ms * 72 = 90  = 720  Bytes
 * 方案2 2.5ms  * 72 = 180 = 1440 Bytes      ** SELETED **
 */
#define AKM_RX_DATA_SND_PERIOD_SIZE                 (180)
#define AKM_RX_DATA_SND_BUFFSER_SIZE                (720)  
#define AKM_RX_DATA_SND_CHANNEL                     (2)
#define AKM_RX_DATA_SND_RATE                        (72000)
#define AKM_RX_DATA_SND_FORMAT                      (PCM_FORMAT_S32_LE)

/*-------------------------------------------------------------------------*/

/*
 * Some ALSA internal helper functions
 */
static int snd_interval_refine_set(struct snd_interval *i, unsigned int val)
{
	struct snd_interval t;
	t.empty = 0;
	t.min = t.max = val;
	t.openmin = t.openmax = 0;
	t.integer = 1;
	return snd_interval_refine(i, &t);
}

static int _snd_pcm_hw_param_set(struct snd_pcm_hw_params *params,
				 snd_pcm_hw_param_t var, unsigned int val,
				 int dir)
{
	int changed;
	if (hw_is_mask(var)) {
		struct snd_mask *m = hw_param_mask(params, var);
		if (val == 0 && dir < 0) {
			changed = -EINVAL;
			snd_mask_none(m);
		} else {
			if (dir > 0)
				val++;
			else if (dir < 0)
				val--;
			changed = snd_mask_refine_set(
					hw_param_mask(params, var), val);
		}
	} else if (hw_is_interval(var)) {
		struct snd_interval *i = hw_param_interval(params, var);
		if (val == 0 && dir < 0) {
			changed = -EINVAL;
			snd_interval_none(i);
		} else if (dir == 0)
			changed = snd_interval_refine_set(i, val);
		else {
			struct snd_interval t;
			t.openmin = 1;
			t.openmax = 1;
			t.empty = 0;
			t.integer = 0;
			if (dir < 0) {
				t.min = val - 1;
				t.max = val;
			} else {
				t.min = val;
				t.max = val+1;
			}
			changed = snd_interval_refine(i, &t);
		}
	} else
		return -EINVAL;
	if (changed) {
		params->cmask |= 1 << var;
		params->rmask |= 1 << var;
	}
	return changed;
}
/*-------------------------------------------------------------------------*/
static int snd_pcm_sw_params_default(ts_rf_snd_dev *snd, struct snd_pcm_sw_params *params)
{
	params->proto = SNDRV_PCM_VERSION;
	params->tstamp_mode = 0;
	params->tstamp_type = 0;
	params->period_step = 1;
	params->sleep_min = 0;
	params->avail_min = snd->period_size;
	params->xfer_align = 1;
	params->start_threshold = snd->buffer_size;
	params->stop_threshold = snd->buffer_size;
	params->silence_threshold = 0;
	params->silence_size = 0;
	params->boundary = snd->buffer_size;
	/* this should not happen (bad child?) */
	if (params->boundary == 0)
		return -EINVAL;
	while (params->boundary * 2 <= LONG_MAX - snd->buffer_size)
		params->boundary *= 2;
	return 0;
}

static int rfcontrol_snd_hw_params(ts_rf_snd_dev *snd)
{
    struct snd_pcm_substream *substream = snd->substream;
    struct snd_pcm_hw_params *params;
    snd_pcm_sframes_t result;
    struct device *dev = g_rfcontrol_dev.device;
    int ret = 0;

	params = kzalloc(sizeof(*params), GFP_KERNEL);
	if (!params)
    {
		return -ENOMEM;
    }

	_snd_pcm_hw_params_any(params);
	_snd_pcm_hw_param_set(params, SNDRV_PCM_HW_PARAM_ACCESS,snd->access, 0);
	_snd_pcm_hw_param_set(params, SNDRV_PCM_HW_PARAM_FORMAT,snd->format, 0);
	_snd_pcm_hw_param_set(params, SNDRV_PCM_HW_PARAM_CHANNELS,snd->channels, 0);
	_snd_pcm_hw_param_set(params, SNDRV_PCM_HW_PARAM_RATE,	snd->rate, 0);
    _snd_pcm_hw_param_set(params, SNDRV_PCM_HW_PARAM_PERIOD_TIME,	snd->period_size, 0);
    _snd_pcm_hw_param_set(params, SNDRV_PCM_HW_PARAM_BUFFER_SIZE,	snd->buffer_size, 0);

	// ret = snd_pcm_kernel_ioctl(substream, SNDRV_PCM_IOCTL_DROP, NULL);
    // dev_err(dev,"snd_pcm_kernel_ioctl/SNDRV_PCM_IOCTL_DROP ret=%d\n",ret);
	ret = snd_pcm_kernel_ioctl(substream, SNDRV_PCM_IOCTL_HW_PARAMS, params);
    dev_err(dev,"snd_pcm_kernel_ioctl/SNDRV_PCM_IOCTL_HW_PARAMS ret=%d\n",ret);

    // dev_err(dev, "%s %d::%s pcm %s state:%d\n",__func__,__LINE__,snd->name,substream->name,substream->runtime->state);

	result = snd_pcm_kernel_ioctl(substream, SNDRV_PCM_IOCTL_PREPARE, NULL);
	if (result < 0) 
    {
		dev_err(g_rfcontrol_dev.device,"preparing sound card  %s failed: %d\n", snd->name, (int)result);
		kfree(params);
		return result;
	}

    return 0;
}

static int rfcontrol_snd_sw_params(ts_rf_snd_dev *snd)
{
    struct snd_pcm_substream *substream = snd->substream;
    struct snd_pcm_sw_params *params;
    snd_pcm_sframes_t result;

	params = kzalloc(sizeof(*params), GFP_KERNEL);
	if (!params)
    {
		return -ENOMEM;
    }

    snd_pcm_sw_params_default(snd, params);
    snd_pcm_kernel_ioctl(substream, SNDRV_PCM_IOCTL_SW_PARAMS, params);

    return 0;
}


static int rfcontrol_snd_device_init(ts_rf_snd_dev *snd)
{
    int ret = 0;
    struct snd_pcm_file *pcm_file;
    struct device *dev = g_rfcontrol_dev.device;

    snd->pcm_handler = pcm_open(snd->card, snd->device, snd->flags, &snd->config);
    if(snd->pcm_handler->fd == -1)
    {
        dev_err(dev, "can't open the card %s\n",snd->name);
        ret = -1;
        return ret;
    }
    snd->substream = snd->pcm_handler->data->substream;
    
    return ret;
}

static int rfcontrol_init(ts_rfcontrol_dev *rfdev )
{
    ts_rf_snd_dev *snd;
    int ret = 0;
    struct device *dev = rfdev->device;

    /*init timeslot*/
    if(rfdev->timeslot == NULL)
    {
        rfdev->timeslot = filp_open("/dev/timeslot", O_RDWR, 0);
        if(rfdev->timeslot == NULL)
        {
            dev_err(dev, "can't open timeslot device! Please check path:/dev/timeslot .\n");
            return -1;
        }
        timeslot_ioctl_kernel(rfdev->timeslot, TIMESLOT_CMD_SET_TS_PROCESS, rfcontrol_process);

    }
    rfcontrol_timeslot_enable(0);
    timeslot_ioctl_kernel(rfdev->timeslot, TIMESLOT_CMD_REG, &rfdev->rfc.accept_rfcontrol_signo_pid);

    /*init tx data path*/
    snd = &rfdev->tx_playback;
    if(NULL == snd->pcm_handler)
    {
        snd->name     = PCM_TX_PLACYBACK_NAME;
        snd->pcm_path = FILE_PCM_TX_PLAYBACK;
        snd->access   = SNDRV_PCM_ACCESS_RW_INTERLEAVED;
        snd->channels = MODULATE_DATA_CHANNEL;
        snd->format   = MODULATE_DATA_FORMAT;
        snd->rate     = MODULATE_RATE;
        snd->period_size = MODULATE_DATA_PROCESS_PERIOD_SIZE;
        snd->buffer_size = MODULATE_DATA_BUFERR_SIZE;

        snd->config.period_size = MODULATE_DATA_PROCESS_PERIOD_SIZE;
        snd->config.period_count = 6;
        snd->config.format = PCM_FORMAT_S32_LE;//MODULATE_DATA_FORMAT;
        snd->config.channels = 1;//MODULATE_DATA_CHANNEL;
        // snd->config.start_threshold = snd->config.period_size*snd->config.period_count;
        // snd->config.stop_threshold = snd->config.period_size*snd->config.period_count;
        snd->config.start_stop_threshold_using_boundary = 1;

        snd->config.rate = MODULATE_RATE;
        snd->flags = PCM_OUT;
        snd->card = 1;
        snd->device = 0;


        rfdev->tx_buffer   = kzalloc(MODULATE_DATA_PHSYCIAL_SIZE_60MS*4, GFP_KERNEL);//32bit - 4bytes
        if(rfdev->tx_buffer == NULL)
        {
            return -ENOMEM;
        }

        ret = rfcontrol_snd_device_init(snd);
        if(ret != 0 )
        {
            dev_err(dev, "%s card init failed\n",snd->name);
            return ret;
        }
    }


    /*init DAC PA RAMP data path*/
    snd = &rfdev->dac_placyback;
    if(NULL == snd->pcm_handler)
    {
        snd->name     = PCM_DAC_PLACYBACK_NAME;
        snd->pcm_path = FILE_PCM_DAC_PLACYBACK;
        snd->access   = SNDRV_PCM_ACCESS_RW_INTERLEAVED;
        snd->channels = DAC_SND_CHANNEL;
        snd->format   = DAC_SND_FORMAT;
        snd->rate     = DAC_FRMAE_RATE;
        snd->period_size = DAC_PROCESS_PERIOD_NUM;
        snd->buffer_size = DAC_SND_BUFFER_NUM;

        snd->config.period_size = DAC_SND_PERIOD_NUM;
        snd->config.period_count = 5;
        snd->config.format = PCM_FORMAT_S32_LE;//MODULATE_DATA_FORMAT;
        snd->config.channels = 1;//MODULATE_DATA_CHANNEL;
        // snd->config.start_threshold = snd->config.period_size*snd->config.period_count;
        // snd->config.stop_threshold = snd->config.period_size*snd->config.period_count;
        snd->config.start_stop_threshold_using_boundary = 1;
        snd->config.rate = DAC_FRMAE_RATE;
        snd->flags = PCM_OUT;
        snd->card = 2;
        snd->device = 0;


        ret = rfcontrol_snd_device_init(snd);
        if(ret != 0 )
        {
            dev_err(dev, "%s card init failed\n",snd->name);
            return ret;
        }
    }

    g_rfcontrol_dev.work_mode = RF_WORK_MODE_IDLE;
    return 0;
}

#define DAC_CH_APC                   (0)
#define DAC_OPT_CH_N                 (1)
static void rfcontrol_pa_ramp_data_set(ts_rfcontrol_dev *rfdev)
{
    int i = 0;
    unsigned int val;
    int ret = 0;

    for(i = 0; i < PA_RAMP_DATA_NUM; i++)
    {
        val = (DAC_CH_APC<<14)|(DAC_OPT_CH_N<<12)|0;
        val = val | (rfdev->pa_data.ramp_up_data[i] & 0xfff);
        rfdev->pa_data.ramp_up_data[i] = val << 16;

        val = (DAC_CH_APC<<14)|(DAC_OPT_CH_N<<12)|0;
        val = val | (rfdev->pa_data.ramp_down_data[i] & 0xfff);
        rfdev->pa_data.ramp_down_data[i] = val << 16;
    }
}

int rfcontrol_akm2401a_modulate_pn9_dataMaker(ts_rfcontrol_dev *rfdev)
{
    int i = 0;
    unsigned int *p_md;
    int *p_data;
    int j = 0;
    int k = 0;
    unsigned short valueh = 0;
    unsigned short valuem = 0;
    unsigned short valuel = 0;
    unsigned int value  = 0;
    int err = 0;
    int count;
    int pos;
	struct snd_pcm_substream *substream = rfdev->tx_playback.substream;
	struct snd_pcm_runtime *runtime = substream->runtime;
    struct device *dev = g_rfcontrol_dev.device;


    p_md = (unsigned int *)rfdev->tx_buffer;
    p_data = (int*)rfdev->transfer_data.symbols;
    if(substream == NULL || NULL == g_rfcontrol_dev.tx_playback.pcm_handler)
    {
        dev_err(dev, "ak2401 device is not ready for using.\n");
        return -1;
    }
    // pr_err("\r\nslot=%d nr_samples=%d\n",g_rfcontrol_dev.ts.tsNo,g_rfcontrol_dev.transfer_data.nr_samples);
    // static unsigned char trace_one_period = 0;

    /*    +------+----------------------+---+---+
     *    |     data(16)  |        ...          |
     *    +------+----------------------+-------+
     *   31               16            3       0
     */
    // for(i = 0,j = 0,k = 0 ; i < rfdev->transfer_data.nr_samples; i++)
    // {
    //     int v = p_data[i];

    //     valueh = ((v & 0x30000) >> 16)  & 0xff;
    //     valuem = ((v & 0x0FF00) >>  8)  & 0xff;
    //     valuel = ((v & 0x000FF) >>  0)  & 0xff;

    //     value = 0x0f << 8;
    //     value |= valueh;
    //     p_md[j + 0]  = value << 16;    

    //     value = 0x10 << 8;
    //     value |= valuem;
    //     p_md[j + 1]     = value << 16; 

    //     value = 0x11 << 8;
    //     value |= valuel;
    //     p_md[j + 2] = value << 16; 

    //     for (k = 0; k < MODULE_MULT_NUM - 3; k++)
    //     {
    //         value = 0x00 << 8;
    //         value |= valuel;
    //         p_md[j + 3 + k] = value << 16;    /*  freq offset1 [7:0]*/
    //     }
    //     j += MODULE_MULT_NUM;
    // }    
    // // pr_err("\r\nslot=%d nr_samples=%d data prepare ok.\n",g_rfcontrol_dev.ts.tsNo,g_rfcontrol_dev.transfer_data.nr_samples);
    // if (pcm_state(g_rfcontrol_dev.tx_playback.pcm_handler) == PCM_STATE_SETUP && pcm_prepare(g_rfcontrol_dev.tx_playback.pcm_handler) != 0) 
    // {
    //     dev_err(g_rfcontrol_dev.device, "2401 modulate data tx path prepare fail!\n");
    //     return -1;
    // }
    // dev_err(g_rfcontrol_dev.device, "2401 modulate data tx path state(%d) rfdev->transfer_data.nr_samples=%d!\n",pcm_state(g_rfcontrol_dev.tx_playback.pcm_handler),rfdev->transfer_data.nr_samples);
    err = pcm_writei(g_rfcontrol_dev.tx_playback.pcm_handler, rfdev->tx_buffer,rfdev->transfer_data.nr_samples*MODULE_MULT_NUM);/* todo: May using really sent size */
    if(err < 0)
    {
        dev_err(g_rfcontrol_dev.device, "Fail(err=%d):2401 modulate data tx path state(%d) rfdev->transfer_data.nr_samples=%d!\n",err,pcm_state(g_rfcontrol_dev.tx_playback.pcm_handler),rfdev->transfer_data.nr_samples);
    }

    return 0;
}

int rfcontrol_akm2401a_modulate_dataMaker(ts_rfcontrol_dev *rfdev)
{
    int i = 0;
    unsigned int *p_md;
    int *p_data;
    int j = 0;
    int k = 0;
    unsigned short valueh = 0;
    unsigned short valuem = 0;
    unsigned short valuel = 0;
    unsigned int value  = 0;
    int err = 0;
    int count;
    int pos;
	struct snd_pcm_substream *substream = rfdev->tx_playback.substream;
	struct snd_pcm_runtime *runtime = substream->runtime;
    struct device *dev = g_rfcontrol_dev.device;


    p_md = (unsigned int *)rfdev->tx_buffer;
    p_data = (int*)rfdev->transfer_data.symbols;
    if(substream == NULL || NULL == g_rfcontrol_dev.tx_playback.pcm_handler)
    {
        dev_err(dev, "ak2401 device is not ready for using.\n");
        return -1;
    }
    // pr_err("\r\nslot=%d nr_samples=%d\n",g_rfcontrol_dev.ts.tsNo,g_rfcontrol_dev.transfer_data.nr_samples);
    // static unsigned char trace_one_period = 0;

    /*    +------+----------------------+---+---+
     *    |     data(16)  |        ...          |
     *    +------+----------------------+-------+
     *   31               16            3       0
     */
    for(i = 0,j = 0,k = 0 ; i < rfdev->transfer_data.nr_samples; i++)
    {
        int v = p_data[i];

        valueh = ((v & 0x30000) >> 16)  & 0xff;
        valuem = ((v & 0x0FF00) >>  8)  & 0xff;
        valuel = ((v & 0x000FF) >>  0)  & 0xff;

        value = 0x0f << 8;
        value |= valueh;
        p_md[j + 0]  = value << 16;    

        value = 0x10 << 8;
        value |= valuem;
        p_md[j + 1]     = value << 16; 

        value = 0x11 << 8;
        value |= valuel;
        p_md[j + 2] = value << 16; 

        for (k = 0; k < MODULE_MULT_NUM - 3; k++)
        {
            value = 0x00 << 8;
            value |= valuel;
            p_md[j + 3 + k] = value << 16;    /*  freq offset1 [7:0]*/
        }
        j += MODULE_MULT_NUM;
    }    
    // pr_err("\r\nslot=%d nr_samples=%d data prepare ok.\n",g_rfcontrol_dev.ts.tsNo,g_rfcontrol_dev.transfer_data.nr_samples);
    if (pcm_state(g_rfcontrol_dev.tx_playback.pcm_handler) == PCM_STATE_SETUP && pcm_prepare(g_rfcontrol_dev.tx_playback.pcm_handler) != 0) 
    {
        dev_err(g_rfcontrol_dev.device, "2401 modulate data tx path prepare fail!\n");
        return -1;
    }
    // dev_err(g_rfcontrol_dev.device, "2401 modulate data tx path state(%d) rfdev->transfer_data.nr_samples=%d!\n",pcm_state(g_rfcontrol_dev.tx_playback.pcm_handler),rfdev->transfer_data.nr_samples);
    err = pcm_writei(g_rfcontrol_dev.tx_playback.pcm_handler, rfdev->tx_buffer,rfdev->transfer_data.nr_samples*MODULE_MULT_NUM);/* todo: May using really sent size */
    if(err < 0)
    {
        dev_err(g_rfcontrol_dev.device, "Fail(err=%d):2401 modulate data tx path state(%d) rfdev->transfer_data.nr_samples=%d!\n",err,pcm_state(g_rfcontrol_dev.tx_playback.pcm_handler),rfdev->transfer_data.nr_samples);
    }

    return 0;
}

static int rfcontrol_dac_data_set(ts_rfcontrol_dev *rfdev, uint8_t ramp_flag)
{
    struct device *dev = g_rfcontrol_dev.device;
    int ret;

    if(NULL == rfdev->dac_placyback.pcm_handler)
    {
        dev_err(dev, "dac device is not ready for using.\n");
        return -1;
    }

    pcm_drain(g_rfcontrol_dev.dac_placyback.pcm_handler);
    pcm_prepare(g_rfcontrol_dev.dac_placyback.pcm_handler);

    if( 1 == ramp_flag )
    {
        ret = pcm_writei(g_rfcontrol_dev.dac_placyback.pcm_handler, rfdev->pa_data.ramp_up_data, 240);
    }
    else  
    {
        ret = pcm_writei(g_rfcontrol_dev.dac_placyback.pcm_handler, rfdev->pa_data.ramp_down_data, 240);
    }

    if(ret < 0)
    {
        pr_err("fill pa ramp %s data err=%d\n",ramp_flag == 1? "Up":"Down", ret);
    }

    return ret;
}


static void rfcontrol_transfer_data_set(ts_rfcontrol_dev *rfdev)
{
    int prepare_slot = 0;
    int ret = 0;

    g_rfcontrol_dev.work_slot_no = g_rfcontrol_dev.transfer_data.work_slot_no;
    prepare_slot = g_rfcontrol_dev.work_slot_no - 1;
    if(prepare_slot <= 0)
    {
        prepare_slot = g_rfcontrol_dev.rfc.num_of_timeslot_one_frame;
    }
    g_rfcontrol_dev.prepare_slot_no = prepare_slot;

    rfcontrol_fsm(g_rfcontrol_dev.transfer_data.work_mode);
}

/* dac tx enable*/
static int rfcontrol_snd_start(ts_rf_snd_dev *snd)
{
	struct snd_pcm_substream *substream = snd->substream;
	struct snd_pcm_runtime *runtime = substream->runtime;
    int ret;

    if(snd->pcm_handler == NULL)
    {
        dev_err(g_rfcontrol_dev.device,"%s start error!\n",snd->name);
        return -1;
    }

    ret = pcm_start(snd->pcm_handler);
	if (ret < 0) 
    {
		dev_err(g_rfcontrol_dev.device, "%s:Preparing sound card failed: %d\n",__FUNCTION__,(int)ret);
		return ret;
	}

    return 0;
}
static void rfcontrol_pa_ramp_start(void)
{
    rfcontrol_snd_start(&g_rfcontrol_dev.dac_placyback);
}

static void rfcontrol_tx_data_start(void)
{
    rfcontrol_snd_start(&g_rfcontrol_dev.tx_playback);
}


static void akm_rx_pdn(struct file *filp, unsigned int flag)
{
    ak2401_ioctl_kenerl(filp, AK2401A_CMD_RXPDN_CTRL, &flag);
}

static void akm_tx_pdn(struct file *filp, unsigned int flag)
{
    ak2401_ioctl_kenerl(filp, AK2401A_CMD_TXPDN_CTRL, &flag);
}

static void akm_agc_keep(struct file *filp, unsigned int flag)
{
    ak2401_ioctl_kenerl(filp, AK2401A_CMD_AGC_KEEP_CTRL, &flag);
}

static void akm_reg_phy_data_maker(ts_ak2401_reg_type *reg, int num, uint32_t *phy_buffer)
{
    int i = 0;
    uint32_t value = 0;


    if(reg == NULL || phy_buffer == NULL || num == 0)
    {
        dev_err(g_rfcontrol_dev.device, "akm_reg_phy_data_make wrong parameters!");
        return;
    }

    for(i = 0; i < num; i++)
    {
        value = 0;
        value = (reg[i].addr & 0x7f) << 8;
        value |= reg[i].value;
        phy_buffer[i] = value << 16;
        // dev_err(g_rfcontrol_dev.device, "%02x - %02x\n",reg[i].addr,reg[i].value);
    }
}


static void akm_reg_write(struct file *filp, int path, ts_ak2401_reg_type *reg)
{
    uint32_t akm_reg_pyh_data = 0;
    uint32_t value = 0;
    snd_pcm_sframes_t result;
    struct snd_pcm_substream *substream = g_rfcontrol_dev.tx_playback.substream;
    struct device *dev = g_rfcontrol_dev.device;
    snd_pcm_sframes_t frames;

    reg->path = path == AKM_1ST ? 1 : 0;
    if(path == AKM_1ST)
    {
        //mcasp
        value = 0;
        value = (reg->addr & 0x7f) << 8;
        value |= reg->value;
        akm_reg_pyh_data = value << 16;
        if(substream == NULL || g_rfcontrol_dev.tx_playback.pcm_handler == NULL)
        {
            dev_err(dev, "akm device is not ready for using.\n");
            return;
        }
        // pr_err("reg addr=%02x value=%02x %08x\n",reg->addr,reg->value,akm_reg_pyh_data);
        pcm_prepare(g_rfcontrol_dev.tx_playback.pcm_handler);
        pcm_writei(g_rfcontrol_dev.tx_playback.pcm_handler, &akm_reg_pyh_data,1);
        pcm_start(g_rfcontrol_dev.tx_playback.pcm_handler);
        pcm_drain(g_rfcontrol_dev.tx_playback.pcm_handler);
    }
    else if(path == AKM_2ND)
    {
        //spi 
        ak2401_ioctl_kenerl(filp, AK2401A_CMD_WIRTE_REG,  reg);
    }
    else  
    {
        //do nothing

    }
}


static void akm_reg_write_mcasp(uint32_t *phy_data, int num)
{
    if(num == 0 || g_rfcontrol_dev.tx_playback.pcm_handler == NULL || phy_data == NULL)
    {
        dev_err(g_rfcontrol_dev.device, "akm device is not ready for using.\n");
        return;
    }
    pcm_prepare(g_rfcontrol_dev.tx_playback.pcm_handler);
    pcm_writei(g_rfcontrol_dev.tx_playback.pcm_handler, phy_data,num);
    pcm_start(g_rfcontrol_dev.tx_playback.pcm_handler);
    pcm_drain(g_rfcontrol_dev.tx_playback.pcm_handler);
}



static void akm_freq_offset_reset(struct file *filp, int path, unsigned int offset)
{
    ts_ak2401a_freq_regs freq_regs;
    // int i = 0;
    uint32_t *reg_phy_buf = NULL;

    freq_regs.path = (path == AKM_1ST) ? 1 : 0;
    if(AKM_2ND == path)
    {
        //todo:SLAVE PATH ONLY RX,DO NOT SURPPORT TX,SO NO OFFSET SET FEATERUE
    }
    else if(AKM_1ST == path)
    {
        freq_regs.freq = offset;
        ak2401_ioctl_kenerl(filp, AK2401A_CMD_OFFSET_REGS, &freq_regs);
        // for(i = 0; i < 8; i++)
        // {
        //     akm_reg_write(filp, AKM_1ST, &freq_regs.reg[i]);
        // }
        reg_phy_buf = kzalloc(8*sizeof(uint32_t),GFP_KERNEL);
        if(reg_phy_buf == NULL)
        {
            dev_err(g_rfcontrol_dev.device, "%s no memory!\n",__func__);
            return;
        }
        akm_reg_phy_data_maker(freq_regs.reg, 8, reg_phy_buf);
        akm_reg_write_mcasp(reg_phy_buf,8);
        kfree(reg_phy_buf);
    }
}

static void akm_reset(struct file *filp, int path,unsigned int flag)
{
    ts_ak2401a_regs regs;
    // int i = 0;
    uint32_t *reg_phy_buf = NULL;

    regs.rst_flag = flag;
    regs.path = path == AKM_1ST ? 1 : 0; /** 1 - config akm by mcasp 0 - config akm by spi */
    ak2401_ioctl_kenerl(filp, AK2401A_CMD_RST_CTRL,  &regs);
    if(AKM_1ST == path)
    {
        //mcasp,need snd device 
        if(regs.num == 0)
        {
            // no reg to send to akm2401
            return;
        }
        // for(i = 0; i < regs.num; i++)
        // {
        //     akm_reg_write(filp, AKM_1ST, &regs.reg[i]);
        // }
        reg_phy_buf = kzalloc(regs.num * sizeof(uint32_t),GFP_KERNEL);
        if(reg_phy_buf == NULL)
        {
            dev_err(g_rfcontrol_dev.device, "%s %d:no memory!\n",__func__,__LINE__);
            return;
        }
        akm_reg_phy_data_maker(regs.reg, regs.num, reg_phy_buf);
        akm_reg_write_mcasp(reg_phy_buf,regs.num);
        kfree(reg_phy_buf);
    }
}

static void akm_freq(struct file *filp, int path,unsigned int freq)
{
    ts_ak2401a_freq_regs freq_regs;
    // int i = 0;
    uint32_t *reg_phy_buf = NULL;

    freq_regs.freq = freq;
    freq_regs.path = (path == AKM_1ST) ? 1 : 0;
    if(AKM_2ND == path)
    {
        ak2401_ioctl_kenerl(filp, AK2401A_CMD_SET_FREQ, &freq);
        g_rfcontrol_dev.akm_2nd_freq = freq;
    }
    else if(AKM_1ST == path)
    {
        ak2401_ioctl_kenerl(filp, AK2401A_CMD_FREQ_REGS, &freq_regs);
        // for(i = 0; i < 8; i++)
        // {
        //     akm_reg_write(filp, AKM_1ST, &freq_regs.reg[i]);
        // }
        reg_phy_buf = kzalloc(8*sizeof(uint32_t),GFP_KERNEL);
        if(reg_phy_buf == NULL)
        {
            dev_err(g_rfcontrol_dev.device, "%s no memory!\n",__func__);
            return;
        }
        akm_reg_phy_data_maker(freq_regs.reg, 8, reg_phy_buf);
        akm_reg_write_mcasp(reg_phy_buf,8);
        kfree(reg_phy_buf);
        g_rfcontrol_dev.akm_1st_freq = freq;
    }
    // dev_err(g_rfcontrol_dev.device,"%s set frequency to %u\n",(path == AKM_1ST) ?"1st":"2nd",freq);
}

static void akm_ld(struct file *filp, int *flag)
{
    ts_ak2401a_ld_type ldt;
    ak2401_ioctl_kenerl(filp,AK2401A_CMD_LD_CTRL, &ldt);
    *flag = ldt.ld_flag;
    // dev_err(g_rfcontrol_dev.device,"%s at %u Hz\n", ldt.ld_flag == 1?"Locked":"Unlocked", ldt.freq);
}

static void akm_rssi_read(struct file *filp, int *rssi)
{
    ts_ak2401a_ld_type ldt;

    ak2401_ioctl_kenerl(filp,AK2401A_CMD_RSSI_CTRL, rssi);
    // dev_err(g_rfcontrol_dev.device,"rssi = %d\n", *rssi);
}

static void akm_param_process( struct device *dev,ts_akm_param_type *akm)
{
    struct file *fakm;
    int ld;
    ts_ak2401_reg_type reg;

    if(akm->path == AKM_1ST)
    {
        //if akm 1st device not open,then open it 
        if(!g_rfcontrol_dev.akm1st)
        {
            g_rfcontrol_dev.akm1st = filp_open(FILE_RF_AK2401_2, O_RDWR, 0);
            if(g_rfcontrol_dev.akm1st < 0)
            {
                dev_err(dev,"can't open %s!\n", FILE_RF_AK2401_1);
                return;
            }
        }
        fakm = g_rfcontrol_dev.akm1st;
    }
    else if(akm->path == AKM_2ND)
    {
        //if akm 1st device not open,then open it 
        if(!g_rfcontrol_dev.akm2nd)
        {
            g_rfcontrol_dev.akm2nd = filp_open(FILE_RF_AK2401_1, O_RDWR, 0);
            if(g_rfcontrol_dev.akm2nd < 0)
            {
                dev_err(dev,"can't open %s!\n", FILE_RF_AK2401_2);
                return;
            }
        }
        fakm = g_rfcontrol_dev.akm2nd;
    }
    else  
    {
        dev_err(dev, "akm path wrong %d\n",akm->path);
    }
    
    // dev_err(dev,"akm para_type=%d\n",akm->param_type);
    switch(akm->param_type)
    {
        case AKM_INIT:
            akm_reset(fakm, akm->path, akm->value);
            break;
        case AKM_FREQ:
            akm_freq(fakm, akm->path, akm->value);
            break;
        case AKM_TXPDN:
            akm_tx_pdn(fakm,akm->value);
            break;
        case AKM_RXPDN:
            akm_rx_pdn(fakm,akm->value);
            break;
        case AKM_AGC_KEEP:
            akm_agc_keep(fakm,akm->value);
            break;
        case AKM_LD:
            akm_ld(fakm,&ld);
            break;
        case AKM_REG:
            reg.addr = (akm->value >> 8) & 0xff;
            reg.value = akm->value & 0xff;
            akm_reg_write(fakm, akm->path, &reg);
            break;
        case AKM_REG_PRINT:
            break;
        case AKM_OFFSET:
            akm_freq_offset_reset(fakm, akm->path, akm->value);
            break;
        case AKM_RSSI:
            akm_rssi_read(fakm, &ld);
            break;   
        default:
            break;
    }
}

static void rfcontrol_dac_set(int channel, uint32_t voltage)
{
    snd_pcm_sframes_t result;
    struct snd_pcm_substream *substream = g_rfcontrol_dev.dac_placyback.substream;
    struct device *dev = g_rfcontrol_dev.device;
    snd_pcm_sframes_t frames;

    uint16_t dac_val;
    uint32_t dac_val_phy;

    if(channel > 3)
    {
        dev_err(dev, "dac channel wrong must <= 3 (%d)!\n",channel);
        return;
    }
    dac_val = ((uint16_t)(((voltage * 4095) / 5000 )) ) & 0xfff; 
    dac_val |= 1 << 12;
    dac_val |= (channel) << 14; 

    dac_val_phy = 0;
    dac_val_phy = dac_val << 16;

    if(substream == NULL || g_rfcontrol_dev.dac_placyback.pcm_handler == NULL)
    {
        dev_err(dev, "dac device is not ready for using.\n");
        return;
    }
    // dev_err(dev, "voltage=%d mv dac_val= %04x dac_val_phy=%08x.\n",voltage,dac_val,dac_val_phy);
    pcm_prepare(g_rfcontrol_dev.dac_placyback.pcm_handler);
    pcm_writei(g_rfcontrol_dev.dac_placyback.pcm_handler, &dac_val_phy,1);
    pcm_start(g_rfcontrol_dev.dac_placyback.pcm_handler);
    pcm_drain(g_rfcontrol_dev.dac_placyback.pcm_handler);
}

/*射频接口封装*/
static void rfcontrol_tx_full_slot_rf_open(ts_rfcontrol_dev *rfdev)
{
    /*psapc,tx_on,rxvco1, akm xpnd,akm freq set,power*/
    rfcontrol_txon_onoff(rfdev, 1);
    rfcontrol_rxvco_onoff(rfdev, 1);
    
}

static int rfcontrol_pll_tx_frq_config(ts_rfcontrol_dev *rfcontrol_dev, uint32_t freq)
{
    if(rfcontrol_dev->akm1st)
    {
        akm_freq(rfcontrol_dev->akm1st,AKM_1ST,freq);
    }
    else  
    {
        dev_err(rfcontrol_dev->device, "pll not prepare ok!\n");
    }
    
    return 0;
}

static int rfcontrol_pll_rx_frq_config(ts_rfcontrol_dev *rfcontrol_dev, uint32_t freq)
{
    if(rfcontrol_dev->akm1st)
    {
        akm_freq(rfcontrol_dev->akm1st,AKM_1ST,freq);
    }
    else  
    {
        dev_err(rfcontrol_dev->device, "pll not prepare ok!\n");
    }
    
    return 0;
}

static int rfcontrol_pll_rx2_frq_config(ts_rfcontrol_dev *rfcontrol_dev, uint32_t freq)
{
    if(rfcontrol_dev->akm2nd)
    {
        akm_freq(rfcontrol_dev->akm2nd,AKM_2ND,freq);
    }
    else  
    {
        dev_err(rfcontrol_dev->device, "2nd pll not prepare ok!\n");
    }
    
    return 0;
}

static int rfcontrol_pll_freq_lock_get(ts_rfcontrol_dev *rfcontrol_dev, uint32_t path)
{
    int ld;

    if(2 == path)
    {
        if(rfcontrol_dev->akm2nd)
        {
            akm_ld(rfcontrol_dev->akm2nd,&ld);
        }
    }
    else  
    {
        /*默认查询 第一路锁相环*/
        if(rfcontrol_dev->akm1st)
        {
            akm_ld(rfcontrol_dev->akm1st,&ld);
        }        
    }

    return ld;
}

static int rfcontrol_rf_tx_onoff(ts_rfcontrol_dev *rfdev, uint32_t flag)
{
    int onoff = (flag == 0) ? 0 : 1;

    /* 组合逻辑，控制开关设备发射电路*/
    /* psapc txon，rxvco， txpdn*/
    if(rfdev)
    {
        gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.rxvco_pin), onoff);
        gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.txon_pin), onoff);
        gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.psapc_pin), onoff);
        /*如果是ak2401，则关闭txpdn*/
        if(rfdev->akm1st)
        {
            akm_tx_pdn(rfdev->akm1st,onoff);
        }

        return 0;
    }

    return -EBADF;
}

static int rfcontrol_rf_rx_onoff(ts_rfcontrol_dev *rfdev, uint32_t flag)
{
    int onoff = (flag == 0) ? 0 : 1;

    /* 组合逻辑，控制开关设备发射电路*/
    /* psapc txon，rxvco， txpdn*/
    if(rfdev)
    {
        gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.rxvco_pin), onoff);
        gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.rxon_pin), onoff);
        /*如果是ak2401，则关闭txpdn*/
        if(rfdev->akm1st)
        {
            akm_rx_pdn(rfdev->akm1st,onoff);
        }

        return 0;
    }

    return -EBADF;
}

static int rfcontrol_rf_rx2_onoff(ts_rfcontrol_dev *rfdev, uint32_t flag)
{
    int onoff = (flag == 0) ? 0 : 1;

    /* 组合逻辑，控制开关设备发射电路*/
    /* psapc txon，rxvco， txpdn*/
    if(rfdev)
    {
        gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.rxvco2_pin), onoff);
        gpiod_set_raw_value(gpio_to_desc(g_rfcontrol_dev.rxon2_pin), onoff);
        /*如果是ak2401，则关闭txpdn*/
        if(rfdev->akm2nd)
        {
            akm_rx_pdn(rfdev->akm2nd,onoff);
        }

        return 0;
    }

    return -EBADF;
}


static long rfcontrol_ioctl(struct file *filp, u32 cmd, unsigned long args)
{
    long error = 0;
    ts_rfcontrol_dev *rfcontrol_dev = (ts_rfcontrol_dev *) filp->private_data;
    struct device *dev = rfcontrol_dev->device;
    u32 flag;
    ts_akm_param_type akm;

    // dev_err(dev,"receive cmd:%d\n",_IOC_NR(cmd));

    switch(cmd)
    {
        case RFCONTROL_CMD_PARAS_GET:
            rfcontrol_info_print();
            break;
        case RFCONTROL_CMD_PARAS_SET:
            if(copy_from_user(&rfcontrol_dev->rfc, (ts_rfcontrol_parameters*)args, sizeof(ts_rfcontrol_parameters)))
            {
                error = -EFAULT;
                dev_err(dev, "RFCONTROL_CMD_PARAS_SET error!");
                goto __out__;
            }
            error = rfcontrol_init(rfcontrol_dev);
            break;
        case RFCONTROL_CMD_SLOT_ADJUST:
            rfcontrol_info_print();
            break;
        case RFCONTROL_CMD_SLOT_GET:
            if(copy_to_user((uint32_t*)args, &rfcontrol_dev->ts, sizeof(ts_tdma_time)))
            {
                error = -EFAULT;
                dev_err(dev, "RFCONTROL_CMD_SLOT_GET error!");
                goto __out__;
            }
            break;
        case RFCONTROL_CMD_SLOT_ENABLE:
            if(copy_from_user(&rfcontrol_dev->timeslot_status, (uint32_t*)args, 4))
            {
                error = -EFAULT;
                dev_err(dev, "RFCONTROL_CMD_SLOT_ENABLE error!");
                goto __out__;
            }
            rfcontrol_timeslot_enable(rfcontrol_dev->timeslot_status);
            break;
        case RFCONTROL_CMD_PA_RAMP_DATA:
            if(copy_from_user(&rfcontrol_dev->pa_data, (ts_pa_data_type*)args, sizeof(ts_pa_data_type)))
            {
                error = -EFAULT;
                dev_err(dev, "RFCONTROL_CMD_PA_RAMP_DATA error!");
                goto __out__;
            }
            rfcontrol_dev->PowerMV = rfcontrol_dev->pa_data.power_voltage_mv;
            rfcontrol_pa_ramp_data_set(rfcontrol_dev);
            break;
        case RFCONTROL_CMD_TRANSFER_DATA_WRITE:
            if(copy_from_user(&rfcontrol_dev->transfer_data, (ts_transfer_type*)args, sizeof(ts_transfer_type)))
            {
                error = -EFAULT;
                dev_err(dev, "RFCONTROL_CMD_TRANSFER_DATA_WRITE error!");
                goto __out__;
            }  
            rfcontrol_transfer_data_set(rfcontrol_dev);          
            break;
        case RFCONTROL_CMD_TX_ON:
            if(copy_from_user(&flag, (uint32_t*)args, 4))
            {
                error = -EFAULT;
                dev_err(dev, "RFCONTROL_CMD_TX_ON error!");
                goto __out__;
            }
            rfcontrol_txon_onoff(rfcontrol_dev, flag);
            break;
        case RFCONTROL_CMD_RX1_ON:
            if(copy_from_user(&flag, (uint32_t*)args, 4))
            {
                error = -EFAULT;
                dev_err(dev, "RFCONTROL_CMD_RX1_ON error!");
                goto __out__;
            }
            rfcontrol_rxon_onoff(rfcontrol_dev, flag);
            break;
        case RFCONTROL_CMD_RX2_ON:
            if(copy_from_user(&flag, (uint32_t*)args, 4))
            {
                error = -EFAULT;
                dev_err(dev, "RFCONTROL_CMD_RX2_ON error!");
                goto __out__;
            }
            rfcontrol_rxon2_onoff(rfcontrol_dev, flag);
            break;
        case RFCONTROL_CMD_RX_VCO1_ON:
            if(copy_from_user(&flag, (uint32_t*)args, 4))
            {
                error = -EFAULT;
                dev_err(dev, "RFCONTROL_CMD_RX_VCO1_ON error!");
                goto __out__;
            }
            rfcontrol_rxvco_onoff(rfcontrol_dev, flag);
            break;
        case RFCONTROL_CMD_RX_VCO2_ON:
            if(copy_from_user(&flag, (uint32_t*)args, 4))
            {
                error = -EFAULT;
                dev_err(dev, "RFCONTROL_CMD_RX_VCO2_ON error!");
                goto __out__;
            }
            rfcontrol_rxvco2_onoff(rfcontrol_dev, flag);
            break;
        case RFCONTROL_CMD_RFPWREN_ON:
            if(copy_from_user(&flag, (uint32_t*)args, 4))
            {
                error = -EFAULT;
                dev_err(dev, "RFCONTROL_CMD_RFPWREN_ON error!");
                goto __out__;
            }
            rfcontrol_rfpwr_onoff(rfcontrol_dev, flag);
            break;
        case RFCONTROL_CMD_PSAPC_ON:
            if(copy_from_user(&flag, (uint32_t*)args, 4))
            {
                error = -EFAULT;
                dev_err(dev, "RFCONTROL_CMD_PSAPC_ON error!");
                goto __out__;
            }
            rfcontrol_psapc_onoff(rfcontrol_dev, flag);
            break;
        case RFCONTROL_CMD_AKM:            
            if(copy_from_user(&akm, (const void *)args, sizeof(ts_akm_param_type)))
            {
                error = -EFAULT;
                dev_err(dev, "can't config akm!");
            }
            akm_param_process(dev, &akm);
            break;
        case RFCONTROL_CMD_AKM_PN9:
            if(rfcontrol_dev->tx_buffer)
            {
                if(copy_from_user(rfcontrol_dev->tx_buffer, (uint32_t*)args, sizeof(ts_akm_pn9_type)))
                {
                    error = -EFAULT;
                    dev_err(dev, "RFCONTROL_CMD_AKM_PN9 error!");
                    goto __out__;
                }   
                rfcontrol_dev->akm_pn9_data_set = 1;             
            }
            break;
        case RFCONTROL_CMD_PA_VOLTAG_MV:
            if(copy_from_user(&rfcontrol_dev->PowerMV, (uint32_t*)args, 4))
            {
                error = -EFAULT;
                dev_err(dev, "RFCONTROL_CMD_PA_VOLTAG_MV error!");
                goto __out__;
            }
            rfcontrol_dac_set(0,rfcontrol_dev->PowerMV);
            break;
        case RFCONTROL_CMD_TV_VOLTAG_MV:
            if(copy_from_user(&rfcontrol_dev->RxTv2MV, (uint32_t*)args, 4))
            {
                error = -EFAULT;
                dev_err(dev, "RFCONTROL_CMD_TV_VOLTAG_MV error!");
                goto __out__;
            }
            rfcontrol_dac_set(1,rfcontrol_dev->RxTv2MV);
            break;
        case RFCONTROL_CMD_CRYSTAL_VOLTAG_MV:
            if(copy_from_user(&rfcontrol_dev->Xref26MVoltageMV, (uint32_t*)args, 4))
            {
                error = -EFAULT;
                dev_err(dev, "RFCONTROL_CMD_CRYSTAL_VOLTAG_MV error!");
                goto __out__;
            }
            rfcontrol_dac_set(2,rfcontrol_dev->Xref26MVoltageMV);
            break;
        case RFCONTROL_CMD_MOD_REF_VOLTAG_MV:
            if(copy_from_user(&rfcontrol_dev->XrefVoltageMV, (uint32_t*)args, 4))
            {
                error = -EFAULT;
                dev_err(dev, "RFCONTROL_CMD_MOD_REF_VOLTAG_MV error!");
                goto __out__;
            }
            rfcontrol_dac_set(3,rfcontrol_dev->XrefVoltageMV);
            break;
        case RFCONTROL_CMD_PLL_TX_FRQ_SET:
            if(copy_from_user(&rfcontrol_dev->tx_frequency , (uint32_t*)args, 4))
            {
                error = -EFAULT;
                dev_err(dev, "RFCONTROL_CMD_PLL_TX_FRQ_SET error!");
                goto __out__;
            }
            rfcontrol_pll_tx_frq_config(rfcontrol_dev, rfcontrol_dev->tx_frequency);
            break;
        case RFCONTROL_CMD_PLL_RX_FRQ_SET:
            if(copy_from_user(&rfcontrol_dev->rx_frequency , (uint32_t*)args, 4))
            {
                error = -EFAULT;
                dev_err(dev, "RFCONTROL_CMD_PLL_TX_FRQ_SET error!");
                goto __out__;
            }
            rfcontrol_pll_rx_frq_config(rfcontrol_dev, rfcontrol_dev->rx_frequency);
            break;   
        case RFCONTROL_CMD_PLL_RX2_FRQ_SET:
            if(copy_from_user(&rfcontrol_dev->rx2_frequency , (uint32_t*)args, 4))
            {
                error = -EFAULT;
                dev_err(dev, "RFCONTROL_CMD_PLL_TX_FRQ_SET error!");
                goto __out__;
            }
            rfcontrol_pll_rx2_frq_config(rfcontrol_dev, rfcontrol_dev->rx2_frequency);
            break; 
        case RFCONTROL_CMD_PLL_LD_GET:
            if(copy_from_user(&flag, (uint32_t*)args, 4))
            {
                error = -EFAULT;
                dev_err(dev, "RFCONTROL_CMD_RX1_ON error!");
                goto __out__;
            }
            flag = rfcontrol_pll_freq_lock_get(rfcontrol_dev, flag);
            if(copy_to_user((uint32_t*)args , &flag, 4))
            {
                error = -EFAULT;
                dev_err(dev, "RFCONTROL_CMD_PLL_TX_FRQ_SET error!");
                goto __out__;
            }            
            break;
        case RFCONTROL_CMD_RF_TX_ONOFF_SET:
            if(copy_from_user(&flag , (uint32_t*)args, 4))
            {
                error = -EFAULT;
                dev_err(dev, "RFCONTROL_CMD_PLL_TX_FRQ_SET error!");
                goto __out__;
            }
            error = rfcontrol_rf_tx_onoff(rfcontrol_dev, flag);
            break;   
        case RFCONTROL_CMD_RF_RX_ONOFF_SET:
            if(copy_from_user(&flag , (uint32_t*)args, 4))
            {
                error = -EFAULT;
                dev_err(dev, "RFCONTROL_CMD_PLL_TX_FRQ_SET error!");
                goto __out__;
            }
            error = rfcontrol_rf_rx_onoff(rfcontrol_dev, flag);
            break;   
        case RFCONTROL_CMD_RF_RX2_ONOFF_SET:
            if(copy_from_user(&flag , (uint32_t*)args, 4))
            {
                error = -EFAULT;
                dev_err(dev, "RFCONTROL_CMD_PLL_TX_FRQ_SET error!");
                goto __out__;
            }
            error = rfcontrol_rf_rx2_onoff(rfcontrol_dev, flag);
            break;       
        default:
            break;
    }
__out__:
    return error;
}


static const struct file_operations rfcontrol_fops = {
    .owner = THIS_MODULE,
    .open = rfcontrol_open,
    .release = rfcontrol_close,
    .unlocked_ioctl = rfcontrol_ioctl,
};


static void do_work_prepare_tx_ramp_up_data(struct work_struct *arg)
{
    pcm_drain(g_rfcontrol_dev.tx_playback.pcm_handler);
    rfcontrol_akm2401a_modulate_dataMaker(&g_rfcontrol_dev);
    rfcontrol_dac_data_set(&g_rfcontrol_dev, 1);
    pr_err("\r\nslot=%d do_work_prepare_tx_ramp_up_data!\n",g_rfcontrol_dev.ts.tsNo);
}

static void do_work_prepare_ramp_down_data(struct work_struct *arg)
{ 
    if(g_rfcontrol_dev.dac_placyback.pcm_handler)
    {
        rfcontrol_dac_data_set(&g_rfcontrol_dev, 0);
        pcm_start(g_rfcontrol_dev.dac_placyback.pcm_handler);
    }
    pr_err("\nslot=%d do_work_prepare_ramp_down_data,start ramp down!\n",g_rfcontrol_dev.ts.tsNo);
}

static int rfcontrol_probe(struct platform_device *pdev)
{
    struct device_node *np = pdev->dev.of_node;
    ts_rfcontrol_dev  *rfcontrol_dev = &g_rfcontrol_dev;
    int ret = 0;
    struct device *dev = &pdev->dev;

    rfcontrol_dev->major = 0;
    if(rfcontrol_dev->major)
    {
        rfcontrol_dev->dev_id = MKDEV(rfcontrol_dev->major, 0);
        ret = register_chrdev_region(rfcontrol_dev->dev_id, DEVICE_CNT, DEVICE_NAME);
    }
    else  
    {
        ret = alloc_chrdev_region(&rfcontrol_dev->dev_id, 0, DEVICE_CNT, DEVICE_NAME);
    }

    if(ret < 0)
    {
        dev_err(dev, "%s dev can't alloc chrdev major!\n",DEVICE_NAME);
        goto err_out;
    }

    INIT_WORK(&rfcontrol_dev->work_prepare_tx_ramp_up_data, do_work_prepare_tx_ramp_up_data);
    INIT_WORK(&rfcontrol_dev->work_prepare_ramp_down_data, do_work_prepare_ramp_down_data);

    rfcontrol_dev->cdev.owner = THIS_MODULE;
    cdev_init(&rfcontrol_dev->cdev, &rfcontrol_fops);

    ret = cdev_add(&rfcontrol_dev->cdev, rfcontrol_dev->dev_id, DEVICE_CNT);
    if(ret < 0)
    {
        dev_err(dev, "%s dev can't cdev_add!\n",DEVICE_NAME);        
        goto err_cdev;
    }

    rfcontrol_dev->class = class_create(THIS_MODULE, DEVICE_NAME);
    if(IS_ERR(rfcontrol_dev->class))
    {
        dev_err(dev, "%s dev can't class_create!\n",DEVICE_NAME);
        ret = PTR_ERR(rfcontrol_dev->class);
        goto err_class;
    }

    rfcontrol_dev->device = device_create(rfcontrol_dev->class, NULL, rfcontrol_dev->dev_id, NULL, DEVICE_NAME);
    if(IS_ERR(rfcontrol_dev->device))
    {
        dev_err(dev, "%s dev can't device_create!\n",DEVICE_NAME);
        ret = PTR_ERR(rfcontrol_dev->device);
        goto err_device;
    }   

    //attach timeslot
    rfcontrol_dev->timeslot = NULL;
    rfcontrol_dev->timeslot_status = 0;
    rfcontrol_dev->ts.tsNo  = 1;
    rfcontrol_dev->rf_prepare_stage = RF_PREPARE_STAGE_NONE;
    rfcontrol_dev->prepare_slot_no = RF_TX_SLOT_NONE;
    rfcontrol_dev->work_slot_no = RF_TX_SLOT_NONE;
    rfcontrol_dev->tx_playback.pcm_handler = NULL;
    rfcontrol_dev->dac_placyback.pcm_handler = NULL;

    /* init rf control pins*/
    rfcontrol_dev->rfpwr_pin = of_get_named_gpio(np, "rfpwr-gpio", 0);
    if(rfcontrol_dev->rfpwr_pin < 0)
    {
        dev_err(dev, "rfpwr_pin must configed!\n");
    }
    ret = gpio_request(rfcontrol_dev->rfpwr_pin, "rfpwr");
    if(ret)
    {
        dev_err(dev, "rfpwr_pin init can't request\n");
    }
    gpio_direction_output(rfcontrol_dev->rfpwr_pin, 0);
    rfcontrol_rfpwr_onoff(rfcontrol_dev, 1);//rfpwr 上电默认开启
    // dev_err(dev," rfcontrol_dev->rfpwr_pin = %d pass!",rfcontrol_dev->rfpwr_pin);

    rfcontrol_dev->txon_pin = of_get_named_gpio(np, "txon-gpio", 0);
    if(rfcontrol_dev->txon_pin < 0)
    {
        dev_err(dev, "txon must configed!\n");
    }
    ret = gpio_request(rfcontrol_dev->txon_pin, "txon");
    if(ret)
    {
        dev_err(dev, "txon_pin init can't request\n");
    }
    gpio_direction_output(rfcontrol_dev->txon_pin, 0);

    rfcontrol_dev->rxon_pin = of_get_named_gpio(np, "rxon-gpio", 0);
    if(rfcontrol_dev->rxon_pin < 0)
    {
        dev_err(dev, "rxon_pin must configed!\n");
    }
    ret = gpio_request(rfcontrol_dev->rxon_pin, "rxon");
    if(ret)
    {
        dev_err(dev, "rxon_pin init can't request\n");
    }
    gpio_direction_output(rfcontrol_dev->rxon_pin, 0);

    rfcontrol_dev->rxon2_pin = of_get_named_gpio(np, "rxon2-gpio", 0);
    if(rfcontrol_dev->rxon2_pin < 0)
    {
        dev_err(dev, "rxon2_pin must configed!\n");
    }
    ret = gpio_request(rfcontrol_dev->rxon2_pin, "rxon2");
    if(ret)
    {
        dev_err(dev, "rxon2_pin init can't request\n");
    }
    gpio_direction_output(rfcontrol_dev->rxon2_pin, 0);

    rfcontrol_dev->rxvco_pin = of_get_named_gpio(np, "rxvco-gpio", 0);
    if(rfcontrol_dev->rxvco_pin < 0)
    {
        dev_err(dev, "rxvco_pin must configed!\n");
    }
    ret = gpio_request(rfcontrol_dev->rxvco_pin, "rxvco");
    if(ret)
    {
        dev_err(dev, "rxvco_pin init can't request\n");
    }
    gpio_direction_output(rfcontrol_dev->rxvco_pin, 0);

    rfcontrol_dev->rxvco2_pin = of_get_named_gpio(np, "rxvco2-gpio", 0);
    if(rfcontrol_dev->rxvco2_pin < 0)
    {
        dev_err(dev, "rxvco2_pin must configed!\n");
    }
    ret = gpio_request(rfcontrol_dev->rxvco2_pin, "rxvco2");
    if(ret)
    {
        dev_err(dev, "rxvco2_pin init can't request\n");
    }
    gpio_direction_output(rfcontrol_dev->rxvco2_pin, 0);

    rfcontrol_dev->psapc_pin = of_get_named_gpio(np, "psapc-gpio", 0);
    if(rfcontrol_dev->psapc_pin < 0)
    {
        dev_err(dev, "psapc_pin must configed!\n");
    }
    ret = gpio_request(rfcontrol_dev->psapc_pin, "psapc");
    if(ret)
    {
        dev_err(dev, "psapc_pin init can't request\n");
    }
    gpio_direction_output(rfcontrol_dev->psapc_pin, 0);

    g_rfcontrol_dev.work_mode = RF_WORK_MODE_IDLE;
    dev_err(dev,"%s install ok!",DEVICE_NAME);
    return 0;
err_snd:
err_device:
    class_destroy(rfcontrol_dev->class);
err_class:
    cdev_del(&rfcontrol_dev->cdev);
err_cdev:
    unregister_chrdev_region(rfcontrol_dev->dev_id,DEVICE_CNT);
err_out:

    return ret;
}

 static int rfcontrol_remove(struct platform_device *pdev)
 {
    
    return 0;
 }

static const struct of_device_id rfcontrol_of_match[] = {
     {.compatible = "victel,rfcontrol"},
     {}
 };
MODULE_DEVICE_TABLE(of, rfcontrol_of_match);

static struct platform_driver rfcontrol_driver = {
    .driver = {
        .name = DEVICE_NAME,
        .of_match_table = of_match_ptr(rfcontrol_of_match),
    },
    .probe = rfcontrol_probe,
    .remove    = rfcontrol_remove,    
};

module_platform_driver(rfcontrol_driver);
MODULE_LICENSE("GPL");
MODULE_AUTHOR("LiMing");
MODULE_DESCRIPTION("rf control driver");
MODULE_VERSION(DEVICE_VERSION);


/**********************************************************PCM***************************************************** */
/* Logs information into a string; follows snprintf() in that
 * offset may be greater than size, and though no characters are copied
 * into string, characters are still counted into offset. */
#define STRLOG(string, offset, size, ...) \
    do { int temp, clipoffset = offset > size ? size : offset; \
         temp = snprintf(string + clipoffset, size - clipoffset, __VA_ARGS__); \
         if (temp > 0) offset += temp; } while (0)


#ifndef ARRAY_SIZE
#define ARRAY_SIZE(a) (sizeof(a) / sizeof((a)[0]))
#endif

/* refer to SNDRV_PCM_ACCESS_##index in sound/asound.h. */
static const char * const access_lookup[] = {
        "MMAP_INTERLEAVED",
        "MMAP_NONINTERLEAVED",
        "MMAP_COMPLEX",
        "RW_INTERLEAVED",
        "RW_NONINTERLEAVED",
};

/* refer to SNDRV_PCM_FORMAT_##index in sound/asound.h. */
static const char * const format_lookup[] = {
        /*[0] =*/ "S8",
        "U8",
        "S16_LE",
        "S16_BE",
        "U16_LE",
        "U16_BE",
        "S24_LE",
        "S24_BE",
        "U24_LE",
        "U24_BE",
        "S32_LE",
        "S32_BE",
        "U32_LE",
        "U32_BE",
        "FLOAT_LE",
        "FLOAT_BE",
        "FLOAT64_LE",
        "FLOAT64_BE",
        "IEC958_SUBFRAME_LE",
        "IEC958_SUBFRAME_BE",
        "MU_LAW",
        "A_LAW",
        "IMA_ADPCM",
        "MPEG",
        /*[24] =*/ "GSM",
        /* gap */
        [31] = "SPECIAL",
        "S24_3LE",
        "S24_3BE",
        "U24_3LE",
        "U24_3BE",
        "S20_3LE",
        "S20_3BE",
        "U20_3LE",
        "U20_3BE",
        "S18_3LE",
        "S18_3BE",
        "U18_3LE",
        /*[43] =*/ "U18_3BE",
#if 0
        /* recent additions, may not be present on local asound.h */
        "G723_24",
        "G723_24_1B",
        "G723_40",
        "G723_40_1B",
        "DSD_U8",
        "DSD_U16_LE",
#endif
};


/* refer to SNDRV_PCM_SUBFORMAT_##index in sound/asound.h. */
static const char * const subformat_lookup[] = {
        "STD",
};
/**hw operations begin */



static void pcm_hw_close(void *data)
{
    struct pcm_hw_data *hw_data = data;

    if (hw_data->filp)
        filp_close(hw_data->filp,NULL);

    kfree(hw_data);
}

static int pcm_hw_ioctl(void *data, unsigned int cmd, ...)
{
    struct pcm_hw_data *hw_data = data;
    va_list ap;
    void *arg;
    int ret = 0;

    va_start(ap, cmd);
    arg = va_arg(ap, void *);
    va_end(ap);
    // pr_err("%s %d::accept cmd=%x %x\n",__func__,__LINE__,_IOC_NR(cmd),cmd);
    ret = snd_pcm_kernel_ioctl(hw_data->substream, cmd, arg);

    if(ret < 0)
    {
        pr_err("pcm_hw_ioctl %s failed (%d)\n",hw_data->substream->pcm->name,ret);
    }

    return ret;
}

static int pcm_hw_open(unsigned int card, unsigned int device, unsigned int flags, void **data)
{
    struct pcm_hw_data *hw_data;
    char fn[256];
    struct file *filp;
    struct snd_pcm_file *pcm_file;

    hw_data = kzalloc(sizeof(*hw_data),GFP_KERNEL);
    if (!hw_data) {
        return -ENOMEM;
    }

    snprintf(fn, sizeof(fn), "/dev/snd/pcmC%uD%u%c", card, device, flags & PCM_IN ? 'c' : 'p');
    // pr_err("%s %d::open %s \n",__func__,__LINE__,fn);
    // Open the device with non-blocking flag to avoid to be blocked in kernel when all of the
    //   substreams of this PCM device are opened by others.
    filp = filp_open(fn, O_RDWR, 0);

    if (NULL == filp) {
        kfree(hw_data);
        return -1;
    }
    
    if(filp->private_data == NULL)
    {
        return -1;
    }
    pcm_file = filp->private_data;

    hw_data->card = card;
    hw_data->device = device;
    hw_data->filp = filp;
    hw_data->substream = pcm_file->substream;

    *data = hw_data;

    return 0;
}

const struct pcm_ops hw_ops = {
    .open = pcm_hw_open,
    .close = pcm_hw_close,
    .ioctl = pcm_hw_ioctl,
};

/**hw operation end */



static inline int param_is_mask(int p)
{
    return (p >= SNDRV_PCM_HW_PARAM_FIRST_MASK) &&
        (p <= SNDRV_PCM_HW_PARAM_LAST_MASK);
}

static inline int param_is_interval(int p)
{
    return (p >= SNDRV_PCM_HW_PARAM_FIRST_INTERVAL) &&
        (p <= SNDRV_PCM_HW_PARAM_LAST_INTERVAL);
}

static inline const struct snd_interval *rfsnd_param_get_interval(const struct snd_pcm_hw_params *p, int n)
{
    return &(p->intervals[n - SNDRV_PCM_HW_PARAM_FIRST_INTERVAL]);
}

static inline struct snd_interval *param_to_interval(struct snd_pcm_hw_params *p, int n)
{
    return &(p->intervals[n - SNDRV_PCM_HW_PARAM_FIRST_INTERVAL]);
}

static inline struct snd_mask *param_to_mask(struct snd_pcm_hw_params *p, int n)
{
    return &(p->masks[n - SNDRV_PCM_HW_PARAM_FIRST_MASK]);
}

static void param_set_mask(struct snd_pcm_hw_params *p, int n, unsigned int bit)
{
    // pr_err("%s %d:n=%d bit=%u\n",__func__,__LINE__,n, bit);
    if (bit >= SNDRV_MASK_MAX)
        return;
    if (param_is_mask(n)) {
        struct snd_mask *m = param_to_mask(p, n);
        m->bits[0] = 0;
        m->bits[1] = 0;
        m->bits[bit >> 5] |= (1 << (bit & 31));
        // pr_err("%s %d:n=%d bit=%u %u %u %u %u %u %u %u %u\n",__func__,__LINE__,n, bit
        //                         ,m->bits[0],m->bits[1],m->bits[2],m->bits[3],m->bits[4],m->bits[5],m->bits[6],m->bits[7]);
    }
    
}

static void param_set_min(struct snd_pcm_hw_params *p, int n, unsigned int val)
{
    if (param_is_interval(n)) {
        struct snd_interval *i = param_to_interval(p, n);
        i->min = val;
    }
}

static unsigned int param_get_min(const struct snd_pcm_hw_params *p, int n)
{
    if (param_is_interval(n)) {
        const struct snd_interval *i = rfsnd_param_get_interval(p, n);
        return i->min;
    }
    return 0;
}

static unsigned int param_get_max(const struct snd_pcm_hw_params *p, int n)
{
    if (param_is_interval(n)) {
        const struct snd_interval *i = rfsnd_param_get_interval(p, n);
        return i->max;
    }
    return 0;
}

static void rfsnd_param_set_int(struct snd_pcm_hw_params *p, int n, unsigned int val)
{
    if (param_is_interval(n)) {
        struct snd_interval *i = param_to_interval(p, n);
        i->min = val;
        i->max = val;
        i->integer = 1;
    }
}

static unsigned int rfsnd_param_get_int(struct snd_pcm_hw_params *p, int n)
{
    if (param_is_interval(n)) {
        struct snd_interval *i = param_to_interval(p, n);
        if (i->integer)
            return i->max;
    }
    return 0;
}

static void param_init(struct snd_pcm_hw_params *p)
{
    int n;

    memset(p, 0, sizeof(*p));
    for (n = SNDRV_PCM_HW_PARAM_FIRST_MASK;
         n <= SNDRV_PCM_HW_PARAM_LAST_MASK; n++) {
            struct snd_mask *m = param_to_mask(p, n);
            m->bits[0] = ~0;
            m->bits[1] = ~0;
    }
    for (n = SNDRV_PCM_HW_PARAM_FIRST_INTERVAL;
         n <= SNDRV_PCM_HW_PARAM_LAST_INTERVAL; n++) {
            struct snd_interval *i = param_to_interval(p, n);
            i->min = 0;
            i->max = ~0;
    }
    p->rmask = ~0U;
    p->cmask = 0;
    p->info = ~0U;
}

static unsigned int pcm_format_to_alsa(enum pcm_format format)
{
    switch (format) {

    case PCM_FORMAT_S8:
        return SNDRV_PCM_FORMAT_S8;

    default:
    case PCM_FORMAT_S16_LE:
        return SNDRV_PCM_FORMAT_S16_LE;
    case PCM_FORMAT_S16_BE:
        return SNDRV_PCM_FORMAT_S16_BE;

    case PCM_FORMAT_S24_LE:
        return SNDRV_PCM_FORMAT_S24_LE;
    case PCM_FORMAT_S24_BE:
        return SNDRV_PCM_FORMAT_S24_BE;

    case PCM_FORMAT_S24_3LE:
        return SNDRV_PCM_FORMAT_S24_3LE;
    case PCM_FORMAT_S24_3BE:
        return SNDRV_PCM_FORMAT_S24_3BE;

    case PCM_FORMAT_S32_LE:
        return SNDRV_PCM_FORMAT_S32_LE;
    case PCM_FORMAT_S32_BE:
        return SNDRV_PCM_FORMAT_S32_BE;

    case PCM_FORMAT_FLOAT_LE:
        return SNDRV_PCM_FORMAT_FLOAT_LE;
    case PCM_FORMAT_FLOAT_BE:
        return SNDRV_PCM_FORMAT_FLOAT_BE;
    };
}

// #define PCM_ERROR_MAX 128

// /** A PCM handle.
//  * @ingroup libtinyalsa-pcm
//  */
// struct pcm {
//     /** The PCM's file descriptor */
//     int fd;
//     /** Flags that were passed to @ref pcm_open */
//     unsigned int flags;
//     /** The number of (under/over)runs that have occurred */
//     int xruns;
//     /** Size of the buffer */
//     unsigned int buffer_size;
//     /** The boundary for ring buffer pointers */
//     unsigned long boundary;
//     /** Description of the last error that occurred */
//     char error[PCM_ERROR_MAX];
//     /** Configuration that was passed to @ref pcm_open */
//     struct pcm_config config;
//     struct snd_pcm_mmap_status *mmap_status;
//     struct snd_pcm_mmap_control *mmap_control;
//     struct snd_pcm_sync_ptr *sync_ptr;
//     void *mmap_buffer;
//     unsigned int noirq_frames_per_msec;
//     /** The delay of the PCM, in terms of frames */
//     long pcm_delay;
//     /** The subdevice corresponding to the PCM */
//     unsigned int subdevice;
//     /** Pointer to the pcm ops, either hw or plugin */
//     const struct pcm_ops *ops;
//     /** Private data for pcm_hw or pcm_plugin */
//     void *data;
//     /** Pointer to the pcm node from snd card definition */
//     struct snd_node *snd_node;
// };

static int oops(struct pcm *pcm, int e, const char *fmt, ...)
{
    va_list ap;
    int sz;

    // va_start(ap, fmt);
    // vsnprintf(pcm->error, PCM_ERROR_MAX, fmt, ap);
    // va_end(ap);
    // sz = strlen(pcm->error);

    // if (e)
    //     snprintf(pcm->error + sz, PCM_ERROR_MAX - sz,":errno=%d", e);

    return -1;
}

/** Gets the buffer size of the PCM.
 * @param pcm A PCM handle.
 * @return The buffer size of the PCM.
 * @ingroup libtinyalsa-pcm
 */
unsigned int pcm_get_buffer_size(const struct pcm *pcm)
{
    return pcm->buffer_size;
}

/** Gets the channel count of the PCM.
 * @param pcm A PCM handle.
 * @return The channel count of the PCM.
 * @ingroup libtinyalsa-pcm
 */
unsigned int pcm_get_channels(const struct pcm *pcm)
{
    return pcm->config.channels;
}

/** Gets the PCM configuration.
 * @param pcm A PCM handle.
 * @return The PCM configuration.
 *  This function only returns NULL if
 *  @p pcm is NULL.
 * @ingroup libtinyalsa-pcm
 * */
const struct pcm_config * pcm_get_config(const struct pcm *pcm)
{
    if (pcm == NULL)
        return NULL;
    return &pcm->config;
}

/** Gets the rate of the PCM.
 * The rate is given in frames per second.
 * @param pcm A PCM handle.
 * @return The rate of the PCM.
 * @ingroup libtinyalsa-pcm
 */
unsigned int pcm_get_rate(const struct pcm *pcm)
{
    return pcm->config.rate;
}

/** Gets the format of the PCM.
 * @param pcm A PCM handle.
 * @return The format of the PCM.
 * @ingroup libtinyalsa-pcm
 */
enum pcm_format pcm_get_format(const struct pcm *pcm)
{
    return pcm->config.format;
}

/** Gets the file descriptor of the PCM.
 * Useful for extending functionality of the PCM when needed.
 * @param pcm A PCM handle.
 * @return The file descriptor of the PCM.
 * @ingroup libtinyalsa-pcm
 */
int pcm_get_file_descriptor(const struct pcm *pcm)
{
    return pcm->fd;
}

/** Gets the error message for the last error that occurred.
 * If no error occurred and this function is called, the results are undefined.
 * @param pcm A PCM handle.
 * @return The error message of the last error that occurred.
 * @ingroup libtinyalsa-pcm
 */
const char* pcm_get_error(const struct pcm *pcm)
{
    return pcm->error;
}

/** Sets the PCM configuration.
 * @param pcm A PCM handle.
 * @param config The configuration to use for the
 *  PCM. This parameter may be NULL, in which case
 *  the default configuration is used.
 * @returns Zero on success, a negative errno value
 *  on failure.
 * @ingroup libtinyalsa-pcm
 * */
int pcm_set_config(struct pcm *pcm, const struct pcm_config *config)
{
    if (pcm == NULL)
        return -EFAULT;
    else if (config == NULL) {
        config = &pcm->config;
        pcm->config.channels = 2;
        pcm->config.rate = 48000;
        pcm->config.period_size = 1024;
        pcm->config.period_count = 4;
        pcm->config.format = PCM_FORMAT_S16_LE;
        pcm->config.start_threshold = config->period_count * config->period_size;
        pcm->config.stop_threshold = config->period_count * config->period_size;
        pcm->config.silence_threshold = 0;
        pcm->config.silence_size = 0;
        pr_err("using default config\n");
    } else
        pcm->config = *config;
    // pr_err("%s %d::\n",__func__,__LINE__);
    struct snd_pcm_hw_params params;
    param_init(&params);
    // pr_err("%s %d::config->format = %d pcm_format_to_alsa(config->format)=%d SNDRV_PCM_FORMAT_S32_LE=%d",__func__,__LINE__,config->format,pcm_format_to_alsa(config->format),SNDRV_PCM_FORMAT_S32_LE);
    param_set_mask(&params, SNDRV_PCM_HW_PARAM_FORMAT,pcm_format_to_alsa(config->format));
    rfsnd_param_set_int(&params, SNDRV_PCM_HW_PARAM_PERIOD_SIZE, config->period_size);
    rfsnd_param_set_int(&params, SNDRV_PCM_HW_PARAM_CHANNELS,config->channels);
    rfsnd_param_set_int(&params, SNDRV_PCM_HW_PARAM_PERIODS, config->period_count);
    rfsnd_param_set_int(&params, SNDRV_PCM_HW_PARAM_RATE, config->rate);

    if (pcm->flags & PCM_NOIRQ) {

        if (!(pcm->flags & PCM_MMAP)) {
            oops(pcm, EINVAL, "noirq only currently supported with mmap().");
            return -EINVAL;
        }

        params.flags |= SNDRV_PCM_HW_PARAMS_NO_PERIOD_WAKEUP;
        pcm->noirq_frames_per_msec = config->rate / 1000;
    }

    if (pcm->flags & PCM_MMAP)
        param_set_mask(&params, SNDRV_PCM_HW_PARAM_ACCESS,
                   SNDRV_PCM_ACCESS_MMAP_INTERLEAVED);
    else
        param_set_mask(&params, SNDRV_PCM_HW_PARAM_ACCESS,SNDRV_PCM_ACCESS_RW_INTERLEAVED);

    // char str[2048]  = { 0 };
    // pcm_params_to_string(&params, str, 2048);
    if (pcm->ops->ioctl(pcm->data, SNDRV_PCM_IOCTL_HW_PARAMS, &params)) {
        // int errno_copy = errno;
        // oops(pcm, errno, "cannot set hw params");
        pr_err("%s %d::cannot set hw params\n",__func__,__LINE__);
        return -1;
    }
    // pr_err("%s %d::SNDRV_PCM_IOCTL_HW_PARAMS pass\n",__func__,__LINE__);
    /* get our refined hw_params */
    pcm->config.period_size = rfsnd_param_get_int(&params, SNDRV_PCM_HW_PARAM_PERIOD_SIZE);
    pcm->config.period_count = rfsnd_param_get_int(&params, SNDRV_PCM_HW_PARAM_PERIODS);
    pcm->buffer_size = config->period_count * config->period_size;
    snd_pcm_uframes_t boundary = pcm->data->substream->runtime->boundary;

    // pr_err("%s period_size=%u period_count=%u buffer_size=%u boundary=%lu\n",pcm->data->substream->name,pcm->config.period_size,pcm->config.period_count,pcm->buffer_size,boundary);

    struct snd_pcm_sw_params sparams;
    memset(&sparams, 0, sizeof(sparams));
    sparams.tstamp_mode = SNDRV_PCM_TSTAMP_ENABLE;
    sparams.period_step = 1;
    if (!pcm->config.avail_min) {
        pcm->config.avail_min = pcm->config.period_size;
    }
    sparams.avail_min = pcm->config.avail_min;

    if (!config->start_threshold) {
        if (pcm->flags & PCM_IN)
            pcm->config.start_threshold = sparams.start_threshold = 1;
        else
            pcm->config.start_threshold = sparams.start_threshold =
                config->period_count * config->period_size / 2;
    } else
        sparams.start_threshold = config->start_threshold;

    /* pick a high stop threshold - todo: does this need further tuning */
    if (!config->stop_threshold) {
        if (pcm->flags & PCM_IN)
            pcm->config.stop_threshold = sparams.stop_threshold =
                config->period_count * config->period_size * 10;
        else
            pcm->config.stop_threshold = sparams.stop_threshold =
                config->period_count * config->period_size;
    }
    else
        sparams.stop_threshold = config->stop_threshold;

    if(1 == config->start_stop_threshold_using_boundary)
    {
        sparams.start_threshold = boundary;//config->stop_threshold;
        // pr_err("if(1 == config->start_stop_threshold_using_boundary)\n ");
    }

    sparams.xfer_align = config->period_size / 2; /* needed for old kernels */
    sparams.silence_size = config->silence_size;
    sparams.silence_threshold = config->silence_threshold;
    // pr_err("start_threshold=%lu sparams.avail_min=%u\r\n",sparams.start_threshold,sparams.avail_min);

    if (pcm->ops->ioctl(pcm->data, SNDRV_PCM_IOCTL_SW_PARAMS, &sparams)) {
        // int errno_copy = errno;
        // oops(pcm, errno, "cannot set sw params");
         pr_err("%s %d::cannot set sw params\n",__func__,__LINE__);
        return -1;
    }
    // pr_err("%s %d::SNDRV_PCM_IOCTL_SW_PARAMS pass\n",__func__,__LINE__);
    pcm->boundary = sparams.boundary;
    // pr_err("pcm->boundary=%lu\n",pcm->boundary);
    return 0;
}

/** Gets the subdevice on which the pcm has been opened.
 * @param pcm A PCM handle.
 * @return The subdevice on which the pcm has been opened */
unsigned int pcm_get_subdevice(const struct pcm *pcm)
{
    return pcm->subdevice;
}

/** Determines the number of bits occupied by a @ref pcm_format.
 * @param format A PCM format.
 * @return The number of bits associated with @p format
 * @ingroup libtinyalsa-pcm
 */
unsigned int pcm_format_to_bits_rfvsnd(enum pcm_format format)
{
    switch (format) {
    case PCM_FORMAT_S32_LE:
    case PCM_FORMAT_S32_BE:
    case PCM_FORMAT_S24_LE:
    case PCM_FORMAT_S24_BE:
    case PCM_FORMAT_FLOAT_LE:
    case PCM_FORMAT_FLOAT_BE:
        return 32;
    case PCM_FORMAT_S24_3LE:
    case PCM_FORMAT_S24_3BE:
        return 24;
    default:
    case PCM_FORMAT_S16_LE:
    case PCM_FORMAT_S16_BE:
        return 16;
    case PCM_FORMAT_S8:
        return 8;
    };
}

/** Determines how many frames of a PCM can fit into a number of bytes.
 * @param pcm A PCM handle.
 * @param bytes The number of bytes.
 * @return The number of frames that may fit into @p bytes
 * @ingroup libtinyalsa-pcm
 */
unsigned int pcm_bytes_to_frames(const struct pcm *pcm, unsigned int bytes)
{
    return bytes / (pcm->config.channels *
        (pcm_format_to_bits_rfvsnd(pcm->config.format) >> 3));
}

/** Determines how many bytes are occupied by a number of frames of a PCM.
 * @param pcm A PCM handle.
 * @param frames The number of frames of a PCM.
 * @return The bytes occupied by @p frames.
 * @ingroup libtinyalsa-pcm
 */
unsigned int pcm_frames_to_bytes(const struct pcm *pcm, unsigned int frames)
{
    return frames * pcm->config.channels *
        (pcm_format_to_bits_rfvsnd(pcm->config.format) >> 3);
}

static int pcm_sync_ptr(struct pcm *pcm, int flags)
{
    int err = 0;

    if (pcm->sync_ptr == NULL) {
        /* status and control are mmapped */
        if (flags & SNDRV_PCM_SYNC_PTR_HWSYNC) {
            if (pcm->ops->ioctl(pcm->data, SNDRV_PCM_IOCTL_HWSYNC) == -1) {
                return -1;
            }
        }
    } else {
        pcm->sync_ptr->flags = flags;
        err = pcm->ops->ioctl(pcm->data, SNDRV_PCM_IOCTL_SYNC_PTR, pcm->sync_ptr);
        if (err < 0) 
        {
            pr_err("pcm_sync_ptr failed err=%d\n",err);
            return err;
        }
    }

    return 0;
}

int pcm_state(struct pcm *pcm)
{
    // Update the state only. Do not sync HW sync.
    int err = pcm_sync_ptr(pcm, SNDRV_PCM_SYNC_PTR_APPL | SNDRV_PCM_SYNC_PTR_AVAIL_MIN);
    if (err < 0)
        return err;

    return pcm->mmap_status->state;
}

static int pcm_hw_mmap_status(struct pcm *pcm)
{
    if (pcm->sync_ptr)
        return 0;

//     int page_size = sysconf(_SC_PAGE_SIZE);
//     pcm->mmap_status = pcm->ops->mmap(pcm->data, NULL, page_size, PROT_READ, MAP_SHARED,
//                             SNDRV_PCM_MMAP_OFFSET_STATUS);
//     if (pcm->mmap_status == MAP_FAILED)
//         pcm->mmap_status = NULL;
//     if (!pcm->mmap_status)
//         goto mmap_error;

//     pcm->mmap_control = pcm->ops->mmap(pcm->data, NULL, page_size, PROT_READ | PROT_WRITE,
//                              MAP_SHARED, SNDRV_PCM_MMAP_OFFSET_CONTROL);
//     if (pcm->mmap_control == MAP_FAILED)
//         pcm->mmap_control = NULL;
//     if (!pcm->mmap_control) {
//         pcm->ops->munmap(pcm->data, pcm->mmap_status, page_size);
//         pcm->mmap_status = NULL;
//         goto mmap_error;
//     }

//     pcm->mmap_control->avail_min = pcm->config.avail_min;

//     return 0;

// mmap_error:

    pcm->sync_ptr = kzalloc(sizeof(*pcm->sync_ptr),GFP_KERNEL);
    if (!pcm->sync_ptr)
        return -ENOMEM;
    pcm->mmap_status = &pcm->sync_ptr->s.status;
    pcm->mmap_control = &pcm->sync_ptr->c.control;
    pcm->mmap_control->avail_min = pcm->config.avail_min;

    return 0;
}

static void pcm_hw_munmap_status(struct pcm *pcm) {
    if (pcm->sync_ptr) {
        kfree(pcm->sync_ptr);
        pcm->sync_ptr = NULL;
    } 
    // else {
    //     int page_size = sysconf(_SC_PAGE_SIZE);
    //     if (pcm->mmap_status)
    //         pcm->ops->munmap(pcm->data, pcm->mmap_status, page_size);
    //     if (pcm->mmap_control)
    //         pcm->ops->munmap(pcm->data, pcm->mmap_control, page_size);
    // }
    pcm->mmap_status = NULL;
    pcm->mmap_control = NULL;
}

static struct pcm bad_pcm = {
    .fd = -1,
};

/** Gets the hardware parameters of a PCM, without created a PCM handle.
 * @param card The card of the PCM.
 *  The default card is zero.
 * @param device The device of the PCM.
 *  The default device is zero.
 * @param flags Specifies whether the PCM is an input or output.
 *  May be one of the following:
 *   - @ref PCM_IN
 *   - @ref PCM_OUT
 * @return On success, the hardware parameters of the PCM; on failure, NULL.
 * @ingroup libtinyalsa-pcm
 */
struct pcm_params *pcm_params_get(unsigned int card, unsigned int device,
                                  unsigned int flags)
{
    struct snd_pcm_hw_params *params;
    void *snd_node = NULL, *data;
    const struct pcm_ops *ops;
    int fd;

    ops = &hw_ops;
    fd = ops->open(card, device, flags, &data);

#ifdef TINYALSA_USES_PLUGINS
    if (fd < 0) {
        int pcm_type;
        snd_node = snd_utils_open_pcm(card, device);
        pcm_type = snd_utils_get_node_type(snd_node);
        if (!snd_node || pcm_type != SND_NODE_TYPE_PLUGIN) {
            fprintf(stderr, "no device (hw/plugin) for card(%u), device(%u)",
                 card, device);
            goto err_open;
        }
        ops = &plug_ops;
        fd = ops->open(card, device, flags, &data, snd_node);
    }
#endif
    if (fd < 0) {
        // fprintf(stderr, "cannot open card(%d) device (%d): %s\n",
        //         card, device, strerror(errno));
        goto err_open;
    }

    params = kzalloc(sizeof(struct snd_pcm_hw_params), GFP_KERNEL);
    if (!params)
        goto err_calloc;

    param_init(params);
    if (ops->ioctl(data, SNDRV_PCM_IOCTL_HW_REFINE, params)) {
        // fprintf(stderr, "SNDRV_PCM_IOCTL_HW_REFINE error (%d)\n", errno);
        goto err_hw_refine;
    }

#ifdef TINYALSA_USES_PLUGINS
    if (snd_node)
        snd_utils_close_dev_node(snd_node);
#endif
    ops->close(data);

    return (struct pcm_params *)params;

err_hw_refine:
    kfree(params);
err_calloc:
#ifdef TINYALSA_USES_PLUGINS
    if (snd_node)
        snd_utils_close_dev_node(snd_node);
#endif
    ops->close(data);
err_open:
    return NULL;
}

/** Frees the hardware parameters returned by @ref pcm_params_get.
 * @param pcm_params Hardware parameters of a PCM.
 *  May be NULL.
 * @ingroup libtinyalsa-pcm
 */
void pcm_params_free(struct pcm_params *pcm_params)
{
    struct snd_pcm_hw_params *params = (struct snd_pcm_hw_params *)pcm_params;

    if (params)
        kfree(params);
}

static int pcm_param_to_alsa(enum pcm_param param)
{
    switch (param) {
    case PCM_PARAM_ACCESS:
        return SNDRV_PCM_HW_PARAM_ACCESS;
    case PCM_PARAM_FORMAT:
        return SNDRV_PCM_HW_PARAM_FORMAT;
    case PCM_PARAM_SUBFORMAT:
        return SNDRV_PCM_HW_PARAM_SUBFORMAT;
    case PCM_PARAM_SAMPLE_BITS:
        return SNDRV_PCM_HW_PARAM_SAMPLE_BITS;
        break;
    case PCM_PARAM_FRAME_BITS:
        return SNDRV_PCM_HW_PARAM_FRAME_BITS;
        break;
    case PCM_PARAM_CHANNELS:
        return SNDRV_PCM_HW_PARAM_CHANNELS;
        break;
    case PCM_PARAM_RATE:
        return SNDRV_PCM_HW_PARAM_RATE;
        break;
    case PCM_PARAM_PERIOD_TIME:
        return SNDRV_PCM_HW_PARAM_PERIOD_TIME;
        break;
    case PCM_PARAM_PERIOD_SIZE:
        return SNDRV_PCM_HW_PARAM_PERIOD_SIZE;
        break;
    case PCM_PARAM_PERIOD_BYTES:
        return SNDRV_PCM_HW_PARAM_PERIOD_BYTES;
        break;
    case PCM_PARAM_PERIODS:
        return SNDRV_PCM_HW_PARAM_PERIODS;
        break;
    case PCM_PARAM_BUFFER_TIME:
        return SNDRV_PCM_HW_PARAM_BUFFER_TIME;
        break;
    case PCM_PARAM_BUFFER_SIZE:
        return SNDRV_PCM_HW_PARAM_BUFFER_SIZE;
        break;
    case PCM_PARAM_BUFFER_BYTES:
        return SNDRV_PCM_HW_PARAM_BUFFER_BYTES;
        break;
    case PCM_PARAM_TICK_TIME:
        return SNDRV_PCM_HW_PARAM_TICK_TIME;
        break;

    default:
        return -1;
    }
}

/** Gets a mask from a PCM's hardware parameters.
 * @param pcm_params A PCM's hardware parameters.
 * @param param The parameter to get.
 * @return If @p pcm_params is NULL or @p param is not a mask, NULL is returned.
 *  Otherwise, the mask associated with @p param is returned.
 * @ingroup libtinyalsa-pcm
 */
const struct pcm_mask *pcm_params_get_mask(const struct pcm_params *pcm_params,
                                     enum pcm_param param)
{
    int p;
    struct snd_pcm_hw_params *params = (struct snd_pcm_hw_params *)pcm_params;
    if (params == NULL) {
        return NULL;
    }

    p = pcm_param_to_alsa(param);
    if (p < 0 || !param_is_mask(p)) {
        return NULL;
    }

    return (const struct pcm_mask *)param_to_mask(params, p);
}

/** Get the minimum of a specified PCM parameter.
 * @param pcm_params A PCM parameters structure.
 * @param param The specified parameter to get the minimum of.
 * @returns On success, the parameter minimum.
 *  On failure, zero.
 */
unsigned int pcm_params_get_min(const struct pcm_params *pcm_params,
                                enum pcm_param param)
{
    struct snd_pcm_hw_params *params = (struct snd_pcm_hw_params *)pcm_params;
    int p;

    if (!params)
        return 0;

    p = pcm_param_to_alsa(param);
    if (p < 0)
        return 0;

    return param_get_min(params, p);
}

/** Get the maximum of a specified PCM parameter.
 * @param pcm_params A PCM parameters structure.
 * @param param The specified parameter to get the maximum of.
 * @returns On success, the parameter maximum.
 *  On failure, zero.
 */
unsigned int pcm_params_get_max(const struct pcm_params *pcm_params,
                                enum pcm_param param)
{
    const struct snd_pcm_hw_params *params = (const struct snd_pcm_hw_params *)pcm_params;
    int p;

    if (!params)
        return 0;

    p = pcm_param_to_alsa(param);
    if (p < 0)
        return 0;

    return param_get_max(params, p);
}

static int pcm_mask_test(const struct pcm_mask *m, unsigned int index)
{
    const unsigned int bitshift = 5; /* for 32 bit integer */
    const unsigned int bitmask = (1 << bitshift) - 1;
    unsigned int element;

    element = index >> bitshift;
    if (element >= ARRAY_SIZE(m->bits))
        return 0; /* for safety, but should never occur */
    return (m->bits[element] >> (index & bitmask)) & 1;
}

static int pcm_mask_to_string(const struct pcm_mask *m, char *string, unsigned int size,
                              char *mask_name,
                              const char * const *bit_array_name, size_t bit_array_size)
{
    unsigned int i;
    unsigned int offset = 0;

    if (m == NULL)
        return 0;
    if (bit_array_size < 32) {
        STRLOG(string, offset, size, "%12s:\t%#08x\n", mask_name, m->bits[0]);
    } else { /* spans two or more bitfields, print with an array index */
        for (i = 0; i < (bit_array_size + 31) >> 5; ++i) {
            STRLOG(string, offset, size, "%9s[%d]:\t%#08x\n",
                   mask_name, i, m->bits[i]);
        }
    }
    for (i = 0; i < bit_array_size; ++i) {
        if (pcm_mask_test(m, i)) {
            STRLOG(string, offset, size, "%12s \t%s\n", "", bit_array_name[i]);
        }
    }
    return offset;
}

int pcm_params_to_string(struct pcm_params *params, char *string, unsigned int size)
{
    const struct pcm_mask *m;
    unsigned int min, max;
    unsigned int clipoffset, offset;

    m = pcm_params_get_mask(params, PCM_PARAM_ACCESS);
    offset = pcm_mask_to_string(m, string, size,
                                 "Access", access_lookup, ARRAY_SIZE(access_lookup));
    m = pcm_params_get_mask(params, PCM_PARAM_FORMAT);
    clipoffset = offset > size ? size : offset;
    offset += pcm_mask_to_string(m, string + clipoffset, size - clipoffset,
                                 "Format", format_lookup, ARRAY_SIZE(format_lookup));
    m = pcm_params_get_mask(params, PCM_PARAM_SUBFORMAT);
    clipoffset = offset > size ? size : offset;
    offset += pcm_mask_to_string(m, string + clipoffset, size - clipoffset,
                                 "Subformat", subformat_lookup, ARRAY_SIZE(subformat_lookup));
    min = pcm_params_get_min(params, PCM_PARAM_RATE);
    max = pcm_params_get_max(params, PCM_PARAM_RATE);
    STRLOG(string, offset, size, "        Rate:\tmin=%uHz\tmax=%uHz\n", min, max);
    min = pcm_params_get_min(params, PCM_PARAM_CHANNELS);
    max = pcm_params_get_max(params, PCM_PARAM_CHANNELS);
    STRLOG(string, offset, size, "    Channels:\tmin=%u\t\tmax=%u\n", min, max);
    min = pcm_params_get_min(params, PCM_PARAM_SAMPLE_BITS);
    max = pcm_params_get_max(params, PCM_PARAM_SAMPLE_BITS);
    STRLOG(string, offset, size, " Sample bits:\tmin=%u\t\tmax=%u\n", min, max);
    min = pcm_params_get_min(params, PCM_PARAM_PERIOD_SIZE);
    max = pcm_params_get_max(params, PCM_PARAM_PERIOD_SIZE);
    STRLOG(string, offset, size, " Period size:\tmin=%u\t\tmax=%u\n", min, max);
    min = pcm_params_get_min(params, PCM_PARAM_PERIODS);
    max = pcm_params_get_max(params, PCM_PARAM_PERIODS);
    STRLOG(string, offset, size, "Period count:\tmin=%u\t\tmax=%u\n", min, max);
    return offset;
}

int pcm_params_format_test(struct pcm_params *params, enum pcm_format format)
{
    unsigned int alsa_format = pcm_format_to_alsa(format);

    if (alsa_format == SNDRV_PCM_FORMAT_S16_LE && format != PCM_FORMAT_S16_LE)
        return 0; /* caution: format not recognized is equivalent to S16_LE */
    return pcm_mask_test(pcm_params_get_mask(params, PCM_PARAM_FORMAT), alsa_format);
}

/** Closes a PCM returned by @ref pcm_open.
 * @param pcm A PCM returned by @ref pcm_open.
 *  May not be NULL.
 * @return Always returns zero.
 * @ingroup libtinyalsa-pcm
 */
int pcm_close(struct pcm *pcm)
{
    if (pcm == &bad_pcm)
        return 0;

    pcm_hw_munmap_status(pcm);

    if (pcm->flags & PCM_MMAP) {
        pcm_stop(pcm);
        // pcm->ops->munmap(pcm->data, pcm->mmap_buffer, pcm_frames_to_bytes(pcm, pcm->buffer_size));
    }

    // snd_utils_close_dev_node(pcm->snd_node);
    pcm->ops->close(pcm->data);
    pcm->buffer_size = 0;
    pcm->fd = -1;
    kfree(pcm);
    return 0;
}


/** Opens a PCM by it's name.
 * @param name The name of the PCM.
 *  The name is given in the format: <i>hw</i>:<b>card</b>,<b>device</b>
 * @param flags Specify characteristics and functionality about the pcm.
 *  May be a bitwise AND of the following:
 *   - @ref PCM_IN
 *   - @ref PCM_OUT
 *   - @ref PCM_MMAP
 *   - @ref PCM_NOIRQ
 *   - @ref PCM_MONOTONIC
 * @param config The hardware and software parameters to open the PCM with.
 * @returns A PCM structure.
 *  If an error occurs, the pointer of bad_pcm is returned.
 *  Otherwise, it returns the pointer of PCM object.
 *  Client code should check that the PCM opened properly by calling @ref pcm_is_ready.
 *  If @ref pcm_is_ready returns false, check @ref pcm_get_error for more information.
 * @ingroup libtinyalsa-pcm
 */
struct pcm *pcm_open_by_name(const char *name,
                             unsigned int flags,
                             const struct pcm_config *config)
{
    unsigned int card, device;
    if (name[0] != 'h' || name[1] != 'w' || name[2] != ':') {
        oops(&bad_pcm, 0, "name format is not matched");
        return &bad_pcm;
    } else if (sscanf(&name[3], "%u,%u", &card, &device) != 2) {
        oops(&bad_pcm, 0, "name format is not matched");
        return &bad_pcm;
    }
    return pcm_open(card, device, flags, config);
}

/** Opens a PCM.
 * @param card The card that the pcm belongs to.
 *  The default card is zero.
 * @param device The device that the pcm belongs to.
 *  The default device is zero.
 * @param flags Specify characteristics and functionality about the pcm.
 *  May be a bitwise AND of the following:
 *   - @ref PCM_IN
 *   - @ref PCM_OUT
 *   - @ref PCM_MMAP
 *   - @ref PCM_NOIRQ
 *   - @ref PCM_MONOTONIC
 * @param config The hardware and software parameters to open the PCM with.
 * @returns A PCM structure.
 *  If an error occurs, the pointer of bad_pcm is returned.
 *  Otherwise, it returns the pointer of PCM object.
 *  Client code should check that the PCM opened properly by calling @ref pcm_is_ready.
 *  If @ref pcm_is_ready returns false, check @ref pcm_get_error for more information.
 * @ingroup libtinyalsa-pcm
 */
struct pcm *pcm_open(unsigned int card, unsigned int device, unsigned int flags, const struct pcm_config *config)
{
    struct pcm *pcm;
    struct snd_pcm_info info;
    int rc;

    pcm = kzalloc(sizeof(struct pcm),GFP_KERNEL);
    if (!pcm) {
        // oops(&bad_pcm, ENOMEM, "can't allocate PCM object");
        return &bad_pcm;
    }

    /* Default to hw_ops, attemp plugin open only if hw (/dev/snd/pcm*) open fails */
    pcm->ops = &hw_ops;
    pcm->fd = pcm->ops->open(card, device, flags, (void**)&pcm->data);

    // pr_err("%s %d::pcm->fd=%d pcm name=%s\n",__func__,__LINE__,pcm->fd,pcm->data->substream->pcm->name);
    if (pcm->fd < 0) {
        // oops(&bad_pcm, errno, "cannot open device (%u) for card (%u)",
        //      device, card);
        goto fail_close_dev_node;
    }

    pcm->flags = flags;

    if (pcm->ops->ioctl(pcm->data, SNDRV_PCM_IOCTL_INFO, &info)) {
        // oops(&bad_pcm, errno, "cannot get info");
        pr_err("%s %d::cannot get info\n",__func__,__LINE__);
        goto fail_close;
    }
    // pr_err("%s %d::info name=%s\n",__func__,__LINE__,info.name);
    pcm->subdevice = info.subdevice;

    if (pcm_set_config(pcm, config) != 0) {
        // memcpy(bad_pcm.error, pcm->error, sizeof(pcm->error));
        pr_err("%s %d::cannot set config\n",__func__,__LINE__);
        goto fail_close;
    }

    rc = pcm_hw_mmap_status(pcm);
    if (rc < 0) {
        pr_err("mmap status failed\n");
        goto fail;
    }

// #ifdef SNDRV_PCM_IOCTL_TTSTAMP
//     if (pcm->flags & PCM_MONOTONIC) {
//         int arg = SNDRV_PCM_TSTAMP_TYPE_MONOTONIC;
//         rc = pcm->ops->ioctl(pcm->data, SNDRV_PCM_IOCTL_TTSTAMP, &arg);
//         if (rc < 0) {
//             oops(&bad_pcm, errno, "cannot set timestamp type");
//             goto fail;
//         }
//     }
// #endif

    pcm->xruns = 0;
    return pcm;

fail:
    pcm_hw_munmap_status(pcm);
    // if (flags & PCM_MMAP)
    //     pcm->ops->munmap(pcm->data, pcm->mmap_buffer, pcm_frames_to_bytes(pcm, pcm->buffer_size));
fail_close:
    pcm->ops->close(pcm->data);
fail_close_dev_node:
// #ifdef TINYALSA_USES_PLUGINS
//     if (pcm->snd_node)
//         snd_utils_close_dev_node(pcm->snd_node);
// #endif
    kfree(pcm);
    return &bad_pcm;
}

/** Checks if a PCM file has been opened without error.
 * @param pcm A PCM handle.
 *  May be NULL.
 * @return If a PCM's file descriptor is not valid or the pointer is NULL, it returns zero.
 *  Otherwise, the function returns one.
 * @ingroup libtinyalsa-pcm
 */
int pcm_is_ready(const struct pcm *pcm)
{
    if (pcm != NULL) {
        return pcm->fd >= 0;
    }
    return 0;
}

/** Links two PCMs.
 * After this function is called, the two PCMs will prepare, start and stop in sync (at the same time).
 * If an error occurs, the error message will be written to @p pcm1.
 * @param pcm1 A PCM handle.
 * @param pcm2 Another PCM handle.
 * @return On success, zero; on failure, a negative number.
 * @ingroup libtinyalsa-pcm
 */
int pcm_link(struct pcm *pcm1, struct pcm *pcm2)
{
    int err = pcm1->ops->ioctl(pcm1->data, SNDRV_PCM_IOCTL_LINK, pcm2->fd);
    if (err == -1) {
        return -1;//oops(pcm1, errno, "cannot link PCM");
    }
    return 0;
}

/** Unlinks a PCM.
 * @see @ref pcm_link
 * @param pcm A PCM handle.
 * @return On success, zero; on failure, a negative number.
 * @ingroup libtinyalsa-pcm
 */
int pcm_unlink(struct pcm *pcm)
{
    int err = pcm->ops->ioctl(pcm->data, SNDRV_PCM_IOCTL_UNLINK);
    if (err == -1) {
        return -1;//oops(pcm, errno, "cannot unlink PCM");
    }
    return 0;
}

/** Prepares a PCM, if it has not been prepared already.
 * @param pcm A PCM handle.
 * @return On success, zero; on failure, a negative number.
 * @ingroup libtinyalsa-pcm
 */
int pcm_prepare(struct pcm *pcm)
{
    int err = 0;

    err = pcm->ops->ioctl(pcm->data, SNDRV_PCM_IOCTL_PREPARE);
    if (err < 0)
    {
        pr_err("cannot prepare channel err=%d\n",err);
        return err;//oops(pcm, errno, "cannot prepare channel");
    }
        

    /* get appl_ptr and avail_min from kernel */
    pcm_sync_ptr(pcm, SNDRV_PCM_SYNC_PTR_APPL|SNDRV_PCM_SYNC_PTR_AVAIL_MIN);

    return 0;
}

/** Starts a PCM.
 * @param pcm A PCM handle.
 * @return On success, zero; on failure, a negative number.
 * @ingroup libtinyalsa-pcm
 */
int pcm_start(struct pcm *pcm)
{
    if (pcm_state(pcm) == PCM_STATE_SETUP && pcm_prepare(pcm) != 0) {
        return -1;
    }

    /* set appl_ptr and avail_min in kernel */
    if (pcm_sync_ptr(pcm, 0) < 0)
        return -1;

    if (pcm->mmap_status->state != PCM_STATE_RUNNING) {
        if (pcm->ops->ioctl(pcm->data, SNDRV_PCM_IOCTL_START) < 0)
            return -1;//oops(pcm, errno, "cannot start channel");
    }

    return 0;
}

/** Drains a PCM.
 * @param pcm A PCM handle.
 * @return On success, zero; on failure, a negative number.
 * @ingroup libtinyalsa-pcm
 */
int pcm_drain(struct pcm *pcm)
{
    if (!pcm_is_ready(pcm))
        return -1;

    if (pcm->ops->ioctl(pcm->data, SNDRV_PCM_IOCTL_DRAIN) < 0)
        return -1;//oops(pcm, errno, "cannot drain channel");

    return 0;
}

/** Stops a PCM.
 * @param pcm A PCM handle.
 * @return On success, zero; on failure, a negative number.
 * @ingroup libtinyalsa-pcm
 */
int pcm_stop(struct pcm *pcm)
{
    if (pcm->ops->ioctl(pcm->data, SNDRV_PCM_IOCTL_DROP) < 0)
        return -1;//oops(pcm, errno, "cannot stop channel");

    return 0;
}

static inline long pcm_mmap_playback_avail(struct pcm *pcm)
{
    long avail = pcm->mmap_status->hw_ptr + (unsigned long) pcm->buffer_size -
            pcm->mmap_control->appl_ptr;

    if (avail < 0) {
        avail += pcm->boundary;
    } else if ((unsigned long) avail >= pcm->boundary) {
        avail -= pcm->boundary;
    }

    return avail;
}

static inline long pcm_mmap_capture_avail(struct pcm *pcm)
{
    long avail = pcm->mmap_status->hw_ptr - pcm->mmap_control->appl_ptr;
    if (avail < 0) {
        avail += pcm->boundary;
    }

    return avail;
}

int pcm_mmap_avail(struct pcm *pcm)
{
    pcm_sync_ptr(pcm, SNDRV_PCM_SYNC_PTR_HWSYNC);
    if (pcm->flags & PCM_IN) {
        return (int) pcm_mmap_capture_avail(pcm);
    } else {
        return (int) pcm_mmap_playback_avail(pcm);
    }
}

static void pcm_mmap_appl_forward(struct pcm *pcm, int frames)
{
    unsigned long appl_ptr = pcm->mmap_control->appl_ptr;
    appl_ptr += frames;

    /* check for boundary wrap */
    if (appl_ptr >= pcm->boundary) {
        appl_ptr -= pcm->boundary;
    }
    pcm->mmap_control->appl_ptr = appl_ptr;
}

int pcm_mmap_begin(struct pcm *pcm, void **areas, unsigned int *offset,
                   unsigned int *frames)
{
    unsigned int continuous, copy_frames, avail;

    /* return the mmap buffer */
    *areas = pcm->mmap_buffer;

    /* and the application offset in frames */
    *offset = pcm->mmap_control->appl_ptr % pcm->buffer_size;

    avail = pcm_mmap_avail(pcm);
    if (avail > pcm->buffer_size)
        avail = pcm->buffer_size;
    continuous = pcm->buffer_size - *offset;

    /* we can only copy frames if the are available and continuos */
    copy_frames = *frames;
    if (copy_frames > avail)
        copy_frames = avail;
    if (copy_frames > continuous)
        copy_frames = continuous;
    *frames = copy_frames;

    return 0;
}

static int pcm_areas_copy(struct pcm *pcm, unsigned int pcm_offset,
                          char *buf, unsigned int src_offset,
                          unsigned int frames)
{
    int size_bytes = pcm_frames_to_bytes(pcm, frames);
    int pcm_offset_bytes = pcm_frames_to_bytes(pcm, pcm_offset);
    int src_offset_bytes = pcm_frames_to_bytes(pcm, src_offset);

    /* interleaved only atm */
    if (pcm->flags & PCM_IN)
        memcpy(buf + src_offset_bytes,
               (char*)pcm->mmap_buffer + pcm_offset_bytes,
               size_bytes);
    else
        memcpy((char*)pcm->mmap_buffer + pcm_offset_bytes,
               buf + src_offset_bytes,
               size_bytes);
    return 0;
}

int pcm_mmap_commit(struct pcm *pcm, unsigned int offset, unsigned int frames)
{
    int ret;

    /* not used */
    (void) offset;

    /* update the application pointer in userspace and kernel */
    pcm_mmap_appl_forward(pcm, frames);
    ret = pcm_sync_ptr(pcm, 0);
    if (ret != 0){
        //printf("%d\n", ret);
        return ret;
    }

    return frames;
}

static int pcm_mmap_transfer_areas(struct pcm *pcm, char *buf,
                                unsigned int offset, unsigned int size)
{
    void *pcm_areas;
    int commit;
    unsigned int pcm_offset, frames, count = 0;

    while (pcm_mmap_avail(pcm) && size) {
        frames = size;
        pcm_mmap_begin(pcm, &pcm_areas, &pcm_offset, &frames);
        pcm_areas_copy(pcm, pcm_offset, buf, offset, frames);
        commit = pcm_mmap_commit(pcm, pcm_offset, frames);
        if (commit < 0) {
            oops(pcm, commit, "failed to commit %d frames\n", frames);
            return commit;
        }

        offset += commit;
        count += commit;
        size -= commit;
    }
    return count;
}

int pcm_get_poll_fd(struct pcm *pcm)
{
    return pcm->fd;
}

int pcm_avail_update(struct pcm *pcm)
{
    pcm_sync_ptr(pcm, SNDRV_PCM_SYNC_PTR_APPL|SNDRV_PCM_SYNC_PTR_AVAIL_MIN);
    return pcm_mmap_avail(pcm);
}

/** Returns available frames in pcm buffer and corresponding time stamp.
 * The clock is CLOCK_MONOTONIC if flag @ref PCM_MONOTONIC was specified in @ref pcm_open,
 * otherwise the clock is CLOCK_REALTIME.
 * For an input stream, frames available are frames ready for the application to read.
 * For an output stream, frames available are the number of empty frames available for the application to write.
 * @param pcm A PCM handle.
 * @param avail The number of available frames
 * @param tstamp The timestamp
 * @return On success, zero is returned; on failure, negative one.
 */
int pcm_get_htimestamp(struct pcm *pcm, unsigned int *avail,
                       struct timespec *tstamp)
{
//     int checking;
//     int tmp;

//     if (!pcm_is_ready(pcm))
//         return -1;

//     checking = 0;

// again:

//     tmp = pcm_avail_update(pcm);
//     if (tmp < 0)
//         return tmp; /* error */

//     if (checking && (unsigned int) tmp == *avail)
//         return 0;

//     *avail = (unsigned int) tmp;
//     *tstamp = pcm->mmap_status->tstamp;

//     /*
//      * When status is mmapped, get avail again to ensure
//      * valid timestamp.
//      */
//     if (!pcm->sync_ptr) {
//         checking = 1;
//         goto again;
//     }

    /* SYNC_PTR ioctl was used, no need to check avail */
    return 0;
}

/** Waits for frames to be available for read or write operations.
 * @param pcm A PCM handle.
 * @param timeout The maximum amount of time to wait for, in terms of milliseconds.
 * @returns If frames became available, one is returned.
 *  If a timeout occurred, zero is returned.
 *  If an error occurred, a negative number is returned.
 * @ingroup libtinyalsa-pcm
 */
int pcm_wait(struct pcm *pcm, int timeout)
{
    struct pollfd pfd;
    int err;

    // pfd.fd = pcm->fd;
    // pfd.events = POLLIN | POLLOUT | POLLERR | POLLNVAL;

    // do {
    //     /* let's wait for avail or timeout */
    //     err = pcm->ops->poll(pcm->data, &pfd, 1, timeout);
    //     if (err < 0)
    //         return -errno;

    //     /* timeout ? */
    //     if (err == 0)
    //         return 0;

    //     /* have we been interrupted ? */
    //     if (errno == -EINTR)
    //         continue;

    //     /* check for any errors */
    //     if (pfd.revents & (POLLERR | POLLNVAL)) {
    //         switch (pcm_state(pcm)) {
    //         case PCM_STATE_XRUN:
    //             return -EPIPE;
    //         case PCM_STATE_SUSPENDED:
    //             return -ESTRPIPE;
    //         case PCM_STATE_DISCONNECTED:
    //             return -ENODEV;
    //         default:
    //             return -EIO;
    //         }
    //     }
    // /* poll again if fd not ready for IO */
    // } while (!(pfd.revents & (POLLIN | POLLOUT)));

    return 1;
}

/*
 * Transfer data to/from mmapped buffer. This imitates the
 * behavior of read/write system calls.
 *
 * However, this doesn't seems to offer any advantage over
 * the read/write syscalls. Should it be removed?
 */
static int pcm_mmap_transfer(struct pcm *pcm, void *buffer, unsigned int frames)
{
    // int is_playback;

    // int state;
    // unsigned int avail;
    // unsigned int user_offset = 0;

    // int err;
    // int transferred_frames;

    // is_playback = !(pcm->flags & PCM_IN);

    // if (frames == 0)
    //     return 0;

    // /* update hardware pointer and get state */
    // err = pcm_sync_ptr(pcm, SNDRV_PCM_SYNC_PTR_HWSYNC |
    //                         SNDRV_PCM_SYNC_PTR_APPL |
    //                         SNDRV_PCM_SYNC_PTR_AVAIL_MIN);
    // if (err == -1)
    //     return -1;
    // state = pcm->mmap_status->state;

    // /*
    //  * If frames < start_threshold, wait indefinitely.
    //  * Another thread may start capture
    //  */
    // if (!is_playback && state == PCM_STATE_PREPARED &&
    //         frames >= pcm->config.start_threshold) {
    //     if (pcm_start(pcm) < 0) {
    //         return -1;
    //     }
    // }

    // while (frames) {
    //     avail = pcm_mmap_avail(pcm);

    //     if (avail < pcm->config.avail_min) {
    //         int time = -1;
    //         if (pcm->flags & PCM_NONBLOCK) {
    //             errno = EAGAIN;
    //             break;
    //         }
	//     if (pcm->flags & PCM_NOIRQ) {
    //             time = (pcm->config.avail_min - avail) / pcm->noirq_frames_per_msec;
	//         /* check the wait time and ensure it's at least 1ms */
	//         if (time < 1)
	// 	    time = 1;
	//     }
    //         /* based on the type and available data, wait dynamically */
    //         err = pcm_wait(pcm, time);
    //         if (err < 0) {
    //             errno = -err;
    //             break;
    //         }
	//     continue;
    //     }

    //     transferred_frames = pcm_mmap_transfer_areas(pcm, buffer, user_offset, frames);
    //     if (transferred_frames < 0) {
    //         break;
    //     }

    //     user_offset += transferred_frames;
    //     frames -= transferred_frames;

    //     /* start playback if written >= start_threshold */
    //     if (is_playback && state == PCM_STATE_PREPARED &&
    //             pcm->buffer_size - avail >= pcm->config.start_threshold) {
    //         if (pcm_start(pcm) < 0) {
    //             break;
    //         }
    //     }
    // }

    return -1;//user_offset ? (int) user_offset : -1;
}

int pcm_mmap_write(struct pcm *pcm, const void *data, unsigned int count)
{
    // if ((~pcm->flags) & (PCM_OUT | PCM_MMAP))
    //     return -EINVAL;

    // unsigned int frames = pcm_bytes_to_frames(pcm, count);
    // int res = pcm_writei(pcm, (void *) data, frames);

    // if (res < 0) {
    //     return res;
    // }

    return -1;//(unsigned int) res == frames ? 0 : -EIO;
}

int pcm_mmap_read(struct pcm *pcm, void *data, unsigned int count)
{
    // if ((~pcm->flags) & (PCM_IN | PCM_MMAP))
    //     return -EINVAL;

    // unsigned int frames = pcm_bytes_to_frames(pcm, count);
    // int res = pcm_readi(pcm, data, frames);

    // if (res < 0) {
    //     return res;
    // }

    return -1;//(unsigned int) res == frames ? 0 : -EIO;
}

/* Returns current read/write position in the mmap buffer with associated time stamp. */
int pcm_mmap_get_hw_ptr(struct pcm* pcm, unsigned int *hw_ptr, struct timespec *tstamp)
{
    // int rc;

    // if (pcm == NULL || hw_ptr == NULL || tstamp == NULL)
    //     return oops(pcm, EINVAL, "pcm %p, hw_ptr %p, tstamp %p", pcm, hw_ptr, tstamp);

    // if (!pcm_is_ready(pcm))
    //     return oops(pcm, errno, "pcm_is_ready failed");

    // rc = pcm_sync_ptr(pcm, SNDRV_PCM_SYNC_PTR_HWSYNC);
    // if (rc < 0)
    //     return oops(pcm, errno, "pcm_sync_ptr failed");

    // if (pcm->mmap_status == NULL)
    //     return oops(pcm, EINVAL, "pcm %p, mmap_status is NULL", pcm);

    // if ((pcm->mmap_status->state != PCM_STATE_RUNNING) &&
    //         (pcm->mmap_status->state != PCM_STATE_DRAINING))
    //     return oops(pcm, ENOSYS, "invalid stream state %d", pcm->mmap_status->state);

    // *tstamp = pcm->mmap_status->tstamp;
    // if (tstamp->tv_sec == 0 && tstamp->tv_nsec == 0)
    //     return oops(pcm, errno, "invalid time stamp");

    // *hw_ptr = pcm->mmap_status->hw_ptr;

    return 0;
}

static int pcm_rw_transfer(struct pcm *pcm, void *data, unsigned int frames)
{
    int is_playback;

    struct snd_xferi transfer;
    int res;

    is_playback = !(pcm->flags & PCM_IN);

    transfer.buf = data;
    transfer.frames = frames;
    transfer.result = 0;

    res = pcm->ops->ioctl(pcm->data, is_playback
                          ? SNDRV_PCM_IOCTL_WRITEI_FRAMES
                          : SNDRV_PCM_IOCTL_READI_FRAMES, &transfer);

    return res == 0 ? (int) transfer.result : -1;
}

static int pcm_generic_transfer(struct pcm *pcm, void *data, unsigned int frames)
{
    int res;

// #if UINT_MAX > TINYALSA_FRAMES_MAX
//     if (frames > TINYALSA_FRAMES_MAX)
//         return -EINVAL;
// #endif
    // if (frames > INT_MAX)
    //     return -EINVAL;

    if (pcm_state(pcm) == PCM_STATE_SETUP && pcm_prepare(pcm) != 0) {
        return -1;
    }

again:

    // if (pcm->flags & PCM_MMAP)
    //     res = pcm_mmap_transfer(pcm, data, frames);
    // else
        res = pcm_rw_transfer(pcm, data, frames);

    if (res < 0) {
        switch (res) {
        case EPIPE:
            pcm->xruns++;
            goto again; /*fixed warning*/
            /* fallthrough */
        case ESTRPIPE:
            /*
             * Try to restart if we are allowed to do so.
             * Otherwise, return error.
             */
            if (pcm->flags & PCM_NORESTART || pcm_prepare(pcm))
                return -1;
            goto again;
        case EAGAIN:
            if (pcm->flags & PCM_NONBLOCK)
                return -1;
            /* fallthrough */
            pr_err("cannot read/write stream data err=%d\n",res);/*fixed warning*/
            break;
        default:
            pr_err("cannot read/write stream data err=%d\n",res);
            return res;//oops(pcm, res, "cannot read/write stream data");
        }
    }

    return res;
}

/** Writes audio samples to PCM.
 * If the PCM has not been started, it is started in this function.
 * This function is only valid for PCMs opened with the @ref PCM_OUT flag.
 * @param pcm A PCM handle.
 * @param data The audio sample array
 * @param frame_count The number of frames occupied by the sample array.
 *  This value should not be greater than @ref TINYALSA_FRAMES_MAX
 *  or INT_MAX.
 * @return On success, this function returns the number of frames written; otherwise, a negative number.
 * @ingroup libtinyalsa-pcm
 */
int pcm_writei(struct pcm *pcm, const void *data, unsigned int frame_count)
{
    if (pcm->flags & PCM_IN)
        return -EINVAL;

    return pcm_generic_transfer(pcm, (void*) data, frame_count);
}
EXPORT_SYMBOL(pcm_writei);

/** Reads audio samples from PCM.
 * If the PCM has not been started, it is started in this function.
 * This function is only valid for PCMs opened with the @ref PCM_IN flag.
 * @param pcm A PCM handle.
 * @param data The audio sample array
 * @param frame_count The number of frames occupied by the sample array.
 *  This value should not be greater than @ref TINYALSA_FRAMES_MAX
 *  or INT_MAX.
 * @return On success, this function returns the number of frames written; otherwise, a negative number.
 * @ingroup libtinyalsa-pcm
 */
int pcm_readi(struct pcm *pcm, void *data, unsigned int frame_count)
{
    if (!(pcm->flags & PCM_IN))
        return -EINVAL;

    return pcm_generic_transfer(pcm, data, frame_count);
}

/** Writes audio samples to PCM.
 * If the PCM has not been started, it is started in this function.
 * This function is only valid for PCMs opened with the @ref PCM_OUT flag.
 * This function is not valid for PCMs opened with the @ref PCM_MMAP flag.
 * @param pcm A PCM handle.
 * @param data The audio sample array
 * @param count The number of bytes occupied by the sample array.
 * @return On success, this function returns zero; otherwise, a negative number.
 * @deprecated
 * @ingroup libtinyalsa-pcm
 */
int pcm_write(struct pcm *pcm, const void *data, unsigned int count)
{
    unsigned int requested_frames = pcm_bytes_to_frames(pcm, count);
    int ret = pcm_writei(pcm, data, requested_frames);

    if (ret < 0)
        return ret;

    return ((unsigned int )ret == requested_frames) ? 0 : -EIO;
}

/** Reads audio samples from PCM.
 * If the PCM has not been started, it is started in this function.
 * This function is only valid for PCMs opened with the @ref PCM_IN flag.
 * This function is not valid for PCMs opened with the @ref PCM_MMAP flag.
 * @param pcm A PCM handle.
 * @param data The audio sample array
 * @param count The number of bytes occupied by the sample array.
 * @return On success, this function returns zero; otherwise, a negative number.
 * @deprecated
 * @ingroup libtinyalsa-pcm
 */
int pcm_read(struct pcm *pcm, void *data, unsigned int count)
{
    unsigned int requested_frames = pcm_bytes_to_frames(pcm, count);
    int ret = pcm_readi(pcm, data, requested_frames);

    if (ret < 0)
        return ret;

    return ((unsigned int )ret == requested_frames) ? 0 : -EIO;
}

/** Gets the delay of the PCM, in terms of frames.
 * @param pcm A PCM handle.
 * @returns On success, the delay of the PCM.
 *  On failure, a negative number.
 * @ingroup libtinyalsa-pcm
 */
long pcm_get_delay(struct pcm *pcm)
{
    if (pcm->ops->ioctl(pcm->data, SNDRV_PCM_IOCTL_DELAY, &pcm->pcm_delay) < 0)
        return -1;

    return pcm->pcm_delay;
}

/*end of the file:rfcontrol.c */
