/*********************************************************************
 * \file sc5883lp.c
 * @Author: liming
 * @Date:   2025-05-22 11:41:27
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 16:40:07
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 *                                                              
 *********************************************************************/
#include <linux/types.h>
#include <linux/kernel.h>
#include <linux/delay.h>
#include <linux/init.h>
#include <linux/module.h>
#include <linux/errno.h>
#include <linux/gpio.h>
#include <linux/cdev.h>
#include <linux/device.h>
#include <linux/of_gpio.h>
#include <linux/semaphore.h>
#include <linux/timer.h>
#include <linux/i2c.h>
#include <asm/uaccess.h>
#include <asm/io.h>
#include "sc5883lp_reg.h"

/* 磁传感器hmc5883 */

#define SC5883LP_NAME "sc5883lp"
#define SC5883LP_CNT 	1
#define DEVICE_VERSION "V1.001"
struct sc5883lp_struct {
	int major;
	int minor;
	dev_t devid;
	struct device *device;
	struct class *class;
	struct cdev cdev;
	void *private_data;
	int16_t x, y, z;
};

#define	GET_BIT(x, bit)	((x & (1 << bit)) >> bit)

static struct sc5883lp_struct sc5883lp_drv;

static int sc5883lp_read_regs(struct sc5883lp_struct *dev, u8 reg, void *val, u16 len)
{
	struct i2c_client *client = (struct i2c_client *)dev->private_data;
	
	struct i2c_msg msg[] = {
		{
			.addr = client->addr,
			.flags = 0,
			.buf = &reg,
			.len = 1,
		},
		{
			.addr = client->addr,
			.flags = I2C_M_RD,
			.buf = val,
			.len = len,
		}
	};

	return i2c_transfer(client->adapter, msg, 2);
}

static int sc5883lp_write_regs(struct sc5883lp_struct *dev, u8 reg, u8 *buf, u16 len)
{
	u8 b[256];
	struct i2c_client *client = (struct i2c_client *)dev->private_data;
	struct i2c_msg msg[] = {
		{
			.addr = client->addr,
			.flags = 0,
			.buf = b,
			.len = len + 1,
		},
		{
		
		}
	};

	b[0] = reg;
	memcpy(&b[1], buf, len);

	return i2c_transfer(client->adapter, msg, 1);
}


static unsigned char sc5883lp_read_one_reg(struct sc5883lp_struct *dev, u8 reg)
{
	u8 data = 0;

	sc5883lp_read_regs(dev, reg, &data, 1);

	return data;
}

static void sc5883lp_write_one_reg(struct sc5883lp_struct *dev, u8 reg, u8 data)
{
	u8 buf = data;

	sc5883lp_write_regs(dev, reg, &buf, 1);
}

void sc5883lp_readdata(struct sc5883lp_struct *dev)
{
	unsigned char i =0;
	unsigned char buf[6];
    unsigned char status;
    unsigned char chipid;

    chipid = sc5883lp_read_one_reg(dev, SMC5883P_REG_CHIP_ID);
	status = sc5883lp_read_one_reg(dev, SMC5883P_REG_STATUS);
	
    while(GET_BIT(status, 0) == 0){
		 status = sc5883lp_read_one_reg(dev, SMC5883P_REG_STATUS);
	}

    if(GET_BIT(status, 1) == 1){
        dev_info(dev->device, "data overflow");
    }

	/* 循环读取所有传感器数据 */
	for(i = 0; i < 6; i++)
	{
		buf[i] = sc5883lp_read_one_reg(dev, SMC5883P_REG_DATA_X_LSB + i);
	}

	dev->x = (int16_t)((buf[1] << 8) | (buf[0]));
	dev->y = (int16_t)((buf[3] << 8) | (buf[2]));
	dev->z = (int16_t)((buf[5] << 8) | (buf[4]));
}

static void sc5883lp_reg_init(struct sc5883lp_struct *dev)
{
	smc5883p_ctrl2_t ctrl2 = {.raw = 0x00};
	smc5883p_ctrl1_t ctrl1 = {.raw = 0x00};

	/* 复位SC5883LP */
	sc5883lp_write_one_reg(dev, SMC5883P_REG_CTRL2, 0x80);

	sc5883lp_write_one_reg(dev, SMC5883P_REG_AXIS_DIR, SMC5883P_AXIS_MODE_5);
	
	/* 初始化SC5883LP为正常模式*/
	ctrl2.bit.rng = SMC5883P_RANGE_30_GAUSS;
	sc5883lp_write_one_reg(dev, SMC5883P_REG_CTRL2, ctrl2.raw);

	ctrl1.bit.mode = SMC5883P_MODE_NORMAL;
	ctrl1.bit.odr = SMC5883P_ODR_200HZ;
	ctrl1.bit.osr1 = SMC5883P_OVER_SAMPLE_RATE_8;
	ctrl1.bit.osr2 = SMC5883P_DOWN_SAMPLING_RATE_8;
	sc5883lp_write_one_reg(dev, SMC5883P_REG_CTRL1, ctrl1.raw);
}

static int sc5883lp_open(struct inode *inode, struct file *flip)
{
    flip->private_data = &sc5883lp_drv;

	return 0;
}

static ssize_t sc5883lp_read(struct file *flip, char __user *user, size_t cnt, loff_t *offt)
{
	int ret;
	int16_t data[3];
	struct sc5883lp_struct *dev =(struct sc5883lp_struct *)flip->private_data;

	sc5883lp_readdata(dev);

	data[0] = dev->x;
	data[1] = dev->y;
	data[2] = dev->z;

	ret = copy_to_user(user, data, sizeof(data));

	return 0;
}

static int sc5883lp_release (struct inode *inode, struct file *flip)
{
	struct sc5883lp_struct *dev =(struct sc5883lp_struct *)flip->private_data;

	return 0;
}

static struct file_operations sc5883lp_fops = {
	.owner = THIS_MODULE,
	.open = sc5883lp_open,
	.read = sc5883lp_read,
	.release = sc5883lp_release,
};

static int sc5883lp_probe(struct i2c_client *client, const struct i2c_device_id *of_match_table)
{
	sc5883lp_drv.major = 0;
	if(sc5883lp_drv.major){
		sc5883lp_drv.devid = MKDEV(sc5883lp_drv.major, 0);
		register_chrdev_region(sc5883lp_drv.devid, SC5883LP_CNT, SC5883LP_NAME);
	}else{
		alloc_chrdev_region(&sc5883lp_drv.devid, 0, SC5883LP_CNT, SC5883LP_NAME);
		sc5883lp_drv.major = MAJOR(sc5883lp_drv.devid);
		sc5883lp_drv.minor = MINOR(sc5883lp_drv.devid);
	}
	
	sc5883lp_drv.cdev.owner = THIS_MODULE;
	cdev_init(&sc5883lp_drv.cdev, &sc5883lp_fops);
	cdev_add(&sc5883lp_drv.cdev, sc5883lp_drv.devid, SC5883LP_CNT);

	sc5883lp_drv.class = class_create(THIS_MODULE, SC5883LP_NAME);
	sc5883lp_drv.device = device_create(sc5883lp_drv.class, NULL, sc5883lp_drv.devid, NULL, SC5883LP_NAME);

	sc5883lp_drv.private_data = client;

	sc5883lp_reg_init(&sc5883lp_drv);
    dev_info(sc5883lp_drv.device, "chipid:%#X", sc5883lp_read_one_reg(&sc5883lp_drv, SMC5883P_REG_CHIP_ID));

	return 0;
}

static void sc5883lp_remove(struct i2c_client *client)
{
	device_destroy(sc5883lp_drv.class, sc5883lp_drv.devid);
	class_destroy(sc5883lp_drv.class);
	cdev_del(&sc5883lp_drv.cdev);
	unregister_chrdev_region(sc5883lp_drv.devid, SC5883LP_CNT);
}


static const struct of_device_id	sc5883lp_match_table[] = {
	{.compatible = "victel,sc5883lp"},
	{ /* Sential */}
	
};

static const struct i2c_device_id sc5883lp_id_table[] = {
	{"victel,sc5883lp", 0},
	{ /* Sential */}

};


static struct i2c_driver sc5883lp_i2c_drv = {
	.probe = sc5883lp_probe,
	.remove = sc5883lp_remove,
	.driver = {
		.owner = THIS_MODULE,
		.name = "sc5883lp",
		.of_match_table = sc5883lp_match_table,

	},
	.id_table = sc5883lp_id_table,
};


static int __init sc5883lp_init(void)
{
	int ret = 0;
	ret = i2c_add_driver(&sc5883lp_i2c_drv);

	return 0;
}

static void __exit sc5883lp_exit(void)
{
	i2c_del_driver(&sc5883lp_i2c_drv);
}

module_init(sc5883lp_init);
module_exit(sc5883lp_exit);
MODULE_AUTHOR("cfl");
MODULE_LICENSE("GPL v2");
MODULE_VERSION(DEVICE_VERSION);


