/*********************************************************************
 * \file dac124S085.c
 * @Author: liming
 * @Date:   2025-02-19 10:37:21
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 16:39:19
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 *                                                              
 *********************************************************************/

#include "linux/device/class.h"
#include "linux/export.h"
#include "linux/kern_levels.h"
#include "linux/printk.h"
#include "linux/types.h"
#include "uapi/linux/spi/spi.h"
#include <linux/init.h>
#include <linux/kernel.h>
#include <linux/module.h>
 
#include <linux/fs.h>
#include <linux/cdev.h>
#include <linux/uaccess.h>
 
 
#include <linux/delay.h>
#include <linux/errno.h>
#include <linux/gpio.h>
#include <linux/of.h>
#include <linux/of_address.h>
#include <linux/of_gpio.h>
#include <asm/io.h>
#include <linux/device.h>
#include <asm/uaccess.h>
#include <linux/platform_device.h>
 
#include <linux/spi/spi.h>
 
#include <sound/tlv320aic32x4.h>
#include <sound/core.h>
#include <sound/pcm.h>
#include <sound/pcm_params.h>
#include <sound/soc.h>
#include <sound/soc-dapm.h>
#include <sound/initval.h>
#include <sound/tlv.h>
 
#define DAC_DEV_NAME "dac124s085"
#define PLL_DEV_CNT (1)
#define DEVICE_VERSION      "V1.001"
#define DAC124_REF       5
#define DAC124_Channel_A 0 
#define DAC124_Channel_B 1 
#define DAC124_Channel_C 2 
#define DAC124_Channel_D 3 
 
#define DAC124_MODE_1    0
#define DAC124_MODE_2    1
#define DAC124_MODE_3    2
#define DAC124_MODE_4    3
 
#define DAC124_CH_SHIT   14
#define DAC124_CH_MASK   0xC000
#define DAC124_MODE_SHIT 12
#define DAC124_MODE_MASK 0x3000
#define DAC124_DATE_MASK 0x0FFF
 
 
#define DAC124_CMD_MAGIC 'd'
#define DAC124_SET_VOLTAGE _IOW(DAC124_CMD_MAGIC, 1, unsigned short)
 
#define DAC124_REG_MAKER(channel, mode, data)  //  ((uint16_t)(((channel << DAC124_CH_SHIT) & DAC124_CH_MASK) | ((mode << DAC124_MODE_SHIT) & DAC124_MODE_MASK) | (data & DAC124_DATE_MASK)))
 
 typedef struct _ts_dac124_obj 
 {
     struct spi_device *spi;
     dev_t dac124_devno;  
     struct cdev dac124_cdev; 
     struct class *dac124_class; 
     struct device *dac124_device;
     struct device_node *dac124_devive_node; 
 }ts_dac124_obj;
 struct ts_dac124_output
 {
     uint8_t channel;
     uint8_t mode;
     float voltage;
 };
 
 static ts_dac124_obj dac124_handler;
 
 static int dac124_write_reg(struct spi_device *spi_device, uint16_t SendData)
 {
     int error = -1;
     uint16_t reg = SendData;
     struct spi_message *message;   //定义发送的消息
     struct spi_transfer *transfer; //定义传输结构体
 
     /*申请空间*/
     message = kzalloc(sizeof(struct spi_message), GFP_KERNEL);
     transfer = kzalloc(sizeof(struct spi_transfer), GFP_KERNEL);
 
     if(message != 0 && transfer !=0 )
     {
         /*填充message和transfer结构体*/
         transfer->tx_buf = &reg;
         transfer->len = 2;
         spi_message_init(message);
         spi_message_add_tail(transfer, message);
 
         error = spi_sync(spi_device, message);
         kfree(message);
         kfree(transfer);
         if (error != 0)
         {
             error = -3;
             printk(KERN_ERR "%s %d: write error : %04x! \n",__FUNCTION__,__LINE__,SendData);
         }
         else 
         {
             printk(KERN_INFO "%s %d: %04x! \n",__FUNCTION__,__LINE__,SendData);
         }
     }
     else 
     {
         error = -4;
          printk(KERN_ERR "%s %d: allcate memory error : %04x! \n",__FUNCTION__,__LINE__,SendData);
     }
 
     return error;
 }
 
 static void SetDac124OutputDate(uint8_t channel, uint8_t mode, uint16_t data)
 {
     int SendData = 0;
 
     dac124_write_reg(dac124_handler.spi, SendData);
 }
 
 static void SetDac124OutputVoltage(struct ts_dac124_output *handler)
 {
     int data;
     data = (int)(handler->voltage * 4096 *1.0 / DAC124_REF);
     SetDac124OutputDate(handler->channel, handler->mode, data);
 }
 
 static int dac124_open(struct inode *inode, struct file *filp)
 {
     return 0;
 }
 
 static ssize_t dac124_write(struct file *filp, const char __user *buf, size_t cnt, loff_t *off)
 {
     return 0;
 }
 
 static int dac124_close(struct inode *inode, struct file *filp)
 {
     return 0;
 }
 
 static long dac124_ioctl(struct file *filp, u32 cmd, unsigned long args)
 {
     int err = 0;
     struct ts_dac124_output *handler;
 
     switch(cmd) {
         case DAC124_SET_VOLTAGE:
             if(copy_from_user(handler, (struct ts_dac124_output *)args, sizeof(struct ts_dac124_output *)))
                 return -EFAULT;
                 
             SetDac124OutputVoltage(handler);
             break;
 
         default:
             err = -ENOTTY;
             break;
     }
 
     return err;
 }
 
 static struct file_operations dac124_fops = {
     .owner = THIS_MODULE,
     .open = dac124_open,
     .write = dac124_write,
     .release = dac124_close,
     .unlocked_ioctl = dac124_ioctl,
 };
 
 static int dac124_drv_probe(struct spi_device *spi)
 {
     int error = -1;
 
     dac124_handler.spi = spi;
     dac124_handler.spi->mode = SPI_MODE_1;
     dac124_handler.spi->max_speed_hz = 40000000;
     spi_setup(dac124_handler.spi);
 
 
     /*register char device*/
     error = alloc_chrdev_region(&dac124_handler.dac124_devno, 0, PLL_DEV_CNT, DAC_DEV_NAME);
     if(error < 0)
     {
         printk( KERN_ERR "%s %d:alloc device no fail!",__FUNCTION__,__LINE__);
         goto error_out;
     }
 
     /*attach file operation and init char device*/
     dac124_handler.dac124_cdev.owner = THIS_MODULE;
     cdev_init(&dac124_handler.dac124_cdev, &dac124_fops);
 
     /*add to char device*/
     error = cdev_add(&dac124_handler.dac124_cdev, dac124_handler.dac124_devno, PLL_DEV_CNT);
     if(error < 0)
     {
         printk( KERN_ERR "%s %d:register char device fial!",__FUNCTION__,__LINE__);
         goto error_add;        
     }
 
     /*register class*/
     dac124_handler.dac124_class = class_create(THIS_MODULE, DAC_DEV_NAME);
     dac124_handler.dac124_device = device_create(dac124_handler.dac124_class, NULL, dac124_handler.dac124_devno, NULL, DAC_DEV_NAME);
 
     error = 0;
 
 error_add:
     unregister_chrdev_region(dac124_handler.dac124_devno, PLL_DEV_CNT);
 
 error_out:
     return error;
 }
 
 static void dac124_remove(struct spi_device *spi)
 {
     /*delete device */
     device_destroy(dac124_handler.dac124_class, dac124_handler.dac124_devno);           //delete device
     class_destroy(dac124_handler.dac124_class);                       //delete class
     cdev_del(&dac124_handler.dac124_cdev);                       // release device no
     unregister_chrdev_region(dac124_handler.dac124_devno, PLL_DEV_CNT); //deattached char device
 }
 
 /*指定 ID 匹配表*/
 static const struct spi_device_id dac124_device_id[] = {
     {"victel,dac124", 0},
     {}
 };
 
 /*指定设备树匹配表*/
 static const struct of_device_id dac124_of_match_table[] = {
     {.compatible = "victel,dac124"},
     {}
 };
 MODULE_DEVICE_TABLE(of,dac124_of_match_table);
 /*spi 总线设备结构体*/
 struct spi_driver dac124_spi_driver = {
     .probe = dac124_drv_probe,
     .remove = dac124_remove,
 
     .driver = {
         .name = "dac124",
         .owner = THIS_MODULE,
         .of_match_table = dac124_of_match_table,
     },
 };
 /*********************************以下模拟SND设备*****************************/
 static int dac124s085_component_probe(struct snd_soc_component *component)
 {
     // dev_info(component->dev,"dac124s085_component_probe\n");
     //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
     return 0;
 }
 
 static int dac124s085_set_bias_level(struct snd_soc_component *component, enum snd_soc_bias_level level)
 {
     // dev_info(component->dev,"dac124s085_set_bias_level\n");
     //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
     return 0;
 }
 
 
 static unsigned int dac124s085_snd_reg_read(struct snd_soc_component *component, unsigned int reg)
 {
     // dev_info(component->dev,"dac124s085_snd_reg_read:%02x\n", reg);
     //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
     return 0;
 }
 static int dac124s085_snd_reg_write(struct snd_soc_component *component, unsigned int reg, unsigned int val)
 {
     // dev_info(component->dev,"dac124s085_snd_reg_write:%02x:%02x\n", reg, val);
     //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
     return 0;    
 }
 
 static const struct snd_kcontrol_new dac124s085_snd_controls[] = {
     SOC_SINGLE("DAC124S085 Power off", 0x21, 7, 1, 0), //todo:0x21 for temp
 };
 
 static const struct snd_soc_dapm_widget dac124s085_dapm_widgets[] = {
     SND_SOC_DAPM_DAC("DAC124S085 DAC", "DAC124S085 DAC", 0x21, 7, 0), //todo: 0x21 tmp
     SND_SOC_DAPM_OUTPUT("DAC124S085 Output"),
 };
 static const struct snd_soc_dapm_route dac124s085_dapm_routes[] = {
     {"DAC124S085 Output", NULL, "DAC124S085 DAC"}, 
 };
 
 static const struct snd_soc_component_driver soc_component_dev_dac124s085 = 
 {
     .probe = dac124s085_component_probe,
     .set_bias_level = dac124s085_set_bias_level,
     .legacy_dai_naming = 0,
     .controls = dac124s085_snd_controls,
     .num_controls = ARRAY_SIZE(dac124s085_snd_controls),
     .dapm_widgets = dac124s085_dapm_widgets,
     .num_dapm_widgets = ARRAY_SIZE(dac124s085_dapm_widgets),
     .dapm_routes = dac124s085_dapm_routes,
     .num_dapm_routes = ARRAY_SIZE(dac124s085_dapm_routes),
     .read = dac124s085_snd_reg_read,
     .write = dac124s085_snd_reg_write,
 };
 
 
#define dac124s085_RATES    SNDRV_PCM_RATE_8000_576000
#define dac124s085_FORMATS (SNDRV_PCM_FMTBIT_S8 | \
                 SNDRV_PCM_FMTBIT_U8 | \
                 SNDRV_PCM_FMTBIT_S16_LE | \
                 SNDRV_PCM_FMTBIT_U16_LE | \
                 SNDRV_PCM_FMTBIT_S20_LE | \
                 SNDRV_PCM_FMTBIT_U20_LE | \
                 SNDRV_PCM_FMTBIT_S24_LE | \
                 SNDRV_PCM_FMTBIT_U24_LE | \
                 SNDRV_PCM_FMTBIT_S24_3LE | \
                 SNDRV_PCM_FMTBIT_U24_3LE | \
                 SNDRV_PCM_FMTBIT_S32_LE | \
                 SNDRV_PCM_FMTBIT_U32_LE)
 
 static int dac124s085_hw_params(struct snd_pcm_substream *substream,
                  struct snd_pcm_hw_params *params,
                  struct snd_soc_dai *dai)
 {
     struct snd_soc_component *component = dai->component;
     // dev_info(component->dev,"dac124s085_hw_params\n");
     //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
     return 0;
 }
 static int dac124s085_mute(struct snd_soc_dai *dai, int mute, int direction)
 {
     struct snd_soc_component *component = dai->component;
     // dev_info(component->dev,"dac124s085_mute mute = %d dir = %d \n", mute, direction);
     //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
     return 0;
 }
 static int dac124s085_set_dai_fmt(struct snd_soc_dai *codec_dai, unsigned int fmt)
 {
     struct snd_soc_component *component = codec_dai->component;
     // dev_info(component->dev,"dac124s085_fixed I2S, ADC-I/Q 2 TDM  64BIT / DAC 192K/96K 16BIT:fmt = 0x%08x\n",fmt);
     //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
     return 0;
 }
 static int dac124s085_set_dai_sysclk(struct snd_soc_dai *codec_dai,
                   int clk_id, unsigned int freq, int dir)
 {
     struct snd_soc_component *component = codec_dai->component;
     // dev_info(component->dev,"dac124s085 set_dai_sysclk clk_id = %d freq = %u dir = %d\n",clk_id, freq, dir);
     //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
     return 0;
 }
 static const struct snd_soc_dai_ops dac124s085_dai_ops = {
     .hw_params = dac124s085_hw_params,
     .mute_stream = dac124s085_mute,
     .set_fmt = dac124s085_set_dai_fmt,
     .set_sysclk = dac124s085_set_dai_sysclk,
     .no_capture_mute = 1,
 };             
 static struct snd_soc_dai_driver dac124s085_dai[] = {
     {
         .name = "dac124s085_playback",
         .playback = {
             .stream_name = "Playback",
             .channels_min = 1,
             .channels_max = 2,
             .rates = dac124s085_RATES,
             .formats = dac124s085_FORMATS,
         },        
         .ops = &dac124s085_dai_ops,
         .symmetric_rate = 0,
         // .id = 1,/* avoid call to fmt_single_name() */
     }
 };
 
 static int simple_soc_probe(struct snd_soc_card *card)
 {
     // dev_info(card->dev,"simple_soc_probe\n");
     //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
     return 0;    
 }
 
 int dac124s085_asoc_dai_init(struct snd_soc_pcm_runtime *rtd)
 {
     // dev_info(rtd->dev,"dac124s085_asoc_dai_init\n");
     //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
     return 0;       
 }
 
 /*初始化类codec驱动
  *初始化类machine 驱动
  *platform驱动mcasp使用ti提供的驱动
  */
 static int dac124s085_data_card(struct platform_device *pdev)
 {
     int ret = 0;
     struct device *dev = &pdev->dev;
 
     /* 类codec注册component*/
     ret = devm_snd_soc_register_component(dev, &soc_component_dev_dac124s085, dac124s085_dai, 1);
 
 // err_out:    
     dev_err(dev,"dac124s085 playback dai register %s!", ret == 0? "Pass" : "Fail" );
     return ret;
 }
 /*********************************以上模拟SND设备*****************************/
 static int dac124s085_probe(struct platform_device *pdev)
 {
     int ret = 0;
     struct device *dev = &pdev->dev;
 
     ret = dac124s085_data_card(pdev);
     dev_info(dev,"dac124s085 install %s\n", ret == 0? "Pass" : "Fail" );
 
     return ret;
 }
 int dac124s085_remove(struct platform_device *pdev)
 {
     int ret = 0;
     struct device *dev = &pdev->dev;
 
     dev_info(dev,"dac124s085 uninstall %s\n", ret == 0? "Pass" : "Fail" );
     return 0;
 }
 static const struct of_device_id dac124s085_of_match[] = {
     { .compatible = "victel,dac124s085" },
     {}
 };
 MODULE_DEVICE_TABLE(of, dac124s085_of_match);
 
 static struct platform_driver dac124s085_card = {
     .driver = {
         .name = "dac124s085-card",
         .pm = &snd_soc_pm_ops,
         .of_match_table = dac124s085_of_match,
     },
     .probe = dac124s085_probe,
     .remove = dac124s085_remove,
 };
 
 module_platform_driver(dac124s085_card);
 
 
 MODULE_LICENSE("GPL");
 MODULE_AUTHOR("liming");
 MODULE_DESCRIPTION("dac124S085 driver");
MODULE_VERSION(DEVICE_VERSION);