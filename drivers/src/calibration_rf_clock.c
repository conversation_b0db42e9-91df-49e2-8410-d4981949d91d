/*********************************************************************
 * \file calibration_rf_clock.c
 * @Author: liming
 * @Date:   2025-02-19 10:37:21
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 16:39:11
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 *                                                              
 *********************************************************************/
#include <linux/clk.h>
#include <linux/err.h>
#include <linux/kernel.h>
#include <linux/module.h>
#include <linux/mutex.h>
#include <linux/of.h>
#include <linux/of_platform.h>
#include <clocksource/timer-ti-dm.h>
#include <linux/platform_data/dmtimer-omap.h>
#include <linux/platform_device.h>
#include <linux/pm_runtime.h>
#include <linux/pwm.h>
#include <linux/slab.h>
#include <linux/time.h>

#define DM_TIMER_LOAD_MIN 0xfffffffe
#define DM_TIMER_MAX      0xffffffff
#define DEVICE_VERSION "V1.001"
// 使用 omap_dm_timer_ops 函数指针操作集
// static const struct omap_dm_timer_ops *dmtimer_ops;

struct omap_dmtimer_chip {
    struct mutex mutex;
    struct omap_dm_timer *dm_timer;
    const struct omap_dm_timer_ops *pdata;
    struct platform_device *dm_timer_pdev;
};

/**
 * pwm_omap_dmtimer_start() - Start the pwm omap dm timer in pwm mode
 * @omap:    Pointer to pwm omap dm timer chip
 */
static int pwm_omap_dmtimer_start(struct platform_device *pdev)
{
    struct omap_dmtimer_chip *omap = platform_get_drvdata(pdev);
    unsigned long clk_rate;
    struct clk *fclk;
    unsigned int cnt;

    /*
     * According to OMAP 4 TRM section ********* the counter should be
     * started at 0xFFFFFFFE when overflow and match is used to ensure
     * that the PWM line is toggled on the first event.
     *
     * Note that omap_dm_timer_enable/disable is for register access and
     * not the timer counter itself.
     */
    mutex_lock(&omap->mutex);

    // omap->pdata->request();

    fclk = omap->pdata->get_fclk(omap->dm_timer);
    if (!fclk) {
        dev_err(&pdev->dev, "invalid pmtimer fclk\n");
        return -EINVAL;
    }

    clk_rate = clk_get_rate(fclk);
    if (!clk_rate) {
        dev_err(&pdev->dev, "invalid pmtimer fclk rate\n");
        return -EINVAL;
    }

    dev_info(&pdev->dev, "clk rate: %luHz\n", clk_rate);

    omap->pdata->set_prescaler(omap->dm_timer, 0);

    omap->pdata->set_source(omap->dm_timer, 0);

    // omap->pdata->enable(omap->dm_timer);
    // dev_info(&pdev->dev, "start counter value:%d\n", DM_TIMER_LOAD_MIN);
    // omap->pdata->write_counter(omap->dm_timer, DM_TIMER_LOAD_MIN);
    // omap->pdata->disable(omap->dm_timer);

    // omap->pdata->start(omap->dm_timer);

    // mdelay(1000);

    // cnt = omap->pdata->read_counter(omap->dm_timer);
    omap->pdata->capture(omap->dm_timer);

    omap->pdata->stop(omap->dm_timer);
    // dev_info(&pdev->dev, "stop counter value:%d\n", cnt);
    // dev_info(&pdev->dev, "value:%d\n", cnt - DM_TIMER_LOAD_MIN);
    omap->pdata->disable(omap->dm_timer);

unlock_mutex:
    mutex_unlock(&omap->mutex);

    return 0;
}


static int omap_dmtimer_probe(struct platform_device *pdev)
{
    struct device_node *np = pdev->dev.of_node;
    struct dmtimer_platform_data *timer_pdata;
    const struct omap_dm_timer_ops *pdata;
    struct platform_device *timer_pdev;
    struct omap_dmtimer_chip *omap;
    struct omap_dm_timer *dm_timer;
    struct device_node *timer;
    int ret = 0;
    u32 v;

    timer = of_parse_phandle(np, "ti,timers", 0);
    if (!timer)
        return -ENODEV;

    timer_pdev = of_find_device_by_node(timer);
    if (!timer_pdev) {
        dev_err(&pdev->dev, "Unable to find Timer pdev\n");
        ret = -ENODEV;
        goto err_find_timer_pdev;
    }

    timer_pdata = dev_get_platdata(&timer_pdev->dev);
    if (!timer_pdata) {
        dev_dbg(&pdev->dev,
             "dmtimer pdata structure NULL, deferring probe\n");
        ret = -EPROBE_DEFER;
        goto err_platdata;
    }

    pdata = timer_pdata->timer_ops;

    if (!pdata || !pdata->request_by_node ||
        !pdata->request ||
        !pdata->set_source ||
        !pdata->free ||
        !pdata->enable ||
        !pdata->disable ||
        !pdata->get_fclk ||
        !pdata->start ||
        !pdata->stop ||
        !pdata->set_prescaler ||
        !pdata->read_counter ||
        !pdata->write_counter ||
        !pdata->capture) {
        dev_err(&pdev->dev, "Incomplete dmtimer pdata structure\n");
        ret = -EINVAL;
        goto err_platdata;
    }

    if (!of_get_property(timer, "ti,timer-pwm", NULL)) {
        dev_err(&pdev->dev, "Missing ti,timer-pwm capability\n");
        ret = -ENODEV;
        goto err_timer_property;
    }

    dm_timer = pdata->request_by_node(timer);
    if (!dm_timer) {
        dev_err(&pdev->dev, "dts node\n");
        ret = -EPROBE_DEFER;
        goto err_request_timer;
    }

    omap = devm_kzalloc(&pdev->dev, sizeof(*omap), GFP_KERNEL);
    if (!omap) {
        dev_err(&pdev->dev, "malloc\n");
        ret = -ENOMEM;
        goto err_alloc_omap;
    }

    omap->pdata = pdata;
    omap->dm_timer = dm_timer;
    omap->dm_timer_pdev = timer_pdev;

    /*
     * Ensure that the timer is stopped before we allow timer core to call
     * timer_enable.
     */
    if (pm_runtime_active(&omap->dm_timer_pdev->dev))
        omap->pdata->stop(omap->dm_timer);

    if (!of_property_read_u32(pdev->dev.of_node, "ti,prescaler", &v))
        dev_info(&pdev->dev, "prescaler:%d\n", v);

    /* setup dmtimer clock source */
    if (!of_property_read_u32(pdev->dev.of_node, "ti,clock-source", &v))
        omap->pdata->set_source(omap->dm_timer, v);

    // omap->chip.dev = &pdev->dev;
    // omap->chip.ops = &omap_dmtimer_ops;
    // omap->chip.npwm = 1;

    mutex_init(&omap->mutex);

    // ret = pwmchip_add(&omap->chip);
    // if (ret < 0) {
    //     dev_err(&pdev->dev, "failed to register dmtimer\n");
    //     goto err_pwmchip_add;
    // }

    of_node_put(timer);

    platform_set_drvdata(pdev, omap);

    pwm_omap_dmtimer_start(pdev);

    dev_info(&pdev->dev, "probe success\n");

    return 0;

err_alloc_omap:

    pdata->free(dm_timer);
err_request_timer:

err_timer_property:
err_platdata:

    put_device(&timer_pdev->dev);
err_find_timer_pdev:

    of_node_put(timer);

    return ret;
}

static int omap_dmtimer_remove(struct platform_device *pdev)
{
    struct omap_dmtimer_chip *omap = platform_get_drvdata(pdev);

    // pwmchip_remove(&omap->chip);

    if (pm_runtime_active(&omap->dm_timer_pdev->dev))
        omap->pdata->stop(omap->dm_timer);

    omap->pdata->free(omap->dm_timer);

    put_device(&omap->dm_timer_pdev->dev);

    mutex_destroy(&omap->mutex);

    return 0;
}

static const struct of_device_id omap_dmtimer_of_match[] = {
    {.compatible = "ti,am62x-dmtimer"},
    {}
};
MODULE_DEVICE_TABLE(of, omap_dmtimer_of_match);

static struct platform_driver omap_dmtimer_driver = {
    .driver = {
        .name = "calibration_rf_clock",
        .of_match_table = of_match_ptr(omap_dmtimer_of_match),
    },
    .probe = omap_dmtimer_probe,
    .remove    = omap_dmtimer_remove,
};

module_platform_driver(omap_dmtimer_driver);

MODULE_AUTHOR("chen <<EMAIL>>");
MODULE_LICENSE("GPL v2");
MODULE_DESCRIPTION("calibration_rf_clock");
MODULE_VERSION(DEVICE_VERSION);