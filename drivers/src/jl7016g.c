/*********************************************************************
 * \file jl7016g.c
 * @Author: liming
 * @Date:   2025-05-29 09:44:50
 * @Last Modified by:   liming
 * @Last Modified time: 2025-06-12 16:39:31
 *--------------------------------------------------------------------
 *Modify history:
 *    ver       who&when                 why
 *--------------------------------------------------------------------
 *                                                              
 *********************************************************************/

#include <linux/kernel.h>
#include <linux/i2c.h>
#include <linux/module.h>
#include <linux/of.h>
#include <linux/regmap.h>
#include <sound/soc.h>

#include <sound/core.h>
#include <sound/pcm.h>
#include <sound/pcm_params.h>
#include <sound/soc.h>
#include <sound/soc-dapm.h>
#include <sound/initval.h>
#include <sound/tlv.h>

#include "jl7016g.h"


#define DEVICE_VERSION "V1.001"

/*SND device */
static int jl7016g_component_probe(struct snd_soc_component *component)
{
    // dev_info(component->dev,"jl7016g_component_probe\n");
    //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
    return 0;
}

static int jl7016g_set_bias_level(struct snd_soc_component *component, enum snd_soc_bias_level level)
{
    // dev_info(component->dev,"jl7016g_set_bias_level\n");
    //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
    return 0;
}


static unsigned int jl7016g_snd_reg_read(struct snd_soc_component *component, unsigned int reg)
{
    // dev_info(component->dev,"jl7016g_snd_reg_read:%02x\n", reg);
    //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
    return 0;
}
static int jl7016g_snd_reg_write(struct snd_soc_component *component, unsigned int reg, unsigned int val)
{
    // dev_info(component->dev,"jl7016g_snd_reg_write:%02x:%02x\n", reg, val);
    //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
    return 0;    
}

static const struct snd_kcontrol_new jl7016g_snd_controls[] = {
    SOC_SINGLE("jl7016g DAC SWITCH", JL7016G_DAC, 0, 1, 0), 
};

static const struct snd_soc_dapm_widget jl7016g_dapm_widgets[] = {
    SND_SOC_DAPM_DAC("JL7016G DAC", "JL7016G DAC", JL7016G_DAC, 0, 0), 
    SND_SOC_DAPM_ADC("JL7016G ADC", "JL7016G ADC", JL7016G_MIC, 0, 0), 
    SND_SOC_DAPM_INPUT("JL7016G INPUT"),
    SND_SOC_DAPM_OUTPUT("JL7016G OUTPUT")

};
static const struct snd_soc_dapm_route jl7016g_dapm_routes[] = {
    {"JL7016G ADC", NULL, "JL7016G INPUT"}, 
    {"JL7016G OUTPUT", NULL, "JL7016G DAC"}, 
};
static const struct snd_soc_component_driver soc_component_dev_jl7016g = 
{
    .probe = jl7016g_component_probe,
    .set_bias_level = jl7016g_set_bias_level,
    .legacy_dai_naming = 0,
    .controls = jl7016g_snd_controls,
    .num_controls = ARRAY_SIZE(jl7016g_snd_controls),
    .dapm_widgets = jl7016g_dapm_widgets,
    .num_dapm_widgets = ARRAY_SIZE(jl7016g_dapm_widgets),
    .dapm_routes = jl7016g_dapm_routes,
    .num_dapm_routes = ARRAY_SIZE(jl7016g_dapm_routes),
    .read = jl7016g_snd_reg_read,
    .write = jl7016g_snd_reg_write,
};

#define jl7016g_RATES    SNDRV_PCM_RATE_8000_576000
#define jl7016g_FORMATS (SNDRV_PCM_FMTBIT_S8 | \
				SNDRV_PCM_FMTBIT_U8 | \
				SNDRV_PCM_FMTBIT_S16_LE | \
				SNDRV_PCM_FMTBIT_U16_LE | \
                SNDRV_PCM_FMTBIT_S20_LE | \
                SNDRV_PCM_FMTBIT_U20_LE | \
				SNDRV_PCM_FMTBIT_S24_LE | \
				SNDRV_PCM_FMTBIT_U24_LE | \
				SNDRV_PCM_FMTBIT_S24_3LE | \
				SNDRV_PCM_FMTBIT_U24_3LE | \
				SNDRV_PCM_FMTBIT_S32_LE | \
				SNDRV_PCM_FMTBIT_U32_LE)

static int jl7016g_hw_params(struct snd_pcm_substream *substream,
                 struct snd_pcm_hw_params *params,
                 struct snd_soc_dai *dai)
{
    struct snd_soc_component *component = dai->component;
    // dev_info(component->dev,"jl7016g_hw_params\n");
    //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
    return 0;
}
static int jl7016g_mute(struct snd_soc_dai *dai, int mute, int direction)
{
    struct snd_soc_component *component = dai->component;
    // dev_info(component->dev,"jl7016g_mute mute = %d dir = %d \n", mute, direction);
    //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
    return 0;
}
static int jl7016g_set_dai_fmt(struct snd_soc_dai *codec_dai, unsigned int fmt)
{
    struct snd_soc_component *component = codec_dai->component;
    // dev_info(component->dev,"jl7016g_fixed I2S, ADC-I/Q 2 TDM  64BIT / DAC 192K/96K 16BIT:fmt = 0x%08x\n",fmt);
    //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
    return 0;
}
static int jl7016g_set_dai_sysclk(struct snd_soc_dai *codec_dai,
                  int clk_id, unsigned int freq, int dir)
{
    struct snd_soc_component *component = codec_dai->component;
    // dev_info(component->dev,"jl7016g set_dai_sysclk clk_id = %d freq = %u dir = %d\n",clk_id, freq, dir);
    //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
    return 0;
}
static const struct snd_soc_dai_ops jl7016g_dai_ops = {
    .hw_params = jl7016g_hw_params,
    .mute_stream = jl7016g_mute,
    .set_fmt = jl7016g_set_dai_fmt,
    .set_sysclk = jl7016g_set_dai_sysclk,
    .no_capture_mute = 1,
};             
static struct snd_soc_dai_driver jl7016g_dai = {
    .name = "jl7016g-codec",
    .playback = {
        .stream_name = "Playback",
        .channels_min = 1,
        .channels_max = 2,
        .rates = jl7016g_RATES,
        .formats = jl7016g_FORMATS,
    },  
    .capture = {
        .stream_name = "Capture",
        .channels_min = 1,
        .channels_max = 2,
        .rates = jl7016g_RATES,
        .formats = jl7016g_FORMATS,
    }, 
             
    .ops = &jl7016g_dai_ops,
    .symmetric_rate = 0,
    // .id = 1,/* avoid call to fmt_single_name() */
};

static int simple_soc_probe(struct snd_soc_card *card)
{
    // dev_info(card->dev,"simple_soc_probe\n");
    //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
    return 0;    
}

int jl7016g_asoc_dai_init(struct snd_soc_pcm_runtime *rtd)
{
    // dev_info(rtd->dev,"jl7016g_asoc_dai_init\n");
    //dummy,初始化相关，由外部IOCTL做初始化，snd设备内不做初始化
    return 0;       
}

/*初始化类codec驱动
 *初始化类machine 驱动
 *platform驱动mcasp使用ti提供的驱动
 */
static int jl7016g_data_card(struct device *dev)
{
    int ret = 0;

    /* 类codec注册component*/
    ret = devm_snd_soc_register_component(dev, &soc_component_dev_jl7016g, &jl7016g_dai, 1);

// err_out:    
    dev_err(dev,"jl7016g dai register %s!", ret == 0? "Pass" : "Fail" );
    return ret;
}
/*********************************以上模拟SND设备*****************************/

/*JL7016 proble*/
int jl7016g_probe(struct device *dev, struct regmap *regmap)
{
    int ret = 0;
    ret = jl7016g_data_card(dev);
    dev_info(dev,"jl7016g install %s\n", ret == 0? "Pass" : "Fail" );

    return 0;
}

void jl7016g_remove(struct device *dev)
{
    int ret = 0;

    dev_info(dev,"jl7016g uninstall %s\n", ret == 0? "Pass" : "Fail" );
}

/*probe from i2c device tree*/
static const struct regmap_range_cfg jl7016g_regmap_pages[] = {
    {
        .selector_reg = 0,
        .selector_mask    = 0xff,
        .window_start = 0,
        .window_len = 128,
        .range_min = JL7016G_DAC,
        .range_max = JL7016G_MIC,
    },
};

const struct regmap_config jl7016g_regmap_config = {
    .max_register = JL7016G_MIC,
    .ranges = jl7016g_regmap_pages,
    .num_ranges = ARRAY_SIZE(jl7016g_regmap_pages),
};

static int jl7016g_i2c_probe(struct i2c_client *i2c)
{
    struct regmap *regmap;
    struct regmap_config config;

    config = jl7016g_regmap_config;
    config.reg_bits = 8;
    config.val_bits = 8;

    regmap = devm_regmap_init_i2c(i2c, &config);

    return jl7016g_probe(&i2c->dev, regmap);
}

static void jl7016g_i2c_remove(struct i2c_client *i2c)
{
	jl7016g_remove(&i2c->dev);
}

static const struct of_device_id jl7016g_of_id[] = {
    { .compatible = "victel,jl7016g" },
    { /* senitel */ }
};
MODULE_DEVICE_TABLE(of, jl7016g_of_id);

static struct i2c_driver jl7016g_i2c_driver = {
    .driver = {
        .owner = THIS_MODULE,
        .name = "jl7016g",
        .of_match_table = jl7016g_of_id,
    },
    .probe_new = jl7016g_i2c_probe,
    .remove =   jl7016g_i2c_remove
};

static int __init jl7016g_init(void)
{
	int ret = 0;
	ret = i2c_add_driver(&jl7016g_i2c_driver);

	return 0;
}

static void __exit jl7016g_exit(void)
{
	i2c_del_driver(&jl7016g_i2c_driver);
}

module_init(jl7016g_init);
module_exit(jl7016g_exit);


MODULE_LICENSE("GPL");
MODULE_AUTHOR("liming");
MODULE_DESCRIPTION("jl7016 driver");
MODULE_VERSION(DEVICE_VERSION);
/*end of the file:jl7016.c*/
