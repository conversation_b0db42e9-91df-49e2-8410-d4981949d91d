#!/bin/sh
# @Author: liming
# @Date:   2025-05-29 17:41:53
# @Last Modified by:   liming
# @Last Modified time: 2025-06-12 16:43:19


echo "Begin to install..."

#以下目录需要依据实际rootfs位置修改
rootfs_path="/home/<USER>/ti-processor-sdk-linux-rt-am62xx-evm-***********/rootfs/rootfs"
victel_path="/home/<USER>/ti-processor-sdk-linux-rt-am62xx-evm-***********/rootfs/victel"
aipld_path="/home/<USER>/aipl/libs/drv"

#确认文件系统目录是否存在
echo "check root fs path ..."
if [ ! -d $rootfs_path ]; then
    echo "fail! PLEASE Check root fs path, $rootfs_path doesn't exist!"
    exit 1
else 
    echo "$rootfs_path exist pass!"
fi

echo "check root fs path ..."
if [ ! -d $victel_path ]; then
    echo "fail! PLEASE Check victel fs path, $victel_path doesn't exist!"
    exit 1
else 
    echo "$victel_path exist pass!"    
fi

echo "check apild develop path and install ..."
if [ ! -d $aipld_path ]; then
    echo "fail! PLEASE check apild develop path, $aipld_path doesn't exist"
else
    echo "$aipld_path exist pass!"
    cp ./release/include/* $aipld_path
    echo "install driver header files to aipld ok!"
fi

#提取驱动
driver_list=$(ls -l ./release | grep .ko | awk '{print $9}' )
# echo "driver list:$driver_list"

#判断是否有驱动需要安装
if [ -z "$driver_list" ]; then 
    echo "no driver to install!"
    exit 1
else 
    echo "begin install drivers ..."
fi

#依次遍历驱动并安装，如果/victel/drivers 不存在，则拷贝，并/lib/modules/6.1.83 中创建软连接
for driver_item in $driver_list
do 
    echo "install $driver_item ..."
    #拷贝更新/victel/drivers 目录下对应驱动
    cp ./release/$driver_item $victel_path/drivers/
done

#更新加载脚本
driver_load_script=$victel_path/scripts/load_drivers.sh

echo "#! /bin/sh" >$driver_load_script
echo " " >> $driver_load_script
echo "cd /lib/modules/6.1.83" >> $driver_load_script
echo "rm -rf modules.dep.bb" >> $driver_load_script
echo "depmod -a" >> $driver_load_script
echo " " >> $driver_load_script

for driver_item in $driver_list
do 
    # echo "echo \"install ${driver_item%.*}\"" >> $driver_load_script
    if [ ${driver_item%.*} != 'rfcontrol' ]; then
        echo "modprobe ${driver_item%.*}" >> $driver_load_script
    fi
    # echo " " >> $driver_load_script
done

echo " " >> $driver_load_script


echo "modprobe rfcontrol" >> $driver_load_script
echo "cd ~"  >> $driver_load_script

chmod 777 $victel_path/scripts/load_drivers.sh

# 1 copy load script save to release
cp $victel_path/scripts/load_drivers.sh ./release

echo "Drivers Install Done!"
