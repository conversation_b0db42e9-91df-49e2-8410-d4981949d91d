# .clangd
CompileFlags:
  Add:
    - "--target=arm-none-eabi"
    - "-mcpu=cortex-m4"
    - "-I/home/<USER>/ti-processor-sdk-linux-rt-am62xx-evm-09.02.01.10/board-support/ti-linux-kernel-6.1.83+gitAUTOINC+96b0ebd827-ti-rt/include"
    - "-I/home/<USER>/driver/release/include"
    - "-I/home/<USER>/ti-processor-sdk-linux-rt-am62xx-evm-09.02.01.10/linux-devkit/sysroots/aarch64-oe-linux/usr/include"
  Compiler: /home/<USER>/ti-processor-sdk-linux-rt-am62xx-evm-09.02.01.10/external-toolchain-dir/arm-gnu-toolchain-11.3.rel1-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc