/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <linux/input.h>
#include <stdio.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
#include <stdlib.h>
#include <errno.h>
#include <sys/epoll.h>

//============================> Libraries Headers <============================

//============================> Project Headers <============================
#include "multikey.h"

/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/

/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/



/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/


/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/

/******************************************************************************

                                Static Functions

 ******************************************************************************/

/******************************************************************************

                                Public Functions

 ******************************************************************************/
int keyboard_init(int fd[fd_count])
{
    int epoll_fd;
    epoll_fd = epoll_create1(0);
    if (epoll_fd == -1) {
        perror("epoll_create1");
        exit(EXIT_FAILURE);
    }

    for (int i = 0; i < fd_count; i++) {
        fd[i] = open(KEY_PATH(i), O_RDONLY | O_NOCTTY | O_NONBLOCK);
        if (fd[i] == -1) {
            fprintf(stderr, "open fd[%d] error: %s\n", i, strerror(errno));
            close(fd[i]);
            continue;
        }

        struct epoll_event ev;
        ev.events = EPOLLIN;
        ev.data.fd = fd[i];
        if (epoll_ctl(epoll_fd, EPOLL_CTL_ADD, fd[i], &ev) == -1) {
            perror("Failed to control epoll");
            close(fd[i]);
        }
    }

    return epoll_fd;
}

int keyboard_read(int epoll_fd, void *buf)
{
    static struct epoll_event events[fd_count];
    struct input_event *inputevent = (struct input_event *)buf;
    int nfds = epoll_wait(epoll_fd, events, fd_count, 0);
    if (nfds == -1) {
        fprintf(stderr, "epoll_wait error: %s\n", strerror(errno));
        return -1;
    }

    for (int n = 0; n < nfds; n++) {
        if (read(events[n].data.fd, inputevent, sizeof(struct input_event)) < 0) {
            perror("Failed to read from file");
            return -1;
        }
    }

    return 0;
}