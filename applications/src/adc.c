/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <stdio.h>
#include <stdlib.h>
#include <fcntl.h>
#include <unistd.h>
#include <stdint.h>
//============================> Libraries Headers <============================

//============================> Project Headers <============================
#include "adc.h"

/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/


/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/



/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/



/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/

/******************************************************************************

                                Static Functions

 ******************************************************************************/


/******************************************************************************

                                Public Functions

 ******************************************************************************/
int read_adc_raw(int chan) 
{
    int fd;
    int ret;
    char buf[16];

    fd = open(ADC_PATH(chan, "raw"), O_RDONLY | O_NONBLOCK | O_NOCTTY);
    if (fd == -1) {
        perror("Failed to open file");
        return -1;
    }

    ret = read(fd, buf, sizeof(buf) - 1);
    if (ret == -1) {
        perror("Failed to read from file");
        return -1;
    }

    close(fd);

    return atoi(buf);
}

double read_adc_data(int chan) 
{
    const double ADC_SCALE = 1.465201465;
    double buf;
    int raw_buf;

    raw_buf = read_adc_raw(chan);
    if (raw_buf == -1) {
        perror("Failed to read adc raw");
        return -1;
    }

    buf = raw_buf * ADC_SCALE;

    return buf;
}