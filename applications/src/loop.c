#include <stdio.h>
#include <string.h>
#include <strings.h>
#include <termios.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <stdlib.h>
#include <stdint.h>
#include <linux/input.h>
#include <sys/reboot.h>
#include <errno.h>

#include "gps.h"

int main(int argc, char **argv)
{
    char buffer[256];
    int nSpeed = atoi(argv[1]);

    int fd = open_port(GPS_PATH);
    if (fd < 0) {
        printf("open port failed\n");
        return -1;
    }

    if(set_opt(fd, nSpeed, 8, 1, 'n') < 0)
    {
        printf("set opt failed\n");
        close(fd);
        return -1;
    }

    strcpy(buffer, "BT TEST\n");
    while (1) {
        write(fd, buffer, sizeof(buffer));
        printf("TX:%s\n", buffer);

        int bytes_read = read(fd, buffer, sizeof(buffer) - 1);
        if (bytes_read > 0) {
            buffer[bytes_read] = '\0';
            printf("RX: %s\n", buffer);
            
        }
        sleep(3);
    }
    
    close(fd);
    return 0;
}