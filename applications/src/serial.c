/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <stdio.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <termios.h>
#include <string.h>
#include <stdlib.h>
//============================> Libraries Headers <============================

//============================> Project Headers <============================
#include "serial.h"

/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/


/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/



/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/



/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/

/******************************************************************************

                                Static Functions

 ******************************************************************************/

/******************************************************************************

                                Public Functions

 ******************************************************************************/

int set_opt(int fd, int nSpeed, int nBits, int nStop, char nPartiy)
{
	struct termios old_cfg, new_cfg;
	if(0 > tcgetattr(fd, &old_cfg))
	{
		perror("Tcgetattr Error");
		return -1;
	}

	bzero(&new_cfg, sizeof(struct termios));
	cfmakeraw(&new_cfg);
	new_cfg.c_cflag |= CREAD | CLOCAL;
	new_cfg.c_cflag &= ~CSIZE;

	switch(nSpeed)
	{
		case 1200:
			cfsetispeed(&new_cfg, B1200);
			cfsetospeed(&new_cfg, B1200);
			break;
		case 9600:
			cfsetispeed(&new_cfg, B9600);
			cfsetospeed(&new_cfg, B9600);
			break;
		case 19200:
			cfsetispeed(&new_cfg, B19200);
			cfsetospeed(&new_cfg, B19200);
			break;
		case 38400:
			cfsetispeed(&new_cfg, B38400);
			cfsetospeed(&new_cfg, B38400);
			break;
		case 57600:
			cfsetispeed(&new_cfg, B57600);
			cfsetospeed(&new_cfg, B57600);
			break;
		case 115200:
			cfsetispeed(&new_cfg, B115200);
			cfsetospeed(&new_cfg, B115200);
			break;
		case 230400:
			cfsetispeed(&new_cfg, B230400);
			cfsetospeed(&new_cfg, B230400);
			break;
		case 460800:
			cfsetispeed(&new_cfg, B460800);
			cfsetospeed(&new_cfg, B460800);
			break;
		case 500000:
			cfsetispeed(&new_cfg, B500000);
			cfsetospeed(&new_cfg, B500000);
			break;
		case 576000:
			cfsetispeed(&new_cfg, B576000);
			cfsetospeed(&new_cfg, B576000);
			break;
		case 921600:
			cfsetispeed(&new_cfg, B921600);
			cfsetospeed(&new_cfg, B921600);
			break;
		case 1000000:
			cfsetispeed(&new_cfg, B1000000);
			cfsetospeed(&new_cfg, B1000000);
			break;
		case 1152000:
			cfsetispeed(&new_cfg, B1152000);
			cfsetospeed(&new_cfg, B1152000);
			break;
		case 1500000:
			cfsetispeed(&new_cfg, B1500000);
			cfsetospeed(&new_cfg, B1500000);
			break;
		default:
			cfsetispeed(&new_cfg, B115200);
			cfsetospeed(&new_cfg, B115200);
			break;
	}

	switch(nBits)
	{
		case 7:
			new_cfg.c_cflag |= CS7;
			break;
		case 8:
			new_cfg.c_cflag |= CS8;
			break;
		default:
			new_cfg.c_cflag |= CS8;
			break;
	}
	
	switch(nStop)
	{
		case 1:
			new_cfg.c_cflag &= ~CSTOPB;
			break;
		case 2:
			new_cfg.c_cflag |= CSTOPB;
			break;
		default:
			new_cfg.c_cflag &= ~CSTOPB;
			break;
	}

	switch(nPartiy)
	{
		case 'n':
			new_cfg.c_cflag &= ~PARENB;
			break;
		case 'o':
			new_cfg.c_cflag |= (PARENB | PARODD);
			new_cfg.c_iflag |= (ISTRIP | INPCK);
			break;
		case 'e':
			new_cfg.c_cflag |= PARENB;
			new_cfg.c_cflag &= ~PARODD;
			new_cfg.c_iflag |= (ISTRIP | INPCK);
			break;
		default:
			new_cfg.c_cflag &= ~PARENB;
			break;
	}

	new_cfg.c_cc[VMIN] = 0;
	new_cfg.c_cc[VTIME] = 0;

	tcflush(fd, TCIOFLUSH);

	if(0 > tcsetattr(fd, TCSANOW, &new_cfg))
	{
		perror("Tcsetattr Error");
		return -1;
	}

	return 0;
}

int set_baud(int fd, int nSpeed)
{
	struct termios option;

	if (tcgetattr(fd, &option)) {
		return -1;
	}

	switch (nSpeed)
	{
		case 1200:
			cfsetispeed(&option, B1200);
			cfsetospeed(&option, B1200);
			break;
		case 9600:
			cfsetispeed(&option, B9600);
			cfsetospeed(&option, B9600);
			break;
		case 19200:
			cfsetispeed(&option, B19200);
			cfsetospeed(&option, B19200);
			break;
		case 38400:
			cfsetispeed(&option, B38400);
			cfsetospeed(&option, B38400);
			break;
		case 57600:
			cfsetispeed(&option, B57600);
			cfsetospeed(&option, B57600);
			break;
		case 115200:
			cfsetispeed(&option, B115200);
			cfsetospeed(&option, B115200);
			break;
		case 230400:
			cfsetispeed(&option, B230400);
			cfsetospeed(&option, B230400);
			break;
		case 460800:
			cfsetispeed(&option, B460800);
			cfsetospeed(&option, B460800);
			break;
		case 500000:
			cfsetispeed(&option, B500000);
			cfsetospeed(&option, B500000);
			break;
		case 576000:
			cfsetispeed(&option, B576000);
			cfsetospeed(&option, B576000);
			break;
		case 921600:
			cfsetispeed(&option, B921600);
			cfsetospeed(&option, B921600);
			break;
		case 1000000:
			cfsetispeed(&option, B1000000);
			cfsetospeed(&option, B1000000);
			break;
		case 1152000:
			cfsetispeed(&option, B1152000);
			cfsetospeed(&option, B1152000);
			break;
		case 1500000:
			cfsetispeed(&option, B1500000);
			cfsetospeed(&option, B1500000);
			break;
		default:
			cfsetispeed(&option, B115200);
			cfsetospeed(&option, B115200);
			break;
	}

	if(tcsetattr(fd, TCSANOW, &option) < 0)
	{
		return -1;
	}

	return 0;
}

int open_port(char *com)
{
	int fd;

	fd = open(com, O_RDWR | O_NONBLOCK | O_NOCTTY);
	if(0 > fd)
	{
		perror("Open Port Error");
		return -1;
	}

	if(0 > fcntl(fd, F_SETFD, 0))
	{
		perror("Fcntl F_SETFD Error");
		return -1;
	}

	return fd;
}