/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <pthread.h>
#include <sys/time.h>
//============================> Libraries Headers <============================

//============================> Project Headers <============================
#include "bt_protocol.h"

/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/


/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/



/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/
// 串口缓冲区
static struct {
    uint8_t s_rx_buffer[RX_BUFFER_SIZE];
    size_t s_rx_head;
    size_t s_rx_tail;
    size_t s_rx_count;
} bt_buffer_t = {0};

static bt_thread_t g_receive_thread = {0};
/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/

/******************************************************************************

                                Static Functions

 ******************************************************************************/
static void bt_init_frame(comm_frame_t *frame)
{
    if (!frame) return;
    
    memset(frame, 0, sizeof(comm_frame_t));
    
    // 设置帧头
    frame->header[0] = FRAME_HEADER_1;
    frame->header[1] = FRAME_HEADER_2;
    frame->header[2] = FRAME_HEADER_3;
    frame->header[3] = FRAME_HEADER_4;
    
    // 设置帧尾
    frame->tail[0] = FRAME_TAIL_1;
    frame->tail[1] = FRAME_TAIL_2;
}

static uint16_t bt_frame_calculate_checksum(const comm_frame_t *frame)
{
    if (!frame) return -1;
    
    uint16_t sum = 0;
    const uint8_t *ptr = (const uint8_t *)&frame->ctask;
    
    // 计算88字节的校验和
    for (int i = 0; i < 88; i++) {
        sum += ptr[i];
    }
    
    return sum;
}

static void bt_frame_set_checksum(comm_frame_t *frame)
{
    if (!frame) return;
    frame->checksum = bt_frame_calculate_checksum(frame);
}

static bool bt_frame_validate(const comm_frame_t *frame)
{
    if (!frame) return false;
    
    // 检查帧头
    if (frame->header[0] != FRAME_HEADER_1 || 
        frame->header[1] != FRAME_HEADER_2 ||
        frame->header[2] != FRAME_HEADER_3 || 
        frame->header[3] != FRAME_HEADER_4) {
        BT_ERROR("帧头验证失败");
        return false;
    }
    
    // 检查帧尾
    if (frame->tail[0] != FRAME_TAIL_1 || frame->tail[1] != FRAME_TAIL_2) {
        BT_ERROR("帧尾验证失败");
        return false;
    }
    
    // 检查校验和
    uint16_t calculated_checksum = bt_frame_calculate_checksum(frame);
    if (frame->checksum != calculated_checksum) {
        BT_ERROR("校验和验证失败");
        return false;
    }
    
    return true;
}

static int bt_serial_send_frame(bt_context_t *ctx, const comm_frame_t *frame)
{
    if (ctx->serial_fd < 0 || !frame) return -1;

    ssize_t bytes_written = write(ctx->serial_fd, frame, FRAME_SIZE);
    if (bytes_written != FRAME_SIZE) {
        BT_ERROR("发送帧失败");
        return -1;
    }

    // 阻塞，直到数据全部发送完毕
    if (tcdrain(ctx->serial_fd) != 0) {
        BT_ERROR("数据发送同步失败");
        return -1;
    }

    // 发送零帧
    if (!ctx->continuous) {
        int ret = bt_serial_send_zero_frame(ctx);
        if (ret < 0) {
            BT_ERROR("发送零帧失败\n");
            return -1;
        }
    }

    return 0;
}

static int bt_init_audio_buffer(bt_audio_buffer_t *buffer)
{
    if(!buffer) return -1;

    memset(buffer, 0, sizeof(bt_audio_buffer_t));

    if (pthread_mutex_init(&buffer->mutex, NULL) != 0) {
        BT_ERROR("音频缓冲区互斥锁初始化失败");
        return -1;
    }

    printf("音频缓冲区互斥锁初始化成功\n");
    return 0;
}

static void bt_destroy_audio_buffer(bt_audio_buffer_t *buffer)
{
    if (buffer) {
        pthread_mutex_destroy(&buffer->mutex);
    }
}

// 向缓冲区添加音频帧
static int bt_audio_buffer_push(bt_audio_buffer_t *buffer, const uint8_t *audio_data, uint16_t sequence)
{
    if(!buffer || !audio_data) return -1;

    pthread_mutex_lock(&buffer->mutex);

    // 检查缓冲区是否已满
    if(buffer->count >= MAX_CONTINUOUS_FRAMES){
        printf("音频缓冲区已满，丢弃最旧帧");
        buffer->tail = (buffer->tail + 1) % MAX_CONTINUOUS_FRAMES;
        buffer->count--;
    }

    // 添加新帧
    memcpy(buffer->frame[buffer->head], audio_data, AUDIO_FRAME_SIZE);
    buffer->sequence[buffer->head] = sequence;

    buffer->head = (buffer->head + 1) % MAX_CONTINUOUS_FRAMES;
    buffer->count++;

    pthread_mutex_unlock(&buffer->mutex);

    return 0;
}

// 从缓冲区取出音频帧
static int bt_audio_buffer_pop(bt_audio_buffer_t *buffer, uint8_t *audio_data, uint16_t *sequence)
{
    if(!buffer || !audio_data) return -1;

    pthread_mutex_lock(&buffer->mutex);
    
    if(buffer->count == 0){
        pthread_mutex_unlock(&buffer->mutex);
        // printf("缓冲区为空");
        return -1; // 缓冲区为空
    }

    //取出帧
    memcpy(audio_data, buffer->frame[buffer->tail], 80);
    *sequence = buffer->sequence[buffer->tail];

    buffer->tail = (buffer->tail + 1) % MAX_CONTINUOUS_FRAMES;
    buffer->count--;
    
    pthread_mutex_unlock(&buffer->mutex);

    return 0;
}

static int receive_raw_data(bt_context_t *ctx, uint8_t *buffer, size_t size)
{
    // 读取数据
    int bytes_read = read(ctx->serial_fd, buffer, size);
    if (bytes_read > 0 && ctx->debug_mode) {
        printf("Frame content (hex): ");
        for (int i = 0; i < bytes_read; i++) {
            printf("%02X ", buffer[i]);
            if ((i + 1) % 16 == 0) printf("\n\t\t     ");
        }
        printf("\n");
    } 

    return bytes_read;
}

// 在环形缓冲区中查找并提取完整帧
static bool extract_complete_frame(bt_context_t *ctx, comm_frame_t *frame)
{
    // 确保有足够数据
    if (bt_buffer_t.s_rx_count < FRAME_SIZE) {
        return false;
    }
    
    // 搜索帧头
    for (size_t i = 0; i <= bt_buffer_t.s_rx_count - FRAME_SIZE; i++) {
        size_t pos = (bt_buffer_t.s_rx_tail + i) % RX_BUFFER_SIZE;
        
        // 检查帧头
        if (bt_buffer_t.s_rx_buffer[pos] != FRAME_HEADER_1) {
            continue;
        }
        
        // 提取帧数据
        if ((pos + FRAME_SIZE) <= RX_BUFFER_SIZE) {
            // 连续数据
            memcpy(frame, &bt_buffer_t.s_rx_buffer[pos], FRAME_SIZE);
        } else {
            // 跨边界数据
            size_t first_part = RX_BUFFER_SIZE - pos;
            memcpy(frame, &bt_buffer_t.s_rx_buffer[pos], first_part);
            memcpy((uint8_t*)frame + first_part, bt_buffer_t.s_rx_buffer, FRAME_SIZE - first_part);
        }
        
        // 验证帧
        if (bt_frame_validate(frame)) {
            // 移除已处理的数据
            size_t remove_bytes = i + FRAME_SIZE;
            bt_buffer_t.s_rx_tail = (bt_buffer_t.s_rx_tail + remove_bytes) % RX_BUFFER_SIZE;
            bt_buffer_t.s_rx_count = (bt_buffer_t.s_rx_count >= remove_bytes) ? 
                                    bt_buffer_t.s_rx_count - remove_bytes : 0;
            
            BT_LOG("成功提取完整帧，剩余: %zu 字节\n", bt_buffer_t.s_rx_count);
            return true;
        }
    }
    
    // 如果缓冲区太满但没找到有效帧，清理一些空间
    if (bt_buffer_t.s_rx_count > RX_BUFFER_SIZE - FRAME_SIZE) {
        bt_buffer_t.s_rx_tail = (bt_buffer_t.s_rx_tail + FRAME_SIZE) % RX_BUFFER_SIZE;
        bt_buffer_t.s_rx_count -= FRAME_SIZE;
        BT_LOG("清理缓冲区空间\n");
    }
    
    return false;
}

static void process_frame(bt_context_t *ctx, comm_frame_t *frame)
{
    switch (frame->abs_addr)
    {
        case BT_ADDR_READ_MAC:
        {
            BT_LOG("读取MAC地址和版本指令已接收");
            memcpy(ctx->device_info.mac, frame->data, 6);
            memcpy(&ctx->device_info.version, frame->data + 6, sizeof(bt_version_t)); 
            BT_LOG("蓝牙MAC地址: %s", ctx->device_info.mac);
            BT_LOG("蓝牙版本号: %d.%d", ctx->device_info.version.bt_major, ctx->device_info.version.bt_minor);
            BT_LOG("蓝牙固件版本号: %d.%d", ctx->device_info.version.bt_fm_major, ctx->device_info.version.bt_fm_minor);
            BT_LOG("蓝牙固件日期: %d-%d-%d", ctx->device_info.version.bt_fm_year, ctx->device_info.version.bt_fm_month, ctx->device_info.version.bt_fm_day);
            break;
        }
        case BT_ADDR_SCAN_RESULT:
        {
            BT_LOG("扫描结果指令已接收");
            
            // 获取当前帧的索引和总帧数
            int frame_idx = frame->index & 0x00FF;  // 低8位是当前帧索引
            int total_frames = (frame->index >> 8) & 0xFF;  // 高8位是总帧数
            
            BT_LOG("扫描结果帧: %d/%d\n", frame_idx + 1, total_frames);
            
            // 从当前帧的DATA域提取设备名称（每帧最多4个设备）
            int device_start_idx = frame_idx * 4;  // 当前帧设备起始索引
            int device_count = 0;  // 本帧检测到的设备数量
            
            // 清空当前帧对应的设备名称区域
            memset(&ctx->device_info.scan_device_name[device_start_idx], 0, 4 * MAX_DEVICE_NAME);
            
            // 提取设备名称
            for (int dev_idx = 0; dev_idx < 4 && (device_start_idx + dev_idx) < MAX_DEVICES; dev_idx++) {
                uint8_t *device_start = &frame->data[dev_idx * MAX_DEVICE_NAME];
                
                if (device_start[0] == 0) {
                    break;  // 没有更多设备
                }
                
                // 复制设备名称
                memcpy(ctx->device_info.scan_device_name[device_start_idx + dev_idx], 
                        device_start, MAX_DEVICE_NAME);
                                            
                device_count++;
            }
            
            // 如果是最后一帧或总帧数为1，打印扫描结果汇总
            if (frame_idx == total_frames - 1 || total_frames == 1) {
                // 统计有效设备数量
                int total_devices = 0;
                for (int i = 0; i < MAX_DEVICES; i++) {
                    if (ctx->device_info.scan_device_name[i][0] != 0) {
                        total_devices++;
                    }
                }
                
                BT_LOG("=== 扫描结果汇总 ===");
                BT_LOG("总共扫描到 %d 个设备:", total_devices);
                
                for (int i = 0; i < MAX_DEVICES; i++) {
                    if (ctx->device_info.scan_device_name[i][0] != 0) {
                        printf("  %d. %s\n", i + 1, ctx->device_info.scan_device_name[i]);
                    }
                }
            }
            
            break;
        }
        case BT_ADDR_CONNECT:
        {
            BT_LOG("连接指令已接收");
            ctx->device_info.conn_state = frame->data[0];
            BT_LOG("连接状态: %d", ctx->device_info.conn_state);
            break;
        }
        case BT_ADDR_RECONNECT:
        {
            BT_LOG("回连指令已接收");
            memcpy(&ctx->device_info.connect_device_name, frame->data, 20);
            BT_LOG("回连:连接设备%s", ctx->device_info.connect_device_name);
            break;
        }
        case BT_ADDR_DISCONNECT:
        {
            BT_LOG("断开连接指令已接收");
            ctx->device_info.conn_state = frame->data[0];
            BT_LOG("断开连接状态: %d", ctx->device_info.conn_state);
            break;
        }
        case BT_ADDR_CALL_CTRL:
        {
            BT_LOG("通话控制指令已接收");
            ctx->device_info.call_index = frame->index;
            BT_LOG("通话控制: %d", ctx->device_info.call_index);
            break;
        }
        case BT_ADDR_AUDIO:
        {
            // 处理音频帧
            static FILE *pcm_file = NULL;
            short pcm_data[80];
            
            // 第一次接收音频帧时，创建PCM文件
            if (pcm_file == NULL) {
                pcm_file = fopen("bt_recorded_audio.pcm", "wb");
                if (pcm_file == NULL) {
                    BT_ERROR("无法创建PCM文件");
                    break;
                }
                BT_LOG("已创建PCM录音文件: bt_recorded_audio.pcm");
            }

            bt_process_received_audio(ctx, frame);

            bt_audio_get_pcm(ctx, pcm_data);
           
            // 写入PCM数据到文件
            if (fwrite(pcm_data, 2, AUDIO_FRAME_SIZE, pcm_file) != AUDIO_FRAME_SIZE) {
                BT_ERROR("写入PCM数据到文件失败");
            }
            // 确保数据写入磁盘
            fflush(pcm_file); 

            // todo:当period_time偏离10ms的处理，帧间隔大插帧，帧间隔小丢帧或合并

            break;
        }
        case BT_ADDR_PTT:
        {
            BT_LOG("PTT指令已接收");
            ctx->device_info.ptt_state = frame->data[0];
            BT_LOG("PTT状态: %d", ctx->device_info.ptt_state);
            break;
        }
        case BT_ADDR_UPGRADE:
        {
            BT_LOG("蓝牙升级指令已接收");
            break;
        }
        default:
            break;
    }
}

static void* receive_thread_func(void *arg)
{
    bt_thread_t *thread_ctx = (bt_thread_t*)arg;
    bt_context_t *ctx = thread_ctx->ctx;
    bt_device_info_t *device_info = &ctx->device_info;
    uint8_t temp_buffer[384];
    comm_frame_t frame;
    
    while (thread_ctx->thread_running) {
        // 接收原始数据
        int bytes_received = receive_raw_data(ctx, temp_buffer, sizeof(temp_buffer));
        
        if (bytes_received > 0) {
            // 将数据添加到环形缓冲区
            size_t available_space = RX_BUFFER_SIZE - bt_buffer_t.s_rx_count;
            if (available_space < bytes_received) {
                // 缓冲区空间不足，丢弃一些旧数据
                size_t discard = bytes_received - available_space + FRAME_SIZE;
                bt_buffer_t.s_rx_tail = (bt_buffer_t.s_rx_tail + discard) % RX_BUFFER_SIZE;
                bt_buffer_t.s_rx_count -= discard;
            }
            
            size_t first_part_size = RX_BUFFER_SIZE - bt_buffer_t.s_rx_head;
            if (first_part_size >= bytes_received) {
                // 数据可以一次性复制
                memcpy(&bt_buffer_t.s_rx_buffer[bt_buffer_t.s_rx_head], temp_buffer, bytes_received);
                bt_buffer_t.s_rx_head = (bt_buffer_t.s_rx_head + bytes_received) % RX_BUFFER_SIZE;
            } else {
                // 需要分两次复制
                memcpy(&bt_buffer_t.s_rx_buffer[bt_buffer_t.s_rx_head], temp_buffer, first_part_size);
                memcpy(bt_buffer_t.s_rx_buffer, temp_buffer + first_part_size, bytes_received - first_part_size);
                bt_buffer_t.s_rx_head = bytes_received - first_part_size;
            }
            bt_buffer_t.s_rx_count += bytes_received;
            
            BT_LOG("接收 %d 字节，缓冲区: %zu/%d", bytes_received, bt_buffer_t.s_rx_count, RX_BUFFER_SIZE);
            
            // 尝试提取完整帧
            while (extract_complete_frame(ctx, &frame)) {
                // 直接处理完整帧
                BT_LOG("提取到完整帧 (ctask: 0x%02X, index: %d)", frame.ctask, frame.index);
                bt_frame_print_hex(ctx, &frame);
                process_frame(ctx, &frame);
            }
        }
        else if (bytes_received < 0 || errno == EAGAIN) {
            BT_ERROR("串口读取失败");
            usleep(10000);
        }
    }
    
    BT_LOG("串口接收线程退出");
    return NULL;
}

/******************************************************************************

                                Public Functions

 ******************************************************************************/
int bt_read_mac_version(bt_context_t *ctx)
{
    if (ctx->serial_fd < 0) return -1;
    
    comm_frame_t cmd_frame;
    
    bt_init_frame(&cmd_frame);
    cmd_frame.ctask = BT_CMD_BASE;
    cmd_frame.index = 0x0000;
    cmd_frame.abs_addr = BT_ADDR_READ_MAC;
    bt_frame_set_checksum(&cmd_frame);
    
    int ret = bt_serial_send_frame(ctx, &cmd_frame);
                                                    
    return ret;
}

int bt_read_scan_result(bt_context_t *ctx, uint16_t index)
{
    if (ctx->serial_fd < 0) return -1;
    
    int device_count = 0;
    comm_frame_t cmd_frame;
    
    bt_init_frame(&cmd_frame);
    cmd_frame.ctask = BT_CMD_BASE;
    cmd_frame.index = index;
    cmd_frame.abs_addr = BT_ADDR_SCAN_RESULT;
    bt_frame_set_checksum(&cmd_frame);
    
    int ret = bt_serial_send_frame(ctx, &cmd_frame);
    
    return ret;
}

int bt_connect_device(bt_context_t *ctx)
{
    if (ctx->serial_fd < 0) return -1;
    
    comm_frame_t cmd_frame;
    
    bt_init_frame(&cmd_frame);
    cmd_frame.ctask = BT_CMD_BASE;
    cmd_frame.index = 0x0000;
    cmd_frame.abs_addr = BT_ADDR_CONNECT;
    
    strncpy((char *)cmd_frame.data, ctx->device_info.connect_device_name, MAX_DEVICE_NAME);
    bt_frame_set_checksum(&cmd_frame);
    
    int ret = bt_serial_send_frame(ctx, &cmd_frame);
    
    return ret;
}

int bt_reconnect_device(bt_context_t *ctx)
{
    if (ctx->serial_fd < 0) return -1;
    
    comm_frame_t cmd_frame;
    char device_name[20];

    bt_init_frame(&cmd_frame);
    cmd_frame.ctask = BT_CMD_BASE;
    cmd_frame.index = 0x0000;
    cmd_frame.abs_addr = BT_ADDR_RECONNECT;
    bt_frame_set_checksum(&cmd_frame);
    
    int ret = bt_serial_send_frame(ctx, &cmd_frame);

    return ret;
}

int bt_disconnect_device(bt_context_t *ctx)
{
    if (ctx->serial_fd < 0) return -1;

    comm_frame_t cmd_frame;
    
    bt_init_frame(&cmd_frame);
    cmd_frame.ctask = BT_CMD_BASE;
    cmd_frame.index = 0x0000;
    cmd_frame.abs_addr = BT_ADDR_DISCONNECT;
    bt_frame_set_checksum(&cmd_frame);
    
    int ret = bt_serial_send_frame(ctx, &cmd_frame);
    
    return ret;
}

int bt_call_control(bt_context_t *ctx, call_index_t call_index)
{
    if (ctx->serial_fd < 0) return -1;
    
    comm_frame_t cmd_frame;
    
    bt_init_frame(&cmd_frame);
    cmd_frame.ctask = BT_CMD_BASE;
    cmd_frame.index = call_index;
    cmd_frame.abs_addr = BT_ADDR_CALL_CTRL;
    bt_frame_set_checksum(&cmd_frame);
    
    int ret = bt_serial_send_frame(ctx, &cmd_frame);
    
    return ret;
}

int bt_send_audio(bt_context_t *ctx, const uint8_t *alaw_data, uint16_t sequence)
{
    if (ctx->serial_fd < 0 || !alaw_data) return -1;
    
    comm_frame_t cmd_frame;
    
    bt_init_frame(&cmd_frame);
    cmd_frame.ctask = BT_CMD_BASE;
    cmd_frame.index = sequence;
    cmd_frame.abs_addr = BT_ADDR_AUDIO;
    
    // 复制音频数据
    memcpy(cmd_frame.data, alaw_data, AUDIO_FRAME_SIZE);
    
    bt_frame_set_checksum(&cmd_frame);
    
    BT_LOG("发送音频数据命令 (序列号: %d):\n", sequence);
    bt_frame_print_hex(ctx, &cmd_frame);
    
    return bt_serial_send_frame(ctx, &cmd_frame);
}

int bt_ptt_event(bt_context_t *ctx, ptt_state_t ptt_state)
{
    if (ctx->serial_fd < 0) return -1;
    
    comm_frame_t cmd_frame;
    
    bt_init_frame(&cmd_frame);
    cmd_frame.ctask = BT_CMD_BASE;
    cmd_frame.index = 0x0000;
    cmd_frame.abs_addr = BT_ADDR_PTT;
    cmd_frame.data[0] = ptt_state;
    
    bt_frame_set_checksum(&cmd_frame);
    
    return bt_serial_send_frame(ctx, &cmd_frame);
}

int bt_upgrade_enable(bt_context_t *ctx)
{
    if (ctx->serial_fd < 0) return -1;
    
    comm_frame_t cmd_frame;
    
    bt_init_frame(&cmd_frame);
    cmd_frame.ctask = BT_CMD_BASE;
    cmd_frame.index = 0x0000;
    cmd_frame.abs_addr = BT_ADDR_UPGRADE;
    
    bt_frame_set_checksum(&cmd_frame);
    
    return bt_serial_send_frame(ctx, &cmd_frame);
}

int bt_serial_send_zero_frame(bt_context_t *ctx)
{
    if (ctx->serial_fd < 0) return -1;

    uint8_t zero_buffer[FRAME_SIZE] = {0};
    ssize_t bytes_written = write(ctx->serial_fd, zero_buffer, FRAME_SIZE);
    if (bytes_written != FRAME_SIZE) {
        BT_ERROR("发送零帧失败");
        return -1;
    }

    // 阻塞，直到数据全部发送完毕
    if (tcdrain(ctx->serial_fd) != 0) {
        BT_ERROR("数据发送同步失败");
        return -1;
    }

    return 0;
}

// 发送音频帧(被叫中调用)
int bt_process_send_audio(bt_context_t *ctx)
{
    if (!ctx) return -1;

    bt_audio_context_t *audio_ctx = (bt_audio_context_t *)ctx->audio_context;
    if (!audio_ctx) return -1;

    uint8_t alaw_data[AUDIO_FRAME_SIZE];
    uint16_t current_sequence;
    int frames_sent = 0;

    while (audio_ctx->audio_active) {
        if (bt_audio_buffer_pop(&audio_ctx->tx_buffer, alaw_data, &current_sequence) == 0) {
            //PTT事件最大存储6帧,自组网模式下最大存储18帧,统一使用1帧发送
            if(bt_send_audio(ctx, alaw_data, current_sequence) == 0){
                frames_sent++;
                BT_LOG("发送音频帧 %d (序列号: %d)", frames_sent, current_sequence);
                /*
                    if (frames_sent == MAX_CONTINUOUS_FRAMES) {
                        BT_LOG("已经连续发送36帧数");
                        //等待流控结束
                        
                    }

                    if (frames_sent % AUDIO_BUFFER_FRAMES == 0) {
                        BT_LOG("发送18帧完成,等待180ms");
                        usleep(180000);
                    }
                                                                */
            }else {
                BT_ERROR("发送音频帧失败");
                return -1;
            }
            
            // usleep(10000);
        }else {
            break;
        }
    }
    BT_LOG("发送音频帧结束");

    return 0;
}

// 处理接收到的音频帧(主叫中调用)
int bt_process_received_audio(bt_context_t *ctx, const comm_frame_t *frame)
{
    if(!ctx || !frame) return -1;
    
    bt_audio_context_t *audio_ctx = (bt_audio_context_t *)ctx->audio_context;
    if (!audio_ctx) return -1;

    // 检查序列号连续性
    uint16_t sequence = frame->index;
    if (audio_ctx->last_rx_sequence != 0){
        uint16_t expected_seq = audio_ctx->last_rx_sequence + 1;
        if (expected_seq != sequence) {
            BT_LOG("音频序列号不连续: 期望%d, 实际%d", expected_seq, sequence);
        }
    } 
    audio_ctx->last_rx_sequence = sequence;

    // 添加到接收缓冲区
    int ret = bt_audio_buffer_push(&audio_ctx->rx_buffer, frame->data, sequence);
    if (ret < 0) {
        BT_ERROR("添加到接收缓冲区失败");
        return -1;
    }

    // 调用回调函数
    if(audio_ctx->rx_callback){
        audio_ctx->rx_callback(frame->data, sequence, audio_ctx->callback_user_data);
    }

    return 0;
}

void bt_frame_print_hex(bt_context_t *ctx, const comm_frame_t *frame)
{
    if (!frame) return;
    
    const uint8_t *ptr = (const uint8_t *)frame;
    
    if (ctx->debug_mode) {
        printf("Frame content (hex): ");
        for (int i = 0; i < FRAME_SIZE; i++) {
            printf("%02X ", ptr[i]);
            if ((i + 1) % 16 == 0) printf("\n\t\t     ");
        }
        printf("\n");
    }
}

// PCM数据转换为A-law并添加到发送缓冲区(alsa应用play调用) 接收->pcm数据->手台->alaw数据->被叫指令->收发音频指令->蓝牙模块->蓝牙耳机播放
int bt_audio_send_pcm(bt_context_t *ctx, const short *pcm_data, size_t size)
{
    if(!ctx || !pcm_data || size > 80)  return -1;

    // 转换PCM到A-law
    uint8_t alaw_data[AUDIO_FRAME_SIZE];
    for (int i = 0; i < size; i++) {
        alaw_data[i] = pcm2pcma(pcm_data[i]);
    }

    // 添加到发送缓冲区
    bt_audio_context_t *audio_ctx = (bt_audio_context_t *)ctx->audio_context;
    int ret = bt_audio_buffer_push(&audio_ctx->tx_buffer, alaw_data, audio_ctx->tx_sequence++);
    if (ret < 0) {
        BT_ERROR("添加到发送缓冲区失败");
        return -1;
    }

    BT_LOG("PCM音频数据已转换并添加到发送缓冲区 (序列号: %d)", audio_ctx->tx_sequence - 1);

    return 0;
}

// 获取接收到的alaw音频数据(alsa应用capture调用) 主叫指令->MIC采集模拟音频->蓝牙模块->alaw数据->收发音频指令->手台->pcm数据->发送
int bt_audio_get_pcm(bt_context_t *ctx, short *pcm_data)
{
    if (!ctx || !pcm_data) return -1;

    bt_audio_context_t *audio_ctx = (bt_audio_context_t *)ctx->audio_context;
    if (!audio_ctx) return -1;

    // 从接收缓冲区获取A-law数据
    uint8_t alaw_data[AUDIO_FRAME_SIZE];
    uint16_t sequence;

    if (bt_audio_buffer_pop(&audio_ctx->rx_buffer, alaw_data, &sequence) < 0) {
        BT_LOG("接收缓冲区没有数据");
        return 0; // 没有数据
    }

    // 转换A-law到PCM
    for (int i = 0; i < AUDIO_FRAME_SIZE; i++) {
        pcm_data[i] = pcma2pcm(alaw_data[i]);
    }

    return AUDIO_FRAME_SIZE;
}

// 清理音频功能
void bt_audio_cleanup(bt_context_t *ctx)
{
    if (!ctx || !ctx->audio_context) return;
    
    bt_audio_context_t *audio_ctx = (bt_audio_context_t *)ctx->audio_context;
    
    // 停止音频线程
    audio_ctx->audio_active = false;
    if (audio_ctx->audio_thread) {
        pthread_join(audio_ctx->audio_thread, NULL);
    }
    
    // 清理缓冲区
    bt_destroy_audio_buffer(&audio_ctx->tx_buffer);
    bt_destroy_audio_buffer(&audio_ctx->rx_buffer);
    
    free(audio_ctx);
    ctx->audio_context = NULL;
    
    BT_LOG("音频功能已清理");
}


// 初始化音频功能
int bt_audio_init(bt_context_t *ctx, audio_rx_callback_t rx_callback, void *user_data)
{
    if (!ctx) return -1;
    
    bt_audio_context_t *audio_ctx = malloc(sizeof(bt_audio_context_t));
    if (!audio_ctx) {
        BT_ERROR("分配音频上下文内存失败");
        return -1;
    }

    memset(audio_ctx, 0, sizeof(bt_audio_context_t));

    // 初始化缓冲区
    if (bt_init_audio_buffer(&audio_ctx->tx_buffer) < 0 ||
        bt_init_audio_buffer(&audio_ctx->rx_buffer) < 0) {
        free(audio_ctx);
        return -1;
    }
    
    audio_ctx->tx_sequence = 1;
    audio_ctx->rx_callback = rx_callback;
    audio_ctx->callback_user_data = user_data;
    audio_ctx->audio_active = true;
    
    ctx->audio_context = audio_ctx;

    BT_LOG("音频功能初始化完成");

    g_receive_thread.ctx = ctx;
    g_receive_thread.thread_running = true;
    
    // 创建接收线程
    if (pthread_create(&g_receive_thread.receive_thread, NULL, receive_thread_func, &g_receive_thread) != 0) {
        BT_ERROR("创建接收线程失败");
        g_receive_thread.thread_running = false;
        return -1;
    }

    pthread_detach(g_receive_thread.receive_thread);

    BT_LOG("接收线程启动成功");
          
    return 0;
}

int set_bt_state(bool state)
{
    int fd;

    fd = open(BLUETOOTH_ON_PATH, O_RDWR);
    if (fd < 0) {
        perror("Fail to search BLUETOOTH_ON_PATH");
        return -1;
    }

    if (state) {
        write(fd, "255", 3);
    }else{
        write(fd, "0", 1);
    }

    printf("\r\nBT_ON:%s!\r\n\r\n", state ? "Enable":"Disable");
    close(fd);
    return 0;
}