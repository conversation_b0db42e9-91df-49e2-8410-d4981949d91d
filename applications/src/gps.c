/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <fcntl.h>
#include <time.h>
#include <unistd.h>
#include <termios.h>
#include <linux/rtc.h>
#include <sys/ioctl.h>
#include <errno.h>
//============================> Libraries Headers <============================

//============================> Project Headers <============================
#include "gps.h"

/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/


/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/



/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/



/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/

/******************************************************************************

                                Static Functions

 ******************************************************************************/

/******************************************************************************

                                Public Functions

 ******************************************************************************/
 int read_gps_data(int fd, void *buf, size_t nBytes) {
    int ret;
    int flags;
    int test_log;
    ssize_t total_read = 0;
    
    // 非阻塞读取
    while (total_read < nBytes) {
        ret = read(fd, buf + total_read, nBytes - total_read);
        if (ret < 0) {
            if (errno == EAGAIN || errno == EWOULDBLOCK) break; // 无更多数据
            perror("read error\n");
            return -1;
        } else if (ret == 0) {
            break; // EOF
        }
        total_read += ret;
        usleep(5000);
    }

    // 写入日志
    test_log = open("/etc/test_log", O_WRONLY | O_CREAT | O_APPEND, 0666);
    if (test_log < 0) {
        perror("Failed to open test_log");
        return -1;
    }
    write(test_log, buf, total_read);
    close(test_log);

    return total_read;
}

int write_gps_data(int fd, void *buf, size_t nBytes)
{
    int ret;

    ret = write(fd, buf, nBytes);
    if (ret < 0) {
        perror("Failed to write to GPS module");
        close(fd);
        return -1;
    }

    // 阻塞，直到数据全部发送完毕
    tcdrain(fd);

    return 0;
}

int gps_analyse(char *buf, GPRMC *gps_data)
{
    char date[7] = {0};    // DDMMYY 格式
    char utc_time[11] = {0}; // hhmmss.sss 格式
    double value;
    int degrees;
    double minutes;

    char *BDRMC = NULL;
    char *BDGGA = NULL;

    if(gps_data == NULL)
    {
        return -1;
    }

    if(strlen(buf) < 10)
    {
        return -1;
    }

    /* 如果buff字符串中包含字符"$BDRMC"则将$BDRMC的地址赋值给ptr */
    if(NULL == (BDRMC = strstr(buf,"$BDRMC")))
    {
        return -1;
    }

    if(NULL == (BDGGA = strstr(buf,"$BDGGA")))
    {
        return -1;
    }

    /* 手动分割字符串 */
    char *start = BDRMC;
    char *end;
    int index = 0;

    while ((end = strchr(start, ',')) != NULL) {
        // 提取当前字段
        char token[32] = {0};
        strncpy(token, start, end - start);

        // 根据字段索引解析数据
        switch (index) {
            case 1: // UTC Time
                strncpy(utc_time, token, sizeof(utc_time));
                break;
            case 2: // Status
                gps_data->pos_state = token[0];
                gps_data->rmt_gps_state |= (gps_data->pos_state == 'A' ? 1 : 0) << 4;
                break;
            case 3: // Latitude
                value = (strlen(token) > 0) ? atof(token) : 0.0;
                degrees = (int)(value / 100); 
                minutes = value - (degrees * 100);
                gps_data->latitude = degrees + (minutes / 60.0);
                break;
            case 4: // N/S
                gps_data->NS = (strlen(token) > 0) ? token[0] : '\0';
                gps_data->rmt_gps_state |= (gps_data->NS == 'N' ? 1 : 0) << 6;
                break;
            case 5: // Longitude
                value = (strlen(token) > 0) ? atof(token) : 0.0;
                degrees = (int)(value / 100); 
                minutes = value - (degrees * 100);
                gps_data->longitude = degrees + (minutes / 60.0);
                break;
            case 6: // E/W
                gps_data->WE = (strlen(token) > 0) ? token[0] : '\0';
                gps_data->rmt_gps_state |= (gps_data->WE == 'E' ? 1 : 0) << 5;
                break;
            case 7: // Speed
                gps_data->speed = (strlen(token) > 0) ? atof(token) : 0.0;
                break;
            case 8: // Direction
                gps_data->direction = (strlen(token) > 0) ? atof(token) : 0.0;
                break;
            case 9: // Date
                strncpy(date, token, sizeof(date));
                break;
            case 12: // Mode
                gps_data->mode = (strlen(token) > 0) ? token[0] : 'N';
                break;
        }
        // 移动到下一个字段
        start = end + 1;
        index++;
    }

    /* 手动分割字符串 */
    start = BDGGA;
    index = 0;

    while ((end = strchr(start, ',')) != NULL) {
        // 提取当前字段
        char token[32] = {0};
        strncpy(token, start, end - start);

        // 根据字段索引解析数据
        switch (index) {
            case 7: // HDOP
                gps_data->count = atoi(token);
                break;
            case 9: // MSL Altitude
                gps_data->altitude = (strlen(token) > 0) ? atof(token) : 0.0;
                break;
        }

        // 移动到下一个字段
        start = end + 1;
        index++;
    }

    sscanf(date, "%2d%2d%2d", &gps_data->time.tm_mday, &gps_data->time.tm_mon, &gps_data->time.tm_year);    

    sscanf(utc_time, "%2d%2d%2d", &gps_data->time.tm_hour, &gps_data->time.tm_min, &gps_data->time.tm_sec);

    gps_data->time.tm_year += 2000;

    return 0;
}

int print_gps(GPRMC *gps_data)
{
    int fd;
    char buffer[1024];
    int offset = 0;
    
    offset = snprintf(buffer, sizeof(buffer),
        "===========================================================\n"
        "==   GPS state bit : %c  [A:有效状态 V:无效状态]\n"
        "==   GPS mode  bit : %c  [A:自主定位 D:差分定位]\n"
        "==   Date : %04d-%02d-%02d\n"
        "==   Time : %02d:%02d:%02d\n"
        "==   latitude :%c %.7f\n"
        "==   longtitude :%c %.7f\n"
        "==   altitude : %.3f  m\n"
        "==   satellite : %d\n"
        "==   speed : %.3f  m/s\n"
        "==\n"
        "===========================================================\n",
        gps_data->pos_state,
        gps_data->mode,
        gps_data->time.tm_year,
        gps_data->time.tm_mon,
        gps_data->time.tm_mday,
        gps_data->time.tm_hour,
        gps_data->time.tm_min, 
        gps_data->time.tm_sec,
        gps_data->NS,
        gps_data->latitude,
        gps_data->WE,
        gps_data->longitude,
        gps_data->altitude,
        gps_data->count,
        gps_data->speed);

    if (write(STDOUT_FILENO, buffer, offset) < 0) {
        return -1;
    }

    return 0;
}

int set_gps_power(int param)
{
    int fd;

    fd = open(GPS_ON_PATH, O_RDWR);
    if(fd < 0)
    {
        perror("Open gps_on fail");
        return -1;
    }

    if(0 == param)
    {
        write(fd, "0", 1);
    }
    else
    {
        write(fd, "255", 3);
    }
    printf("\r\nGPS_ON:%s!\r\n\r\n", param == 1 ? "Enable":"Disable");

    close(fd);

    return 0;
}

int save_gps_time(int fd, struct tm *gps_time)
{   
    int ret;
    time_t t;
    struct tm *tm_info;
    struct rtc_time rtc_tm;
    struct timespec ts;
    char buffer[32];

    /* set rtc time */
    gps_time->tm_year = gps_time->tm_year - 1900;

	rtc_tm.tm_sec  = gps_time->tm_sec;
	rtc_tm.tm_min  = gps_time->tm_min;
	rtc_tm.tm_hour = gps_time->tm_hour;
	rtc_tm.tm_mday = gps_time->tm_mday;
	rtc_tm.tm_mon  = gps_time->tm_mon;
	rtc_tm.tm_year = gps_time->tm_year;
	rtc_tm.tm_wday = gps_time->tm_wday;
	rtc_tm.tm_yday = gps_time->tm_yday;
	rtc_tm.tm_isdst = gps_time->tm_isdst;

    ret = ioctl(fd, RTC_SET_TIME, &rtc_tm);
    if (ret == -1) {
        perror("ioctl RTC_SET_TIME error");
        return -1;
    }

    /* set system time */
    t = mktime(gps_time);
    ts.tv_sec = t;
    ts.tv_nsec = 0;
    if (clock_settime(CLOCK_REALTIME, &ts) != 0) {
        perror("Failed to set high precision system time");
        return -1;
    }
    
    /* save UTC time */
    time(&t);
    tm_info = localtime(&t);
    strftime(buffer, 32, "%Y-%m-%d %H:%M:%S\n", tm_info);

    fd = open("/etc/rtc_time_file", O_WRONLY | O_CREAT | O_TRUNC, 0666);
    if (fd < 0) {
        perror("Failed to open rtc_time_file");
        return -1;
    }

    if (write(fd, buffer, strlen(buffer)) < 0) {
        perror("Failed to write to rtc_time_file");
        close(fd);
        return -1;
    }

    close(fd);

    return 0;
}

