include ./Rules.make 

CURRENT_PATH = $(shell pwd)/src
RELEASE_PATH = $(shell pwd)/release
INC_PATH = $(shell pwd)/release/include
# $(info $(CURRENT_PATH))

# 库名称
LIB_NAME = libp3
DYNAMIC_LIB = $(LIB_NAME).so
STATIC_LIB = $(LIB_NAME).a

# 源文件
SRCS = $(wildcard $(CURRENT_PATH)/*.c)
SRCS := $(filter-out $(CURRENT_PATH)/flash_erase.c $(CURRENT_PATH)/mtd_test.c $(CURRENT_PATH)/loop.c $(CURRENT_PATH)/bt_test.c $(CURRENT_PATH)/gps_test.c $(CURRENT_PATH)/timepps.c $(CURRENT_PATH)/rtc.c $(CURRENT_PATH)/ppswatch.c $(CURRENT_PATH)/ubx_test.c $(CURRENT_PATH)/calibration_test.c $(CURRENT_PATH)/sc5883lp_test.c, $(SRCS))

# 目标文件
OBJS = $(SRCS:.c=.o)

# 编译选项
CFLAGS += -fPIC -I$(INC_PATH) -lm -O2
LDFLAGS := -lpthread -lm

# 生成动态库
$(DYNAMIC_LIB): $(OBJS)
	$(CC) -shared $(CFLAGS) -o $(RELEASE_PATH)/$(DYNAMIC_LIB) $(OBJS)
	$(STRIP) $(RELEASE_PATH)/$(DYNAMIC_LIB)
	@chmod a+x $(RELEASE_PATH)/$(DYNAMIC_LIB)

# 生成静态库
$(STATIC_LIB): $(OBJS)
	$(AR) -rcs $(RELEASE_PATH)/$(STATIC_LIB) $(OBJS)
	$(STRIP) $(RELEASE_PATH)/$(STATIC_LIB)
	@chmod a+x $(RELEASE_PATH)/$(STATIC_LIB)

# 生成所有库
libs: $(DYNAMIC_LIB) $(STATIC_LIB)

gps_test: $(CURRENT_PATH)/gps_test.o
	$(CC) -o $(RELEASE_PATH)/$@ $(CURRENT_PATH)/$@.c -L$(RELEASE_PATH) -lp3 -I$(INC_PATH)
	$(STRIP) $(RELEASE_PATH)/$@
	@chmod a+x $(RELEASE_PATH)/$@

loop: $(CURRENT_PATH)/loop.o
	$(CC) -o $(RELEASE_PATH)/$@ $(CURRENT_PATH)/$@.c -L$(RELEASE_PATH) -lp3 -I$(INC_PATH)
	$(STRIP) $(RELEASE_PATH)/$@
	@chmod a+x $(RELEASE_PATH)/$@

bt_test: $(CURRENT_PATH)/bt_test.o
	$(CC) -g -o $(RELEASE_PATH)/$@ $(CURRENT_PATH)/$@.c -L$(RELEASE_PATH) -lp3 -I$(INC_PATH) -lm
	$(STRIP) $(RELEASE_PATH)/$@
	@chmod a+x $(RELEASE_PATH)/$@

mtd_test: $(CURRENT_PATH)/mtd_test.o
	$(CC) -o $(RELEASE_PATH)/$@ $(CURRENT_PATH)/$@.c
	$(STRIP) $(RELEASE_PATH)/$@
	@chmod a+x $(RELEASE_PATH)/$@

flash_erase: $(CURRENT_PATH)/flash_erase.o
	$(CC) -o $(RELEASE_PATH)/$@ $(CURRENT_PATH)/$@.c
	$(STRIP) $(RELEASE_PATH)/$@
	@chmod a+x $(RELEASE_PATH)/$@

ubx_test: $(CURRENT_PATH)/ubx_test.o
	$(CC) -o $(RELEASE_PATH)/$@ $(CURRENT_PATH)/$@.c -L$(RELEASE_PATH) -lp3 -I$(INC_PATH)
	$(STRIP) $(RELEASE_PATH)/$@
	@chmod a+x $(RELEASE_PATH)/$@

timepps: $(CURRENT_PATH)/timepps.o
	$(CC) -o $(RELEASE_PATH)/$@ $(CURRENT_PATH)/$@.c -I$(INC_PATH)
	$(STRIP) $(RELEASE_PATH)/$@
	@chmod a+x $(RELEASE_PATH)/$@

rtc: $(CURRENT_PATH)/rtc.o
	$(CC) -o $(RELEASE_PATH)/$@ $(CURRENT_PATH)/$@.c 
	$(STRIP) $(RELEASE_PATH)/$@
	@chmod a+x $(RELEASE_PATH)/$@

ppswatch: $(CURRENT_PATH)/ppswatch.o
	$(CC) -o $(RELEASE_PATH)/$@ $(CURRENT_PATH)/$@.c -I$(INC_PATH) -lm
	$(STRIP) $(RELEASE_PATH)/$@
	@chmod a+x $(RELEASE_PATH)/$@

calibration_test: $(CURRENT_PATH)/calibration_test.o
	$(CC) -o $(RELEASE_PATH)/$@ $(CURRENT_PATH)/$@.c -L$(RELEASE_PATH) -lp3 -I$(INC_PATH) -lm
	$(STRIP) $(RELEASE_PATH)/$@
	@chmod a+x $(RELEASE_PATH)/$@

sc5883lp_test: $(CURRENT_PATH)/sc5883lp_test.o
	$(CC) -o $(RELEASE_PATH)/$@ $(CURRENT_PATH)/$@.c -L$(RELEASE_PATH) -lp3 -I$(INC_PATH) -lm
	$(STRIP) $(RELEASE_PATH)/$@
	@chmod a+x $(RELEASE_PATH)/$@

prebuild:
	@mkdir -p $(RELEASE_PATH)

all: prebuild libs flash_erase mtd_test loop bt_test gps_test timepps rtc ppswatch ubx_test calibration_test sc5883lp_test
	@rm $(CURRENT_PATH)/*.o
	@echo install victel rf commands done!

.PHONY: clean libs
clean:
	@rm $(CURRENT_PATH)/*.o
	@rm -f $(RELEASE_PATH)/$(DYNAMIC_LIB) $(RELEASE_PATH)/$(STATIC_LIB)

install:
	@echo install victel rf commands done!

