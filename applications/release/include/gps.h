#ifndef GPS_H
#define GPS_H

#ifdef __cplusplus
extern "C" {
#endif

/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <stdint.h>
//============================> Libraries Headers <============================

//============================> Project Headers <============================
#include "serial.h"
#include <time.h>

/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/
#define GPS_PATH           SERIAL_PATH(S3)
#define GPS_ON_PATH        "/sys/class/leds/gps_on/brightness"
#define GPS_TIME_PATH        "/etc/rtc_time_file"

/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/



/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/
typedef struct __gprmc__
{
   struct tm time;/* 时间 */
   unsigned int count; /* 星数 */
   float altitude; /* 高度 */
   char pos_state;/* gps状态位 */
   char NS;
   char WE;
   double latitude;/* 纬度 */
   double longitude;/* 经度 */
   float speed; /* 速度 */
   float direction;/*航向 */
   float declination; /* 磁偏角 */
   char dd;
   char mode;/* GPS模式位 */
   uint8_t rmt_gps_state;
}GPRMC;

/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/



/******************************************************************************

                                Public Functions

 ******************************************************************************/

/**
 * @brief 读取GPS原始数据
 * 
 * @param fd GPS串口文件描述符
 * @param buf 读取到的GPS原始数据
 * @param nBytes 需要读取的字节数
 * @return 0:成功 -1:失败
 */
int read_gps_data(int fd, void *buf, size_t nBytes);

/**
 * @brief 写入GPS数据
 * 
 * @param fd GPS串口文件描述符
 * @param buf 写入的GPS数据
 * @param nBytes 需要写入的字节数
 * @return 0:成功 -1:失败
 */
int write_gps_data(int fd, void *buf, size_t nBytes);

/**
 * @brief 解析GPS数据
 * 
 * @param buf GPS原始数据
 * @param gps_data 解析后的GPS数据
 * @return 0:成功 -1:失败
 */
int gps_analyse(char *buf,GPRMC *gps_data);

/**
 * @brief 打印GPS数据
 * 
 * @param gps_data 解析后的GPS数据
 * @return 0:成功 -1:失败
 */
int print_gps(GPRMC *gps_data);

/**
 * @brief 控制GPS电源开关
 * 
 * @param param 1:打开 0:关闭
 * @return 0:成功 -1:失败
 */
int set_gps_power(int param);

/**
 * @brief 保存GPS时间
 * 
 * @param fd RTC文件描述符
 * @param gps_time GPS时间
 * @return 0:成功 -1:失败
 */
int save_gps_time(int fd, struct tm *gps_time);


#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
