#ifndef MULTIKEY_H
#define MULTIKEY_H

#ifdef __cplusplus
extern "C" {
#endif

/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <stdio.h>
//============================> Libraries Headers <============================

//============================> Project Headers <============================


/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/
#define fd_count 3
//============================> File Path <============================//
#define KEY_PATH(index)   get_key_path(index)

/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/



/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/



/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/
static inline const char* get_key_path(int index) {
    static char path[64];
    snprintf(path, sizeof(path), "/dev/input/event%d", index);
    return path;
}


/******************************************************************************

                                Public Functions

 ******************************************************************************/

/**
 * @brief 按键初始化
 * 
 * @param fd[fd_count] 按键设备文件描述符
 * @return epoll文件描述符
 *
 */
int keyboard_init(int fd[fd_count]);

/**
 * @brief 读取按键值
 * 
 * @param epoll_fd epoll文件描述符
 * @param buf 按键值
 * @return 0:成功 -1:失败
 *
 */
int keyboard_read(int epoll_fd, void *buf);


#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
