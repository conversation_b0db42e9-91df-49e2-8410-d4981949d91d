#ifndef PCM_H
#define PCM_H

#ifdef __cplusplus
extern "C" {
#endif

/******************************************************************************

                                Includes

 ******************************************************************************/
//============================> System Headers <============================

//============================> Libraries Headers <============================

//============================> Project Headers <============================

/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/

/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/



/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/



/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/

/******************************************************************************

                                Static Functions

 ******************************************************************************/
/**
 * @brief  查找表位扫描函数，根据给定的 PCM 值返回对应的位数
 * @param  pcm 输入的 PCM 值
 * @return 返回对应的位数
 */
static short search_table_bitscan(short pcm);

/******************************************************************************

                                Public Functions

 ******************************************************************************/

/**
 * @brief  将 A-Law 编码转换为 PCM 编码
 * @param  alaw 输入的 A-Law 编码
 * @return 返回对应的 PCM 编码
 */
short pcma2pcm(unsigned char alaw);

/**
 * @brief  将 PCM 编码转换为 A-Law 编码
 * @param  pcm 输入的 PCM 编码
 * @return 返回对应的 A-Law 编码
 */
unsigned char pcm2pcma(short pcm);


#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
