#ifndef LCD_H
#define LCD_H

#ifdef __cplusplus
extern "C" {
#endif

/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <stdbool.h>
#include <stdint.h>

//============================> Libraries Headers <============================

//============================> Project Headers <============================

/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/
 #define LCD_PATH(type) "/dev/" #type
 #ifndef GC9307_PATH
 #define GC9307_PATH LCD_PATH(gc9307)
 #endif
 #ifndef FBDEV_PATH
 #define FBDEV_PATH LCD_PATH(fb0)
 #endif
/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/
typedef enum
{
    LCD_DRV_ROTATE_0 = 0,
    LCD_DRV_ROTATE_90,
    LCD_DRV_ROTATE_180,
    LCD_DRV_ROTATE_270
} lcd_drv_rotation_t;

/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/

/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/

/******************************************************************************

                                Public Functions

 ******************************************************************************/
/**
 * @brief 设置LCD屏幕的旋转角度
 *
 * @param rotation 旋转角度
 */
int lcd_drv_set_rotation(lcd_drv_rotation_t rotation);

/**
 * @brief 切换LCD屏幕的睡眠状态
 *
 * @param state true为睡眠，false为唤醒
 */
void lcd_drv_toggle_sleep(bool state);

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

