#ifndef RTCTIME_H
#define RTCTIME_H

#ifdef __cplusplus
extern "C" {
#endif

/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <stdbool.h>
#include <stdint.h>

//============================> Libraries Headers <============================

//============================> Project Headers <============================

/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/
 #define RTC_PATH "/dev/rtc0"

/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/


/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/
 typedef struct s_rtc_time
 {
     uint8_t hour;
     uint8_t minute;
     uint8_t second;
 } rtc_time_t;
/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/

/******************************************************************************

                                Public Functions

 ******************************************************************************/
/**
 * @brief 读取RTC时间
 *
 * @param fd RTC设备文件描述符  
 * @param buf 时间
 * @return 0 成功，-1 失败
 */
int read_rtc_time(int fd, void *buf);

/**
 * @brief 设置RTC时间
 *
 * @param fd RTC设备文件描述符
 * @param buf 时间
 * @return 0 成功，-1 失败
 */
int set_rtc_time(int fd, void *buf);


#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

