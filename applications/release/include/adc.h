#ifndef ADC_H
#define ADC_H

#ifdef __cplusplus
extern "C" {
#endif

/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <stdio.h>
#include <linux/types.h>
//============================> Libraries Headers <============================

//============================> Project Headers <============================


/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/

//============================> File Path <============================//
#define ADC_PATH(chan, type)   get_adc_path(chan, type)


/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/



/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/



/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/

extern const double ADC_SCALE;

static inline const char* get_adc_path(int chan, const char *type) {
    static char path[64];
    snprintf(path, sizeof(path), "/sys/bus/iio/devices/iio:device0/in_voltage%d_%s", chan, type);
    return path;
}

/******************************************************************************

                                Public Functions

 ******************************************************************************/

/**
 * @brief 读取指定通道的ADC原始值
 * 
 * @param chan 通道号
 * @return -1 失败 >=0 原始值
 */
int read_adc_raw(int chan);

/**
 * @brief 读取指定通道的ADC电压值
 * 
 * @param chan 通道号
 * @return -1 失败 >=0 电压值(mV)
 */
double read_adc_data(int chan);

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
