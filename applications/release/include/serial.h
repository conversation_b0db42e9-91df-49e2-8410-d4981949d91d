#ifndef SERIAL_H
#define SERIAL_H

#ifdef __cplusplus
extern "C" {
#endif

/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================

//============================> Libraries Headers <============================

//============================> Project Headers <============================


/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/
 //============================> File Path <============================//
#define SERIAL_PATH(index)   "/dev/tty" #index

/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/



/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/



/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/



/******************************************************************************

                                Public Functions

 ******************************************************************************/

/**
 * @brief: 设置串口参数
 * @param fd 文件描述符
 * @param nSpeed 波特率
 * @param nBits 数据位
 * @param nStop 停止位
 * @param nPartiy 校验位
 * @return -1: 失败, 0: 成功
 */
int set_opt(int fd, int nSpeed, int nBits, int nStop, char nPartiy);

/**
 * @brief：设置串口波特率
 * @param fd 文件描述符
 * @param nSpeed 波特率
 * @return -1: 失败, 0: 成功
 */
int set_baud(int fd, int nSpeed);

/**
 * @brief: 打开串口
 * @param com 串口号
 * @return -1: 失败, > 0: 文件描述符
 */
int open_port(char *com);

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
