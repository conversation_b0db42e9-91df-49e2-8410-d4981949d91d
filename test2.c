#include <stdio.h>

typedef struct {
    int d;
} b;

typedef struct {
    int e;
} c;

typedef struct {
    b test1;
    c test2;
} a;

void test(b *test3) {
    a *test4 = (a *)test3;
    c *test5 = &(test4->test2);
    printf("Address of test3 (b*): %p\n", (void*)test3);
    printf("Address of test4 (a*): %p\n", (void*)test4);
    printf("Address of test5 (c*): %p\n", (void*)test5);
}

int main() {
    a test6;
    printf("Address of test6: %p\n", (void*)&test6);
    printf("Address of test6.test1: %p\n", (void*)&test6.test1);
    test(&test6.test1);
    return 0;
}